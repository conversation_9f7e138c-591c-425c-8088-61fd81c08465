{"name": "react-ecommerce", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "dev_server": "vite --host", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "dev_fashion": "node generate-env.cjs ./config/fashion.json && vite", "build_fashion": "node generate-env.cjs ./config/fashion.json && tsc -b && vite build", "dev_electronics": "node generate-env.cjs ./config/electronics.json && vite", "build_electronics": "node generate-env.cjs ./config/electronics.json && tsc -b && vite build", "dev_grocery": "node generate-env.cjs ./config/grocery.json && vite", "build_grocery": "node generate-env.cjs ./config/grocery.json && tsc -b && vite build"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-regular-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@lottiefiles/dotlottie-react": "^0.13.5", "@react-oauth/google": "^0.12.1", "@reduxjs/toolkit": "^2.5.1", "@tanstack/query-sync-storage-persister": "^5.68.0", "@tanstack/react-query": "^5.68.0", "@tanstack/react-query-persist-client": "^5.68.0", "blurhash": "^2.0.5", "bootstrap": "^5.3.2", "drift-zoom": "^1.5.1", "firebase": "^11.6.1", "fuse.js": "^7.1.0", "loadable-image": "^3.2.6", "photoswipe": "^5.4.4", "react": "^18.3.1", "react-blurhash": "^0.3.0", "react-dom": "^18.3.1", "react-intersection-observer": "^9.15.1", "react-range-slider-input": "^3.0.7", "react-redux": "^9.2.0", "react-router-dom": "^7.1.5", "react-shimmer": "^3.2.0", "sass": "^1.82.0", "swiper": "^11.1.15", "transitions-kit": "^1.2.4"}, "devDependencies": {"@eslint/js": "^9.17.0", "@types/bootstrap": "^5.2.10", "@types/drift-zoom": "^1.5.2", "@types/node": "^22.13.1", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.17.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "terser": "^5.38.1", "typescript": "~5.6.2", "typescript-eslint": "^8.18.2", "vite": "^6.0.5", "vite-plugin-javascript-obfuscator": "^3.1.0"}}