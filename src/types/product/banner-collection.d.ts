export type BannerCollection = {
    imgSrc: string;
    width: number,
    height: number,
    title: string,
    titleRedirectUrl: string,
    blurHash: string,
    description: string,
    action: string;
    actionUrl: string,
    defaultStyle: boolean,
}

export type BannerCountdown = {
    title: string,
    sub_title: string,
    action_text: string,
    cta_url: string,
    img_src: string;
    blur_hash: string,
    width: number,
    height: number,
    ends_on: string,
}

export type Testimonial = {
    id: number,
    imgSrc: string,
    blurHash: string,
    imgWidth: number,
    imgHeight: number
    title: string,
    quote: string,
    author: string,
    avatar: string,
    price: number,
    rating: number,
}