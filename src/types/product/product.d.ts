export interface ProductImage {
    blurhash?: string;
    created_at?: string;
    height: number;
    image_url: string;
    product_id: string;
    updated_at?: string;
    width: number;
}

export interface ProductVideo {
    blurhash?: string;
    created_at?: string;
    height: number;
    url: string;
    thumbnail: string;
    isYoutube?: boolean;
    product_id: number;
    updated_at?: string;
    width: number;
}

export interface ProductVariant {
    additional_info?: string;
    created_at: string;
    currency?: string;
    description?: string;
    id: string;
    mrp?: number;
    options: Record<string, string | undefined>;
    policy?: {
        return: string;//Todo to number,
        shipping: string,
        warranty: string,
    };
    position?: number;
    product_id: number;
    images: ProductImage[];
    product_video_url?: ProductVideo[] | string[];
    purchase_price?: number;
    selling_price: number;
    slug: string,
    spec?: Record<string?, string | undefined>;
    status?: string;
    stock: number;
    tags?: string[];
    technical_details?: string;
    updated_at: string;
    name: string;
    weight?: number;
    weight_unit?: number;
    hotSale?: boolean;
    isOnSale?: boolean;
}

export interface Product {
    brand: string;
    brand_id: number;
    main_category: string,
    category: string;
    category_id: number;
    created_at: string;
    id: number;
    name: string;
    number_of_rating: number;
    option_keys: string[];
    rating: number;
    seller: string;
    seller_address: string | null;
    seller_id: number;
    short_description: string;
    sub_category: string;
    sub_category_id: number;
    updated_at: string;
    variants_minimal: VariantDetails[];

}

export interface VariantDetails {
    id: string;
    options: Record<string, string>;
    images: ProductImage[];
    stock: number;
    name: string;
    slug: string;
}

export interface ProductResponse {
    product: Product;
    selected_id: string;
    variant_details: ProductVariant;
}
