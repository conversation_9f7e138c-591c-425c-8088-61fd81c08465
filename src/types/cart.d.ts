import { ProductImage } from './product/product'

export interface CartDetails {
    coupon?: Coupon
    order_summary: OrderSummary
}

export interface Coupon {
    id: number
    is_applied: boolean
    message: string
    name: any
}

export interface OrderSummary {
    coupon_discount: number
    discount: number
    items: Item[]
    shipping: number
    subtotal: number
    total: number
}

export interface Item {
    id: string,
    mrp: number
    name: string
    options: Record<string, string | undefined>
    product_total: number
    quantity: number
    selling_price: number
    slug: string
    images: ProductImage[]
}