import { Timestamp } from "firebase/firestore";
import { ProductImage } from "./product/product";
export type OrderStatus = 'Pending' | 'Processing' | 'Shipped' | 'Delivered' | 'Cancelled' | 'Completed';
export type PaymentStatus = 'Pending' | 'Failed' | 'Completed' | 'Refunded';
export type PaymentMethod = 'COD' | 'Card' | 'UPI' | 'NetBanking' | 'Wallet';

export interface Item {
    id: string,
    mrp: number
    name: string
    options: Record<string, string | undefined>
    product_total: number
    quantity: number
    selling_price: number
    slug: string
    image: ProductImage
}

export interface OrderSummary {
    subtotal: number;
    shipping_fee: number;
    tax: number;
    discount: number;
    coupon_discount: number;
    total: number;
}

export interface PaymentDetails {
    payment_id?: string;
    payment_method: PaymentMethod;
    payment_status: PaymentStatus;
    paid_amount?: number;
    payment_date?: string;
    transaction_id?: string;
}

export interface OrderTrackingDetails {
    tracking_number?: string;
    courier_name?: string;
    estimated_delivery_date?: string;
    current_status: string;
    location?: string;
    tracking_url?: string;
    tracking_history: {
        status: string;
        timestamp: string;
        location?: string;
        description?: string;
    }[];
}

export interface Order {
    id: string;
    order_number: string;
    user_id: number | string;
    created_at: string | Timestamp;
    updated_at: string | Timestamp;
    items: Item[];
    shipping_address: UserAddress;
    billing_address: UserAddress;
    order_summary: OrderSummary;
    payment_details: PaymentDetails;
    tracking_details?: OrderTrackingDetails;
    status: OrderStatus;
    expected_delivery_date?: string;
    delivered_date?: string;
    cancelled_date?: string;
    cancellation_reason?: string;
    refund_status?: 'Pending' | 'Processing' | 'Completed';
    notes?: string;
    invoice_url?: string;
}