import { Slide } from "@/types/product/slide"
import { Category } from "@/types/product/collection"
import { CartDetails } from "./cart"
import { ProductVariant } from "./product/product"
import { User } from "./user"
import { AlertModalProps } from "@/components/modals/alert-modal"

export type SlideConfig = {
    endPoint: string,
}

export type CollectionConfig = {
    endPoint: string,
    title: string,
    viewMore: string,
}

export type ProductConfig = {
    title: string,
    endPoint: string,
}

export type BannerConfig = {
    endPoint: string,
}

export type TestimonialConfig = {
    endPoint: string,
    title: string,
    subTitle: string,
}

export type BlogConfig = {
    endPoint: string,
    title: string,
    subTitle: string,
}

export type PolicyItem = {
    id: number,
    icon: string,
    title: string,
    description: string,
}

export type HomeConfig = {
    policyItems: PolicyItems[],
}

export interface Brand {
    id: number,
    name: string,
}

export interface FilterModel {
    brands: Brand[],
    max_price: number,
    options: Record<string, string[]>
}

export interface DataContextType {
    navData?: Category[],
    isLoading: boolean,
    setIsLoading: React.Dispatch<React.SetStateAction<boolean>>,
    // Auth types
    authLoading: boolean,
    isAuthenticated: boolean,
    user: null | User,
    login: (token: string, userData: any) => void
    logout: () => void,

    cartLoading: boolean,
    cartDetails: CartDetails | undefined,
    isAddedToCartProducts: (id: string) => boolean,
    addToCart: (product: string, qty: number) => void,
    cartProducts: any[],
    setCartProducts: React.Dispatch<React.SetStateAction<any[]>>,
    totalPrice: number,
    quickViewItem: ProductVariant | undefined
    wishlist: ProductVariant[] | undefined,
    updateWishlist: (product: ProductVariant) => void,
    isAddedtoWishlist: (id: string) => boolean,
    setQuickViewItem: React.Dispatch<React.SetStateAction<ProductVariant | undefined>>
    quickAddItem: number,
    setQuickAddItem: React.Dispatch<React.SetStateAction<number>>

    compareItem: ProductVariant[],
    setCompareItem: React.Dispatch<React.SetStateAction<ProductVariant[]>>,

    address: UserAddress | undefined,
    editAddress: (address: UserAddress | undefined) => void,

    addToCompareItem: (product: ProductVariant) => void,
    isAddedtoCompareItem: (id: string) => boolean,
    removeFromCompareItem: (id: string) => void,

    showAlert: (children: React.ReactNode, size?: "sm" | "lg" | undefined, dismissible?: boolean) => void,
    closeAlert: () => void,
    alertModel: AlertModalProps | null,
}