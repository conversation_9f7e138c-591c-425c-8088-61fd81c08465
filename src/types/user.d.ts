export interface UserResponse {
    access_token: string
    data: Data
    refresh_token: string
    success: boolean
}

export interface Data {
    exp: string
    iat: string
    refresh_expiry: string
    user: User
}

export interface User {
    email: string
    is_email_verified: boolean
    first_name: string
    id: number | string
    image_url?: string
    is_active: boolean
    last_name?: string
    primary_phone?: string
}

interface UserAddress {
    id: number | string;
    name: string;
    contact_number?: string;
    address_line_1: string;
    address_line_2: string;
    landmark?: string;
    city: string;
    state: string;
    pin_code: string;
    address_type?: 'house' | 'apartment' | 'business' | 'other';
    comments?: string;
    is_default?: boolean;
    latitude?: number;
    longitude?: number;
    country?: string;
    isDefault?: boolean;
}
