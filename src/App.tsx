import React from 'react'
import { Outlet, Route, Routes } from "react-router"
import { RequireAuth } from './components/common/require-auth'
import { useSyncCacheManager } from './utils/SyncCacheManager'

const Page404 = React.lazy(() => import('./pages/others/page404'))
const Login = React.lazy(() => import('./pages/auth/login'))
const Register = React.lazy(() => import('./pages/auth/register'))
const Layout = React.lazy(() => import('./layout/layout'))
const HomeDefault = React.lazy(() => import('./pages/home/<USER>'))
const ProductDefaultGrid = React.lazy(() => import('./pages/product-list/product-default-grid'))
const ProductDetails = React.lazy(() => import('./pages/product/product-details'))
const Cart = React.lazy(() => import('./pages/checkout/cart'))
const WishlistPage = React.lazy(() => import('./pages/checkout/wishlist-page'))
const Checkout = React.lazy(() => import('./pages/checkout/checkout'))
const Categories = React.lazy(() => import('./pages/categories'))
const AllProductsGrid = React.lazy(() => import('./pages/product-list/all-products-grid'))
const AboutUs = React.lazy(() => import('./pages/others/about-us'))
const ContactUs = React.lazy(() => import('./pages/others/contact-us'))
const MyAccount = React.lazy(() => import('./pages/my-account'))
const Profile = React.lazy(() => import('./pages/profile'))
const AddressPage = React.lazy(() => import('./pages/address'))
const OrderList = React.lazy(() => import('./pages/order/order-list'))
const OrderDetails = React.lazy(() => import('./pages/order/order-details'))
const Compare = React.lazy(() => import('./pages/product/compare'))
const Shipping = React.lazy(() => import('./pages/others/shipping'))
const TermsOfUse = React.lazy(() => import('./pages/others/terms-of-use'))
const PrivacyPolicy = React.lazy(() => import('./pages/others/privacy-policy'))
const ReturnRefund = React.lazy(() => import('./pages/others/return-refund'))
const FaqPage = React.lazy(() => import('./pages/others/faq-page'))

const SyncCacheInitializer = ({ children }: { children: React.ReactNode }) => {
  useSyncCacheManager(); // Initialize sync cache manager
  return <>{children}</>;
};

function App() {
  return (
    <Layout>
      <SyncCacheInitializer>
        <Routes>
          <Route >
            {/* Public routes */}
            <Route path='/' element={<HomeDefault />} />
            <Route path='/login' element={<Login />} />
            <Route path='/register' element={<Register />} />
            <Route path='/categories' element={<Categories />} />
            <Route path='/products' element={<AllProductsGrid />} />
            <Route path='/:slug' element={<ProductDefaultGrid showCategory={true} />} />
            <Route path='/sub/:slug' element={<ProductDefaultGrid />} />
            <Route path='/product-details/:slug' element={<ProductDetails />} />
            <Route path='/wish-list' element={<WishlistPage />} />
            <Route path='/compare-products' element={<Compare />} />
            <Route path='/about-us' element={<AboutUs />} />
            <Route path='/contact-us' element={<ContactUs />} />
            <Route path='/shipping' element={<Shipping />} />
            <Route path='/term-of-use' element={<TermsOfUse />} />
            <Route path='/privacy-policy' element={<PrivacyPolicy />} />
            <Route path='/return-refund' element={<ReturnRefund />} />
            <Route path='/FAQs' element={<FaqPage />} />


            {/* Protected routes */}
            <Route element={<RequireAuth><Outlet /></RequireAuth>}>

              <Route path='/my-account' element={<MyAccount />} />
              <Route path='/profile' element={<Profile />} />
              <Route path='/address' element={<AddressPage />} />
              <Route path='/orders' element={<OrderList />} />
              <Route path='/order/:id' element={<OrderDetails />} />
              <Route path='/cart' element={<Cart />} />
              <Route path='/checkout' element={<Checkout />} />
            </Route>


            {/* 404 route */}
            <Route path='*' element={<Page404 />} />

          </Route>
        </Routes>
      </SyncCacheInitializer>
    </Layout>
  )
}

export default App
