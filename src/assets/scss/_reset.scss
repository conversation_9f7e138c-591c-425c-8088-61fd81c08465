@use "abstracts/mixin";
@use "abstracts/variable";

html,
body,
div,
span,
applet,
object,
iframe,
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote,
pre,
a,
abbr,
acronym,
address,
big,
cite,
code,
del,
dfn,
em,
img,
ins,
kbd,
q,
s,
samp,
small,
strike,
strong,
sub,
sup,
tt,
var,
b,
u,
i,
center,
dl,
dt,
dd,
ol,
ul,
li,
fieldset,
form,
label,
legend,
table,
caption,
tbody,
tfoot,
thead,
tr,
th,
td,
article,
aside,
canvas,
details,
embed,
figure,
figcaption,
footer,
header,
hgroup,
menu,
nav,
output,
ruby,
section,
summary,
time,
mark,
audio,
video {
    margin: 0;
    padding: 0;
    border: 0;
    outline: 0;
    font-size: 100%;
    font: inherit;
    vertical-align: baseline;
    font-family: inherit;
    font-size: 100%;
    font-style: inherit;
    font-weight: inherit;
}

article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section {
    display: block;
}

/* Elements
-------------------------------------------------------------- */

html {
    margin-right: 0 !important;
    scroll-behavior: smooth;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: variable.$font-main;
    font-size: 16px;
    line-height: 26px;
    font-weight: 400;
    color: var(--main);
    background-color: var(--white);
}

img {
    max-width: 100%;
    height: auto;
    transform: scale(1);
    vertical-align: middle;
    -ms-interpolation-mode: bicubic;
}

.row {
    margin-right: -15px;
    margin-left: -15px;

    >* {
        padding-left: 15px;
        padding-right: 15px;
    }
}

ul,
li {
    list-style-type: none;
    margin-bottom: 0;
    padding-left: 0;
    list-style: none;
}

.container {
    max-width: 1320px;
}

.container {
    width: 100%;
    margin: auto;
}

.container {
    padding-left: 15px;
    padding-right: 15px;
}

.container-full3,
.container-full2,
.container-full {
    width: 100%;
    max-width: 100%;
    margin: 0 auto;
    padding: 0px 15px;
}

.container-full3 {
    padding: 0px 14px;
}

.slider-layout-right {
    width: calc(100vw - ((100vw - 1290px) / 2));
    margin-right: unset;
    max-width: 100%;
    margin-left: auto;

    .swiper {
        margin-right: -15px;
    }
}

svg path {
    @include mixin.transition3();
}

// form //
textarea,
input[type="text"],
input[type="password"],
input[type="datetime"],
input[type="datetime-local"],
input[type="date"],
input[type="month"],
input[type="time"],
input[type="week"],
input[type="number"],
input[type="email"],
input[type="url"],
input[type="search"],
input[type="tel"],
input[type="color"] {
    border: 2px solid var(--line);
    outline: 0;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
    font-size: 16px;
    line-height: 26px;
    border-radius: 8px;
    padding: 9px 16px;
    width: 100%;
    background: var(--white);
    color: var(--main);
    font-weight: 400;
    @include mixin.transition3;

    &:hover,
    &:focus {
        border-color: var(--main);
    }
}

textarea::placeholder,
input[type="text"]::placeholder,
input[type="password"]::placeholder,
input[type="datetime"]::placeholder,
input[type="datetime-local"]::placeholder,
input[type="date"]::placeholder,
input[type="month"]::placeholder,
input[type="time"]::placeholder,
input[type="week"]::placeholder,
input[type="number"]::placeholder,
input[type="email"]::placeholder,
input[type="url"]::placeholder,
input[type="search"]::placeholder,
input[type="tel"]::placeholder,
input[type="color"]::placeholder {
    color: var(--secondary-2);
    @include mixin.transition3;
}

textarea {
    height: 160px;
    resize: none;
}

/* Placeholder color */
::-webkit-input-placeholder {
    color: var(--secondary-2);
}

:-moz-placeholder {
    color: var(--secondary-2);
}

::-moz-placeholder {
    color: var(--secondary-2);
    opacity: 1;
}

button {
    @include mixin.transition3;
    background-color: var(--main);
    color: var(--white);
    padding: 15px 32px;
    border-radius: 99px;
    @include mixin.flex(center, space-between);
    gap: 8px;
    font-size: 16px;
    line-height: 26px;
    font-weight: 600;
    text-transform: capitalize;

    .icon {
        font-size: 22px;
    }

    &:hover {
        background-color: transparent;
        color: var(--main);
    }
}


/* Since FF19 lowers the opacity of the placeholder by default */

:-ms-input-placeholder {
    color: var(--secondary-2);
}

/* Typography
-------------------------------------------------------------- */

.title-display,
h1,
h2,
h3,
h4,
h5,
h6 {
    font-family: variable.$font-main;
    text-rendering: optimizeLegibility;
    color: var(--main);
    font-weight: 500;
}

.title-display {
    font-size: 80px;
    line-height: 88px;
}

h1 {
    font-size: 56px;
    line-height: 68px;
}

h2 {
    font-size: 44px;
    line-height: 50px;
}

h3 {
    font-size: 40px;
    line-height: 48px;
}

h4 {
    font-size: 30px;
    line-height: 42px;
}

h5 {
    font-size: 24px;
    line-height: 30px;
}

h6 {
    font-size: 20px;
    line-height: 28px;
}

.body-text-1 {
    font-size: 18px;
    line-height: 28px;
}

.body-text {
    font-size: 16px;
    line-height: 26px;
}

.text-title {
    font-size: 16px;
    line-height: 24px;
    font-weight: 500;
}

.text-button {
    font-size: 16px;
    line-height: 26px;
    font-weight: 600;
}

.text-btn-uppercase {
    font-size: 12px;
    line-height: 20px;
    text-transform: uppercase;
    font-weight: 600;
    letter-spacing: 0.1em;
}

.text-btn-uppercase2 {
    font-family: variable.$font-2;
    font-size: 18px;
    line-height: 24px;
    text-transform: uppercase;
    font-weight: 600;
    letter-spacing: 0.1em;
}

.text-caption-1 {
    font-size: 14px;
    line-height: 22px;
}

.text-caption-2 {
    font-size: 12px;
    line-height: 16px;
}

.font-main {
    font-family: variable.$font-main;
}

.font-2 {
    font-family: variable.$font-2;
}

.font-3 {
    font-family: variable.$font-3 !important;
}


b,
strong {
    font-weight: bolder;
}

video {
    width: 100%;
    height: 100%;
    object-fit: cover;
}


// font-weight
.fw-4 {
    font-weight: 400 !important;
}

.fw-5 {
    font-weight: 500 !important;
}

.fw-6 {
    font-weight: 600 !important;
}

.fw-7 {
    font-weight: 700 !important;
}

.fw-8 {
    font-weight: 800 !important;
}

// color
.text-primary {
    color: var(--primary) !important;
}

.text-main {
    color: var(--main) !important;
}

.text-secondary {
    color: var(--secondary) !important;
}

.text-secondary-2 {
    color: var(--secondary-2) !important;
}

.text-white {
    color: var(--white) !important;
}

.text-surface {
    color: var(--surface) !important;
}

.text-critical {
    color: var(--critical) !important;
}

.text-warning {
    color: var(--warning) !important;
}

.text-success {
    color: var(--success) !important;
}

.text-yellow {
    color: var(--yellow) !important;
}

.text-line {
    color: var(--line) !important;
}

.text-pink {
    color: var(--pink) !important;
}

.text-blue {
    color: var(--blue) !important;
}

// background color 
.bg-primary {
    background-color: var(--primary) !important;
}

.bg-main {
    background-color: var(--main) !important;
}

.bg-secondary {
    background-color: var(--secondary) !important;
}

.bg-secondary-2 {
    background-color: var(--secondary-2) !important;
}

.bg-white {
    background-color: var(--white) !important;
}

.bg-surface {
    background-color: var(--surface) !important;
}

.bg-critical {
    background-color: var(--critical) !important;
}

.bg-warning {
    background-color: var(--warning) !important;
}

.bg-success {
    background-color: var(--success) !important;
}

.bg-yellow {
    background-color: var(--yellow) !important;
}

.bg-line {
    background-color: var(--line) !important;
}

.bg-pink {
    background-color: var(--pink) !important;
}

.bg-light-pink {
    background-color: #DFC6B8 !important;
}

.bg-light-pink-2 {
    background-color: #F4C5BF !important;
}

.bg-light-pink-3 {
    background-color: #FDEBEB !important;
}

.bg-dark-pink {
    background-color: #D10047 !important;
}

.bg-blue {
    background-color: var(--blue) !important;
}

.bg-blue-2 {
    background-color: #0C74D6 !important;
}

.bg-blue-3 {
    background-color: #0766BF !important;
}

.bg-light-blue {
    background-color: #D3D9EF !important;
}

.bg-light-blue-2 {
    background-color: #EBEAEF !important;
}

.bg-light-blue-3 {
    background-color: #EDF5F6 !important;
}

.bg-light-blue-4 {
    background-color: #EAF5FF !important;
}

.bg-light-blue-5 {
    background-color: #C9DFED !important;
}

.bg-dark-blue {
    background-color: #1B4872 !important;
}

.bg-purple {
    background-color: #2D3054 !important;
}

.bg-purple-2 {
    background-color: #6461BE !important;
}

.bg-purple-3 {
    background-color: #73465A !important;
}

.bg-orange {
    background-color: #FF9747 !important;
}

.bg-orange-2 {
    background-color: #F1592A !important;
}

.bg-light-orange {
    background-color: #EEAE76 !important;
}

.bg-grey {
    background-color: #E0D3C5 !important;
}

.bg-grey-2 {
    background-color: #D7CFC4 !important;
}

.bg-dark-grey {
    background-color: #B1AA98 !important;
}

.bg-dark-grey-2 {
    background-color: #9E9B96 !important;
}

.bg-light-grey {
    background-color: #F0EFED !important;
}

.bg-light-green {
    background-color: #B7B8A3 !important;
}

.bg-brown {
    background-color: #D6BB9A !important;
}

.bg-red {
    background-color: #DC2A35 !important;
}

.bg-red-2 {
    background-color: #D14244 !important;
}

.bg-beige {
    background-color: #CCBBA7 !important;
}

.bg-beige-2 {
    background-color: #F6EFDD !important;
}

.bg-beige-1 {
    background-color: #F6F1EE !important;
}

.bg-header-11 {
    background-color: var(--header-bg) !important;
}

.letter-1 {
    letter-spacing: 0.1em;
}

.letter-2 {
    letter-spacing: 0.2em;
}

.text-stroke-white {
    -webkit-text-stroke: 2px var(--white);
    color: transparent;
}

a {
    @include mixin.transition3;
    text-decoration: none;
    cursor: pointer;
    display: inline-block;
    color: var(--main);

    &:focus,
    &:hover {
        @include mixin.transition3;
        text-decoration: none;
        outline: 0;
    }
}

.link {
    @include mixin.transition3;

    &:hover {
        color: var(--primary) !important;
    }
}

// grid
.grid-2 {
    @include mixin.grid(2, 1fr);
}

.grid-3 {
    @include mixin.grid(3, 1fr);
}

.grid-4 {
    @include mixin.grid(4, 1fr);
}

.grid-6 {
    @include mixin.grid(6, 1fr);
}

// gap
.gap-4 {
    gap: 4px !important;
}

.gap-5 {
    gap: 5px;
}

.gap-6 {
    gap: 6px !important;
}

.gap-4 {
    gap: 4px !important;
}

.gap-8 {
    gap: 8px;
}

.gap-10 {
    gap: 10px;
}

.gap-12 {
    gap: 12px;
}

.gap-15 {
    gap: 15px;
}

.gap-16 {
    gap: 16px;
}

.gap-20 {
    gap: 20px;
}

.gap-24 {
    gap: 24px !important;
}

.row-gap-30 {
    row-gap: 30px !important;
}

.line {
    border: 1px solid var(--line);
}


.line-bt {
    border-bottom: 1px solid var(--line);
}

.line-top {
    border-top: 1px solid var(--line);
}

.line-top-rgba {
    border-top: 1px solid rgba(233, 233, 233, 0.1);
}

.line-black {
    border: 1px solid var(--main);
}

.no-line {
    border: 0 !important
}

.place-self-center {
    place-self: center !important;
}

// radius

.radius-3 {
    border-radius: 3px !important;
}

.radius-5 {
    border-radius: 5px !important;
}

.radius-10 {
    border-radius: 10px !important;
}

.radius-12 {
    border-radius: 12px !important;
}

.radius-20 {
    border-radius: 20px !important;
}

.radius-60 {
    border-radius: 60px !important;
}

.rounded-full {
    border-radius: 999px !important;
}

.o-hidden {
    overflow: hidden;
}

// height
.h-40 {
    height: 40px;
}

.h-46 {
    height: 46px;
}

.h-52 {
    height: 52px;
}

// padding
.px_15 {
    padding-left: 15px;
    padding-right: 15px;
}

.py-4 {
    padding-top: 4px !important;
    padding-bottom: 4px !important;
}

.box-center {
    position: absolute;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
}

// padding
.pt_0 {
    padding-top: 0px !important;
}

.pb_0 {
    padding-bottom: 0px !important;
}

.pr_0 {
    padding-right: 0px !important;
}

.py_8 {
    padding: 8px 0 !important;
}

.py_15 {
    padding: 15px 0;
}

.py_20 {
    padding: 20px 0;
}

.py_23 {
    padding: 23px 0 !important;
}

.pb_8 {
    padding-bottom: 8px;
}

.pb_15 {
    padding-bottom: 15px;
}

.pb_20 {
    padding-bottom: 20px;
}

// margin
.my_20 {
    margin: 20px 0px;
}

.mt_5 {
    margin-top: 5px;
}

.mt_3 {
    margin-top: 3px;
}

.mt_4 {
    margin-top: 4px;
}

.mt_8 {
    margin-top: 8px;
}

.mt_20 {
    margin-top: 20px;
}

.mt_37 {
    margin-top: 37px;
}

.mt_140 {
    margin-top: 140px;
}

.mb_4 {
    margin-bottom: 4px;
}

.mb_8 {
    margin-bottom: 8px;
}

.mb_6 {
    margin-bottom: 6px;
}

.mb_10 {
    margin-bottom: 10px;
}

.mb_12 {
    margin-bottom: 12px;
}

.mb_15 {
    margin-bottom: 15px;
}

.mb_16 {
    margin-bottom: 16px;
}

.mb_18 {
    margin-bottom: 18px;
}

.mb_20 {
    margin-bottom: 20px;
}

.mb_24 {
    margin-bottom: 24px !important;
}

.mb_30 {
    margin-bottom: 30px;
}

.mb_40 {
    margin-bottom: 40px;
}

.mb_32 {
    margin-bottom: 32px;
}

.mb_36 {
    margin-bottom: 36px;
}

.mb_60 {
    margin-bottom: 60px;
}

.mb_200 {
    margin-bottom: 200px;
}

// spacing
.flat-spacing {
    padding-top: 80px;
    padding-bottom: 80px;
}

.flat-spacing-2 {
    padding-top: 65px;
    padding-bottom: 65px;
}

.flat-spacing-3 {
    padding-top: 80px;
    padding-bottom: 90px;
}

.flat-spacing-4 {
    padding-top: 60px;
    padding-bottom: 60px;
}

.flat-spacing-5 {
    padding: 40px 60px;
}

.flat-spacing-6 {
    padding-top: 90px;
    padding-bottom: 80px;
}

.flat-spacing-7 {
    padding-top: 60px;
    padding-bottom: 80px;
}

.flat-spacing-8 {
    padding-top: 100.5px;
    padding-bottom: 100.5px;
}

.space-30 {
    padding-top: 30px;
    padding-left: 30px;
    padding-right: 30px;
}

[data-grid="grid-1"] {
    display: grid;
    gap: 30px;
    grid-template-columns: 1fr;
}

[data-grid="grid-2"] {
    display: grid;
    gap: 30px;
    grid-template-columns: 1fr 1fr;
}

[data-grid="grid-3"] {
    display: grid;
    gap: 30px;
    grid-template-columns: repeat(3, 1fr);
}

[data-grid="grid-4"] {
    display: grid;
    gap: 30px;
    grid-template-columns: repeat(4, 1fr);
}

[data-grid="grid-5"] {
    display: grid;
    gap: 30px;
    grid-template-columns: repeat(5, 1fr);
}

[data-grid="grid-6"] {
    display: grid;
    gap: 30px;
    grid-template-columns: repeat(6, 1fr);
}

[data-grid="grid-7"] {
    display: grid;
    gap: 30px;
    grid-template-columns: repeat(7, 1fr);
}

.grid-template-columns-2 {
    grid-template-columns: 1fr 1fr;
}

.tf-row-flex {
    display: flex;
    flex-direction: row;
    column-gap: 30px;
    row-gap: 30px;
}

.tf-grid-layout {
    display: grid;
    column-gap: 15px;
    row-gap: 30px;

    &.tf-col-2 {
        grid-template-columns: 1fr 1fr;
    }

    &.tf-col-3 {
        grid-template-columns: repeat(3, 1fr);
    }

    &.tf-col-4 {
        grid-template-columns: repeat(4, 1fr);
    }

    &.tf-col-5 {
        grid-template-columns: repeat(5, 1fr);
    }

    &.tf-col-6 {
        grid-template-columns: repeat(6, 1fr);
    }

    &.tf-col-7 {
        grid-template-columns: repeat(7, 1fr);
    }

    .wg-pagination {
        grid-column: 1 / -1;
        width: 100%;
    }

    .wd-load {
        grid-column: 1 / -1;
    }
}

.tf-grid-layout-v2 {
    display: grid;
    gap: 15px;
}

.overflow-unset {
    overflow: unset !important;
}

.sticky-top {
    z-index: 50;
    top: 15px;
}

.wmax {
    width: max-content !important;
}

.cursor-not-allowed {
    cursor: not-allowed;
}

.cursor-auto {
    cursor: auto;
}

.tag-list {
    list-style: disc;
    padding-left: 20px;

    li {
        list-style: inherit;
    }
}

.has-line-bottom {
    position: relative;

    &::after {
        position: absolute;
        content: "";
        bottom: 0;
        left: 50%;
        background-color: var(--line);
        height: 1px;
        width: 100%;
        max-width: 1440px;
        transform: translateX(-50%);
    }
}

.line-under {
    color: rgba(0, 0, 0, 0.85);
    text-underline-offset: 3px;
    text-decoration-thickness: 1px;
    text-decoration-line: underline;
    transition: text-decoration-thickness 1s ease;

    &:hover {
        color: var(--main);
        text-decoration-thickness: 2px;
        text-decoration-line: underline;
    }
}

.transition-linear {
    transition-timing-function: linear !important;
}

.z-5 {
    z-index: 5;
}

.text-highlight {
    -webkit-text-stroke: 1px #000;
    color: transparent !important;
    flex-direction: row-reverse;
}

.text-line-clamp-1 {
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    display: -webkit-box;
    overflow: hidden;
}

.text-line-clamp-2 {
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    display: -webkit-box;
    overflow: hidden;
}

.aspect-ratio-1 {
    aspect-ratio: 1/1;
}

.initial-child-container {
    flex: 0 0 auto;
    display: flex;
    min-width: auto;
    flex-direction: row;
    align-items: center;
}

.line-top-container {
    position: relative;

    &::before {
        position: absolute;
        content: "";
        top: 0;
        left: 50%;
        background-color: var(--line);
        height: 1px;
        width: 100%;
        max-width: 1320px;
        transform: translateX(-50%);
    }

}

.line-bottom-container {
    position: relative;

    &::after {
        position: absolute;
        content: "";
        bottom: 0;
        left: 50%;
        background-color: var(--line);
        height: 1px;
        width: 100%;
        max-width: 1320px;
        transform: translateX(-50%);
    }

}

// Scroll Top
#scroll-top {
    position: fixed;
    display: block;
    width: 48px;
    height: 48px;
    line-height: 50px;
    border-radius: 4px;
    z-index: 1;
    border-radius: 50%;
    opacity: 0;
    visibility: hidden;
    cursor: pointer;
    overflow: hidden;
    z-index: 100;
    background-color: var(--main);
    border: 0;
    bottom: 92px;
    right: 20px;
    padding: 0;
    @include mixin.flex(center, center);
    @include mixin.transition3;

    &.show {
        opacity: 1;
        visibility: visible;
    }

    &.type-1 {
        bottom: 140px;
    }

    &:hover {
        transform: translateY(-5px);
        background-color: var(--primary);
    }
}

/* Preload 
------------------------------------------- */

.preload-wrapper {
    .preload-container {
        display: flex;
    }
}

.preload-container {
    display: none;
    position: relative;
    width: 100%;
    height: 100%;
    background: #ffffff;
    position: fixed;
    top: 0;
    bottom: 0;
    right: 0;
    left: 0;
    z-index: 99999999999;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.spinner {
    width: 60px;
    height: 60px;
    border: 3px solid transparent;
    border-top: 3px solid var(--line);
    border-radius: 100%;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    margin: auto;
    animation: spin 1s infinite linear;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
    }
}