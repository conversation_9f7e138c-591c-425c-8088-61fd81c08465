.range-slider {
  height: 4px !important;

  .range-slider__range {
    background: var(--main) !important;
  }

  .range-slider__thumb {
    background: var(--white) !important;
    border: 2px solid var(--main);
    width: 16px !important;
    height: 16px !important;
  }
}

#shoppingCart.show .tf-progress-bar .value {
  width: 60% !important;
}

.menu-item-li.active {
  .menu-link-text {
    color: var(--primary) !important;

    &::before {
      width: 100%;
      left: 0;
      right: auto;
      background-color: var(--primary) !important;
    }
  }
}

.sub-nav-link.active,
.mb-menu-link.active,
.sub-nav-link.active {
  color: var(--primary) !important;
}

@media only screen and (max-width: 767px) {
  .footer .footer-col-block .tf-collapse-content {
    overflow-y: hidden;
    display: block;
    height: 0px;
    transition: 0.5s;
  }
}

.tfSubscribeMsg {
  max-height: 0px;
  // transition: 0.4s;
  overflow: hidden;

  &.active {
    margin-top: 10px;
    margin-bottom: 10px;
    max-height: 180px;
  }
}

.swiper-pagination-lock {
  display: none;
}

.show-mx-1200 {
  display: none;
}

@media (max-width: 1200px) {
  .show-mx-1200 {
    display: block !important;
  }

  .hidden-mx-1200 {
    display: none;
  }
}

.tf-toolbar-bottom {
  overflow-x: hidden;
}

.mw-100p-scroll {
  max-width: 100%;
  overflow-y: auto;
}

.mw-100p-hidden,
.tf-product-info-wrap {
  max-width: 100%;
  overflow-x: hidden;
}

.tabFilter {
  transform: translateY(30px) !important;
  opacity: 0 !important;
  transition: 0.3s !important;

  &.filtered {
    transform: translateY(0px) !important;
    opacity: 1 !important;
  }
}

.tf-pr-attrs {
  border-spacing: 0;
  border-collapse: collapse;
  width: 100%;
  border-radius: 5px;
  border-style: hidden;
  box-shadow: 0 0 0 .1rem var(--line);
}

.tf-pr-attrs tr {
  border: 1px solid var(--line);
  vertical-align: middle;
}

.tf-pr-attrs tr th {
  padding: 10px;
  border-right: 1px solid var(--line);
  font-weight: 500;
}

.tf-pr-attrs tr td {
  padding: 10px;
}