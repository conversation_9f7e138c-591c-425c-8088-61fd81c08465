/* Media Queries
-------------------------------------------------------------- */
// min-width:
@media (min-width: 576px) {
  .tf-grid-layout {
    &.sm-col-2 {
      grid-template-columns: 1fr 1fr;
    }

    &.sm-col-3 {
      grid-template-columns: repeat(3, 1fr);
    }

    &.sm-col-4 {
      grid-template-columns: repeat(4, 1fr);
    }

    &.sm-col-5 {
      grid-template-columns: repeat(5, 1fr);
    }

    &.sm-col-6 {
      grid-template-columns: repeat(6, 1fr);
    }

    &.sm-col-7 {
      grid-template-columns: repeat(7, 1fr);
    }
  }

  .gallery-item {
    .box-icon {
      width: 40px;
      height: 40px;
      font-size: 24px;
    }
  }

  .flat-img-with-text-v4 {
    .relatest-post {
      .relatest-post-item {
        .image {
          width: 180px;
          height: 120px;
        }

        .content {
          gap: 12px;
        }
      }
    }
  }
}

@media (min-width: 768px) {
  .canvas-filter {
    max-width: 400px;

    .canvas-header {
      padding: 15px 32px;
    }

    .canvas-body {
      padding: 20px 32px;
    }

    .canvas-bottom {
      padding: 18px 32px;
    }
  }

  .widget-facet {
    padding-bottom: 32px;

    &.facet-price {
      .box-price-product {
        gap: 20px;
      }
    }
  }

  .list-account-address {
    grid-template-columns: repeat(2, 1fr);
  }

  .about-us-features {
    max-width: 620px;
  }

  .about-us-content {
    max-width: 610px;
    margin-left: auto;
  }

  .frequently-bought-together-2,
  .slider-scroll,
  .thumbs-slider {
    max-width: 615px;
  }

  .tf-product-info-list {
    max-width: 615px;
    margin-left: auto;
  }

  .canvas-mb {
    width: 100% !important;
    max-width: 367px;

    .mb-canvas-content {
      min-width: 367px;
    }
  }

  .tf-grid-layout {
    &.md-col-2 {
      grid-template-columns: 1fr 1fr;
    }

    &.md-col-3 {
      grid-template-columns: repeat(3, 1fr);
    }

    &.md-col-4 {
      grid-template-columns: repeat(4, 1fr);
    }

    &.md-col-5 {
      grid-template-columns: repeat(5, 1fr);
    }

    &.md-col-6 {
      grid-template-columns: repeat(6, 1fr);
    }

    &.md-col-7 {
      grid-template-columns: repeat(7, 1fr);
    }
  }

  .card-product {
    .card-product-wrapper {
      .on-sale-wrap {
        top: 12px;
        right: 12px;
        left: 12px;
      }
    }

    &.card-product-size {
      .list-btn-main {
        bottom: 44px;
      }
    }

    &.style-7,
    &.style-6,
    &.style-2 {
      &.card-product-size {
        .list-product-btn {
          bottom: 44px;
        }
      }
    }
  }

  .flat-img-with-text {
    flex-direction: row;

    .banner-right {
      padding-top: 40px;
    }

    .banner-left {
      padding-bottom: 40px;
      padding-right: 60px;
    }

    .banner-content {
      position: absolute;
      bottom: 40px;
      left: 25.3%;
      transform: unset;
      top: auto;
    }
  }

  .flat-img-with-text-v2 {
    .banner-left {
      margin-bottom: 0;
    }
  }

  .collection-position-2 {
    .on-sale-wrap {
      top: 12px;
      right: 12px;
      left: 12px;
    }
  }

  .grid-cls-v1 {
    display: grid;
    grid-template-areas:
      "item1 item2 item4"
      "item1 item3 item4";
    gap: 15px;
    grid-template-columns: 1fr 1fr 1fr;

    .item1 {
      grid-area: item1;
    }

    .item2 {
      grid-area: item2;
    }

    .item3 {
      grid-area: item3;
    }

    .item4 {
      grid-area: item4;
    }

    .collection-position-2 {
      .img-style {
        height: 100%;
      }
    }
  }

  .grid-cls-v2 {
    display: grid;
    grid-template-areas:
      "item1 item2"
      "item1 item3";
    gap: 15px;

    .item1 {
      grid-area: item1;
    }

    .item2 {
      grid-area: item2;
    }

    .item3 {
      grid-area: item3;
    }
  }

  .tf-grid-layout {
    .banner-discover-left {
      padding-right: 15px;
    }

    .banner-discover-right {
      padding-left: 15px;
    }
  }

  .flat-banner-parallax-v3,
  .flat-banner-parallax-v2 {
    .fl-content {
      padding: 30px;
    }
  }

  .tf-compare-col {
    min-width: 300px;
  }
}

@media (min-width: 992px) {
  #scroll-top {
    bottom: 40px;
    right: 40px;

    &.type-1 {
      bottom: 113px;
    }
  }

  .canvas-filter {
    .canvas-bottom {
      .tf-btn {
        padding: 12px;
      }
    }
  }

  .image-select {

    &.type-currencies,
    &.type-languages {
      >.dropdown-menu {
        margin-left: -20px !important;
      }
    }
  }

  .modal {
    &.fullRight {
      &.fade {
        transition: opacity 0.3s linear;
      }
    }
  }

  .page-title {
    padding: 68px 0 90px;
  }

  .breadcrumbs-default {
    padding: 60px 0px 80px;
  }

  .btn-sidebar-account {
    display: none;
  }

  .tab-shipping.type-two-cols {
    display: grid;
    grid-template-columns: 1fr 1fr;
  }

  .sw-dots {
    gap: 12px;

    &:not(.swiper-pagination-lock) {
      margin-top: 40px;
    }
  }

  .nav-sw {
    width: 44px;
    height: 44px;

    .icon {
      font-size: 20px;
    }
  }

  .slider-default {
    &.slider-position {
      .box-content {
        left: 60px;
        right: 60px;
        bottom: 60px;
      }
    }
  }

  .tf-slideshow {
    &.slider-electronic {
      .wrap-slider {
        height: 600px;
      }
    }

    .card-box {
      padding: 30px;
    }
  }

  .wrap-slider {
    .content-slider {
      gap: 40px;
    }

    .box-title-slider {
      gap: 16px;
    }
  }

  .sidebar.maxw-360 {
    max-width: 360px;
    margin-left: auto;
  }

  .tf-topbar {
    .topbar-left {
      gap: 20px;
    }
  }

  .tf-grid-layout {
    column-gap: 30px;
    row-gap: 30px;

    &.lg-col-2 {
      grid-template-columns: 1fr 1fr;
    }

    &.lg-col-3 {
      grid-template-columns: repeat(3, 1fr);
    }

    &.lg-col-4 {
      grid-template-columns: repeat(4, 1fr);
    }

    &.lg-col-5 {
      grid-template-columns: repeat(5, 1fr);
    }

    &.lg-col-6 {
      grid-template-columns: repeat(6, 1fr);
    }

    &.lg-col-7 {
      grid-template-columns: repeat(7, 1fr);
    }
  }

  .card-product {
    .card-product-info {
      padding-top: 16px;
    }

    .list-btn-main {
      bottom: 12px;
      left: 14px;
      right: 14px;

      .box-icon {
        width: 42px;
        height: 42px;

        .icon {
          font-size: 24px;
        }
      }
    }

    .btn-main-product {
      padding: 10px;
    }

    .list-product-btn {
      top: 8px;
      right: 8px;
      gap: 8px;

      .box-icon {
        width: 40px;
        height: 40px;
        min-width: 40px;

        .icon {
          font-size: 24px;
        }
      }
    }

    &.style-7,
    &.style-6,
    &.style-2 {
      .list-product-btn {
        bottom: 20px;
      }
    }

    &.style-5 {
      .list-btn-main {
        gap: 12px;
      }
    }

    &.style-6 {
      .list-product-btn {
        gap: 12px;
      }
    }

    &.style-swatch-img {
      .list-color-product {
        gap: 4px;

        .list-color-item {
          width: 40px;
          height: 40px;
        }
      }
    }

    .box-icon {
      svg {
        width: 24px;
      }
    }

    &.style-list {
      gap: 40px;

      .list-product-btn {
        gap: 12px;

        .btn-main-product {
          max-width: 272px;
          width: 100%;
          border: 2px solid var(--line);
          height: 48px;
          line-height: 48px;
          padding-top: 0;
          padding-bottom: 0;
        }

        .box-icon {
          border: 2px solid var(--line);
          width: 48px;
          height: 48px;
          min-width: 48px;
        }
      }

      .size-box {
        margin-bottom: 20px;

        .size-item {
          font-size: 16px;
          line-height: 24px;
        }
      }

      .list-color-product {
        margin-bottom: 12px;
      }

      .title {
        font-size: 20px;
        line-height: 30px;
      }
    }

    &.list-st-2 {
      gap: 24px;

      .card-product-info {
        gap: 16px;
        padding: 12px 0px;
      }

      .box-icon {
        width: 48px;
        height: 48px;
        min-width: 48px;
      }
    }

    &.list-st-3 {
      border-width: 2px;
      padding: 24px;
      gap: 32px;

      .inner-wrapper-card {
        .box-progress-stock {
          margin-top: 16px;
        }
      }

      .card-product-info {
        gap: 20px;

        .inner-top {
          gap: 8px;
          padding-bottom: 12px;
          margin-bottom: 12px;
        }

        .title,
        .price {
          font-size: 20px;
          line-height: 28px;
        }

        .inner-bottom {
          gap: 16px;
        }

        .list-product-btn {
          gap: 10px;

          .box-icon {
            border-width: 2px;
            width: 40px;
            height: 40px;

            .tooltip {
              margin: 0;
            }
          }

          .btn-main-product {
            height: 40px;
            line-height: 40px;
            padding-top: 0;
            padding-bottom: 0;
          }
        }
      }
    }

    .countdown-box {
      .countdown__timer {
        gap: 18px;
      }
    }
  }

  .collection-default {
    gap: 32px;

    .content {
      gap: 12px;
    }
  }

  .collection-position {
    &.style-lg {
      .content {
        gap: 12px;
      }
    }
  }

  .collection-position-2 {
    .content {
      left: 20px;
      right: 20px;
      bottom: 20px;
    }

    .cls-btn {
      .icon {
        font-size: 20px;
      }
    }

    &.style-2 {
      .cls-btn {
        padding: 16px 28px;
      }
    }

    &.style-3 {
      .cls-btn {
        padding: 12px 24px;
      }
    }

    &.style-4 {
      .cls-btn {
        padding: 12px 52px;
      }
    }

    &.style-5 {
      .content {
        bottom: 40px;
      }
    }

    &.style-6 {
      .cls-btn {
        padding: 12px 40px;
      }
    }

    &.style-7 {
      .content {
        left: 28px;
        right: 28px;
        bottom: 28px;
      }
    }

    .cls-content {
      padding: 16px 20px;
    }

    &.radius-lg {
      border-radius: 40px;
    }
  }

  .collection-position-3 {
    .archive-top {
      left: 40px;
      top: 22px;
      gap: 12px;
    }

    .archive-btn {
      left: 40px;
      bottom: 40px;
    }
  }

  .slider-collection {
    .collection-position-2 {
      .cls-btn {
        padding: 12px 52px;

        .icon {
          font-size: 24px;
        }
      }

      .content {
        bottom: 40px;
      }
    }
  }

  .tab-product {
    gap: 40px;
    margin-bottom: 40px;

    .nav-tab-item {
      a {
        font-size: 30px;
        line-height: 42px;
      }
    }
  }

  .swiper,
  .flat-animate-tab {
    .sec-btn {
      margin-top: 40px;
    }
  }

  .flat-countdown-banner {
    .tf-countdown-lg {
      .countdown__timer {
        display: flex;
        justify-content: flex-end;
      }
    }

    .banner-img {
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      right: auto;
      overflow: hidden;
    }
  }

  .testimonial-item {
    .img-style {
      .box-icon {
        visibility: hidden;
        opacity: 0;
        width: 40px;
        height: 40px;
        font-size: 24px;
      }
    }

    &:hover {
      .img-style {
        .box-icon {
          visibility: visible;
          opacity: 1;
        }
      }
    }
  }

  .tf-marquee {
    .marquee-child-item {
      padding-left: 25px;
      padding-right: 25px;
    }

    &.marquee-style2 {
      .marquee-child-item {
        padding-left: 20px;
        padding-right: 20px;
      }
    }
  }

  .loobook-product {
    gap: 12px;

    .content {
      .btn-lookbook {
        margin-top: 12px;
      }
    }

    &.style-row {
      .img-style {
        max-width: 100px;
        max-height: 100px;
      }
    }
  }

  .lookbook-item {
    .dropend {
      .dropdown-menu {
        --bs-dropdown-min-width: 16rem;
      }
    }
  }

  .flat-img-with-text {
    display: flex;
    justify-content: space-between;
    gap: 30px;

    .banner {
      width: 50%;
    }

    .banner-right {
      padding-top: 60px;
    }

    .banner-left {
      padding-bottom: 60px;
      padding-right: 80px;
    }

    .banner-content {
      padding: 40px 60px 37px 60px;
      bottom: 60px;

      .content-text {
        margin-bottom: 32px;
        gap: 12px;
      }
    }
  }

  .tab-banner {
    .nav-tab-item {
      .nav-tab-link {
        padding-top: 20px;
        padding-bottom: 20px;

        .icon {
          font-size: 20px;
        }
      }
    }
  }

  .testimonial-item-v2 {
    gap: 20px;

    .quote-box {
      gap: 20px;

      .icon {
        font-size: 44px;
      }
    }
  }

  .tf-sw-testimonial {
    .box-navigation {
      margin-top: 28px;
    }
  }

  .flat-img-with-text-v3 {
    .banner-left {
      padding-right: 60px;
    }

    .box-title {
      margin-bottom: 40px;
      gap: 12px;
    }
  }

  .flat-img-with-text-v4 {
    gap: 60px;
    grid-template-columns: 1fr 1fr;
  }

  .list-collection {
    gap: 40px;
    margin-bottom: 40px;

    &.style-lg {
      .cls-item {
        gap: 40px;

        .img-cls {
          width: 133px;
          height: 100px;
        }
      }
    }
  }

  .flat-countdown-banner-2 {
    .box-content {
      .tf-countdown-lg {
        margin-top: 24px;
      }

      .btn-banner {
        margin-top: 40px;
      }
    }
  }

  .banner-cls {
    position: relative;

    .cls-content {
      gap: 36px;
      left: 40px;
      right: 40px;
      bottom: 40px;

      .box-title-cls {
        gap: 12px;
      }
    }
  }

  .flat-single-home {
    &:not(.flat-single-home-default) {

      .tf-product-info-wrap,
      .tf-product-media-wrap {
        padding-left: 15px;
        padding-right: 15px;
      }
    }

    &.flat-single-home-default {
      .tf-product-info-wrap {
        padding-left: 15px;
      }
    }
  }

  .flat-banner-parallax {
    padding: 182px 0px;

    .fl-content {
      gap: 36px;
    }
  }

  .flat-banner-parallax-v3,
  .flat-banner-parallax-v2 {
    .fl-content {
      padding: 40px;
    }
  }

  .grid-cls-v2 {
    gap: 30px;
  }

  .testimonial-item {
    &.style-3 {
      padding: 32px;
    }
  }

  .banner-cls-discover {
    .cls-content {
      gap: 36px;

      .box-title-top {
        gap: 12px;
      }
    }
  }

  .tf-grid-layout {
    .banner-discover-left {
      padding-right: 30px;
    }

    .banner-discover-right {
      padding-left: 30px;
    }
  }

  .flat-with-text-lookbook-v2 {
    display: flex;
    align-items: center;

    .lookbook-content {
      width: 50%;
      padding-bottom: 0;
    }

    .banner-img {
      width: 50%;
    }
  }

  // shop
  .wrapper-control-shop {
    .tf-list-layout {
      .card-product {
        margin-bottom: 40px;
        padding-bottom: 40px;
      }

      .wg-pagination {
        margin-top: 40px;
      }
    }

    .tf-grid-layout {
      row-gap: 40px;
    }
  }

  .tf-table-page-cart {
    th {
      font-size: 20px;
      padding-left: 20px;
      padding-right: 20px;
      text-align: center;

      &:last-child {
        padding-right: 0;
      }

      &:first-child {
        padding-left: 0;
        text-align: start;
      }
    }

    td {
      padding: 28px 20px;
    }
  }

  .box-order {
    padding: 24px;

    .ship {
      gap: 40px;
    }
  }

  .tf-page-checkout {
    .wrap {
      &:not(:last-child) {
        margin-bottom: 40px;
      }

      .title {
        margin-bottom: 20px;
      }
    }

    .login-box {
      padding: 20px;

      .grid-2 {
        gap: 20px;
      }
    }

    .info-box {
      gap: 20px;
    }
  }

  .payment-box {
    .payment-item {
      &:not(:last-child) {
        margin-bottom: 20px;
      }

      .payment-body {
        gap: 19px;

        .input-payment-box {
          gap: 20px;
        }
      }
    }
  }

  .form-payment {
    .tf-btn {
      margin-top: 40px;
    }
  }

  .flat-sidebar-checkout {
    .item-product {
      gap: 24px;
    }
  }
}

@media (min-width: 1025px) {
  .flat-img-with-text {
    .banner-content {
      h3 {
        line-height: 52px;
      }
    }
  }

  .tf-marquee {
    &.marquee-style2 {
      .marquee-child-item {
        .icon {
          font-size: 40px;
        }
      }
    }
  }
}

@media (min-width: 1200px) {
  .sidebar-filter {
    .canvas-body {
      padding: 0;
    }
  }

  .dropdown-filter {
    max-width: 100%;
    position: absolute;
    z-index: 100;
    padding: 30px 0px 40px;
    top: 100%;
    left: 0;
    right: 0;
    opacity: 0;
    visibility: hidden;

    .widget-facet {
      margin-bottom: 0;
      padding-bottom: 0;
      border: 0;
    }

    .canvas-body {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      column-gap: 30px;
      row-gap: 40px;
    }

    &.show {
      margin-top: 10px;
      opacity: 1;
      visibility: visible;
    }
  }

  .container-full2 {
    padding: 0 40px;
  }

  .nav-sw {
    &.lg {
      width: 60px;
      height: 60px;
      border-color: transparent;

      .icon {
        font-size: 28px;
      }
    }
  }

  .slider-padding,
  .header-fullwidth {
    padding-left: 40px;
    padding-right: 40px;
  }

  .slider-default {
    .box-content {
      left: 80px;
      right: 80px;
      bottom: 80px;
    }
  }

  .tf-slideshow {
    .wrap-pagination {
      bottom: 40px;
    }

    .nav-sw {
      &.nav-sw-left {
        left: 60px;
      }

      &.nav-sw-right {
        right: 60px;
      }
    }

    &.slider-position {
      .wrap-pagination {
        bottom: 20px;
      }
    }

    &.slider-pos-nav {
      .nav-sw {
        &.nav-sw-left {
          left: 40px;
        }

        &.nav-sw-right {
          right: 40px;
        }
      }
    }

    .card-box {
      padding: 60px;

      &.style-2 {
        margin-right: -10px;
      }
    }

    &.slideshow-effect {
      .wrap-pagination {
        bottom: 32px;
      }
    }
  }

  .header-default {
    .wrapper-header {
      min-height: 82px;
    }
  }

  .header-absolute {
    margin-bottom: -82px;
  }

  .footer {
    .footer-menu {
      gap: 100px;
    }
  }

  .tf-dropdown-sort {
    padding: 5px 12px;
    min-width: 164px;
  }

  .tf-grid-layout {
    &.xl-col-2 {
      grid-template-columns: 1fr 1fr;
    }

    &.xl-col-3 {
      grid-template-columns: repeat(3, 1fr);
    }

    &.xl-col-4 {
      grid-template-columns: repeat(4, 1fr);
    }

    &.xl-col-5 {
      grid-template-columns: repeat(5, 1fr);
    }

    &.xl-col-6 {
      grid-template-columns: repeat(6, 1fr);
    }

    &.xl-col-7 {
      grid-template-columns: repeat(7, 1fr);
    }
  }

  .card-product {
    .marquee-product {
      display: flex;
    }

    .tooltip,
    .countdown-wrap {
      display: block;
    }

    .list-product-btn {
      top: 12px;
      right: 12px;
    }

    &:not(.style-list, .list-st-2, .list-st-3) {

      .size-list,
      .list-btn-main {
        transform: translateY(100%);
        opacity: 0;
        visibility: hidden;
      }

      .box-icon {
        transform: translate(20px);
        opacity: 0;
        visibility: hidden;
      }

      .card-product-info {
        .btn-main-product {
          transform: translateY(20px);
          opacity: 0;
          visibility: hidden;
        }
      }
    }

    &:hover:not(.style-list, .list-st-2, .list-st-3) {
      .marquee-product {
        opacity: 0;
        visibility: hidden;
      }

      .variant-wrap {
        transform: translateY(100%);
        opacity: 0;
        visibility: hidden;
      }

      .size-list,
      .box-icon,
      .list-btn-main {
        opacity: 1 !important;
        visibility: visible !important;
        transform: translate(0) !important;
      }

      .card-product-info {
        .btn-main-product {
          transform: translate(0);
          opacity: 1;
          visibility: visible;
        }
      }
    }

    &.style-7,
    &.style-6,
    &.style-2 {
      .list-product-btn {
        left: 12px;
        right: 12px;

        .box-icon {
          transform: translateY(20px);
        }
      }
    }

    &.style-swatch-img {
      .list-color-product {
        gap: 8px;

        .list-color-item {
          width: 48px;
          height: 48px;
          padding: 4px;
          border: 2px solid transparent;
        }
      }
    }

    &.list-st-3 {
      .card-product-info {
        gap: 35px;
      }
    }
  }

  .tf-countdown-lg {
    .countdown__timer {
      gap: 47px;
    }

    .countdown__item {
      min-width: 68px;
      min-height: 90px;

      .countdown__value {
        font-size: 56px;
        line-height: 68px;
        margin-bottom: -3px;
      }

      &:not(:last-child)::after {
        font-size: 30px;
        line-height: 42px;
        right: -27px;
      }
    }
  }

  .gallery-item {
    .box-icon {
      opacity: 0;
      visibility: hidden;
    }

    &:hover {
      &::before {
        opacity: 1;
        visibility: visible;
      }

      .box-icon {
        opacity: 1;
        visibility: visible;
      }
    }
  }

  .lookbook-item {
    .dropdown-menu {
      --bs-dropdown-min-width: 14rem;
    }

    .dropend {
      .dropdown-menu {
        --bs-dropdown-min-width: 324px;
      }
    }
  }

  .flat-img-with-text {
    display: flex;
    justify-content: space-between;
    gap: 30px;

    .banner {
      width: 50%;
    }

    .banner-right {
      padding-top: 80px;
    }

    .banner-left {
      padding-bottom: 80px;
      padding-right: 157px;
    }

    .banner-content {
      bottom: 80px;
    }
  }

  .flat-img-with-text-v2 {
    align-items: center;

    .banner-left {
      padding-right: 110px;
      gap: 60px;
    }

    .banner-right {
      padding-left: 47px;
    }
  }

  .flat-img-with-text-v3 {
    .banner-left {
      padding-right: 158px;
    }
  }

  .flat-img-with-text-v4 {
    gap: 110px;
  }

  .flat-with-text-lookbook {
    .lookbook-content {
      padding-left: 110px;
      padding-right: 22px;

      .box-title {
        margin-bottom: 34px;
      }

      .total-lb {
        margin-top: 34px;
      }
    }
  }

  // .wrap-lookbook-hover {
  //     .bundle-pin-item {
  //         cursor: pointer;
  //     }
  //     .bundle-hover-item {
  //         @include transition3();
  //         &.no-hover {
  //             opacity: 0.3;
  //         }
  //         &.card-product {
  //             .title {
  //                 width: max-content;
  //                 display: inline;
  //                 background-repeat: no-repeat;
  //                 background-position-y: 0px;
  //                 background-image: linear-gradient(transparent calc(100% - 1px), currentColor 1px);
  //                 transition: 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
  //                 background-size: 0 100%;
  //                 -webkit-backface-visibility: hidden;
  //                 backface-visibility: hidden;
  //             }
  //         }
  //     }
  //     .has-hover {
  //         .card-product:not(.no-hover) {
  //             .title {
  //                 background-size: 100% 100%;
  //                 transition-delay: 0.2s;
  //             }
  //         }
  //     }
  // }
  .flat-with-text-lookbook-v2 {
    .lookbook-content {
      padding: 30px 80px;
      padding-right: 100px;

      .box-title {
        margin-bottom: 32px;
      }

      .total-lb {
        margin-top: 32px;
      }
    }
  }

  .tf-sw-testimonial {
    .testimonial-item-v2 {
      padding-left: 140px;
      padding-right: 140px;
    }
  }

  .grid-cls-v1 {
    gap: 30px;
  }

  .layout-sw-center {
    overflow: hidden;

    .tf-sw-categories {
      margin: 0 -300px;
    }
  }

  .flat-single-home {
    &:not(.flat-single-home-default) {
      .tf-product-media-main {
        width: calc(100% - 110px);

        .item {
          max-height: 653px;
        }
      }

      .tf-product-media-thumbs {
        width: 100px;
        max-height: 653px;
      }
    }
  }

  .tf-compare-field {
    padding: 15px 30px;
  }

  .tf-compare-item {
    padding: 32px 40px 20px;
  }

  .tf-compare-col {
    min-width: 350px;
  }

  .box-order {
    .ship {
      gap: 65px;
    }
  }

  .fl-sidebar-cart {
    padding-left: 30px;
  }

  .line-separation {
    width: 1px;
    height: 100%;
  }

  .nav-account {
    .dropdown-account {
      .list-menu-item {
        a {
          padding: 16px 0px;
        }
      }
    }

    .dropdown-login {
      .tf-btn {
        padding: 12px 32px;
      }
    }
  }

  .wrapper-chat {
    right: 60px;
  }

  .hover-cursor-img {
    position: relative;

    .hover-image {
      position: fixed;
      transform: scale(0);
      pointer-events: none;
      opacity: 0;
      transition: transform 0.3s ease, opacity 0.3s ease;
      z-index: 1000;
      display: block;

      img {
        max-width: 150px;
        box-shadow: var(--shadow1);
      }
    }
  }
}

@media (min-width: 1440px) {
  .slider-video {
    .wrap-slider {
      height: 680px;
    }
  }

  .header-style-02 {
    .logo-header {
      margin-left: 40px;
    }
  }

  .header-default {
    .box-support {
      margin-right: 44px;
    }
  }

  .tf-grid-layout {
    &.xxl-col-2 {
      grid-template-columns: 1fr 1fr;
    }

    &.xxl-col-3 {
      grid-template-columns: repeat(3, 1fr);
    }

    &.xxl-col-4 {
      grid-template-columns: repeat(4, 1fr);
    }

    &.xxl-col-5 {
      grid-template-columns: repeat(5, 1fr);
    }

    &.xxl-col-6 {
      grid-template-columns: repeat(6, 1fr);
    }

    &.xxl-col-7 {
      grid-template-columns: repeat(7, 1fr);
    }
  }

  .tf-slideshow {
    .slider-group {
      gap: 20px;

      img {
        border-radius: 8px;
      }
    }
  }

  .flat-collection-circle {
    .nav-sw-left {
      margin-left: -22px;
    }

    .nav-sw-right {
      margin-right: -22px;
    }
  }

  .nav-account {
    .dropdown-account {
      min-width: 250px;
      left: -100px;
    }

    .dropdown-login {
      min-width: 290px;
    }
  }

  .header-fullwidth {
    .nav-account {
      .dropdown-login {
        left: auto;
        right: -100px;
      }
    }
  }
}

@media (min-width: 1600px) {
  .nav-account {
    .dropdown-account {
      left: -70px;
      min-width: 320px;
    }

    .dropdown-login {
      left: -100px;
    }
  }
}

// max-width:
@media only screen and (max-width: 1440px) {
  .my-account-wrap {
    gap: 40px;
  }

  .terms-of-use-wrap {
    gap: 60px;
  }

  .product-fixed-price {
    .tf-product-info-list {
      padding: 32px 15px;
    }
  }

  .tf-main-product.full-width {
    padding: 0 15px;
    gap: 0 30px;

    >div {
      width: calc(50% - 15px);
    }
  }

  .offcanvas-compare .offcanvas-content {
    .icon-close-popup {
      top: 10px;
      right: 10px;
      width: 25px;
      height: 25px;
      font-size: 10px;
    }

    .tf-compare-list {
      gap: 20px;

      .tf-compare-wrap {
        padding-right: 0;
        margin-right: 0;
        gap: 25px;
      }

      .tf-compare-item {
        .icon-close {
          width: 28px;
          height: 28px;
          font-size: 10px;
        }

        >.btns-repeat {
          right: -22px;
        }
      }
    }
  }
}

@media only screen and (max-width: 1399px) {
  .header-style-2 {
    .box-nav-ul {
      gap: 13px;
    }
  }

  .tf-slideshow {
    .wrap-slider {
      height: 700px;
    }
  }

  .wrapper-shop {
    &.tf-col-7 {
      column-gap: 15px;
    }
  }

  .tf-control-layout {

    .sw-layout-6,
    .sw-layout-7 {
      display: none;
    }
  }
}

@media only screen and (max-width: 1199px) {
  .nav-account .dropdown-account {
    top: calc(100% + 20px);
  }

  .fl-sidebar-cart {
    margin-top: 30px;
  }

  .sidebar-filter {
    position: fixed;
    bottom: 0;
    z-index: 3000;
    display: flex;
    flex-direction: column;
    background-clip: padding-box;
    outline: 0;

    &.left {
      top: 0;
      left: 0;
      transform: translateX(-100%);
    }

    &.right {
      top: 0;
      right: 0;
      transform: translateX(100%);
    }

    &.show {
      transform: none;
    }
  }

  .wg-free-delivery .free-delivery-info {
    padding-left: 30px;
    padding-right: 30px;
  }

  .tf-countdown.style-2 .countdown__timer {
    gap: 50px;
  }

  .tf-control-layout {

    .sw-layout-5,
    .sw-layout-4 {
      display: none;
    }
  }

  .login-wrap {
    gap: 60px;
  }

  .page-faqs-wrap {
    gap: 60px 30px;

    .ask-question {
      width: 300px;
    }
  }

  .page-404 {
    .page-404-inner {
      .content {
        max-width: unset;

        .heading {
          font-size: 80px;
          line-height: 100px;
        }
      }
    }
  }

  .contact-us-content {
    gap: 30px;
  }

  .wg-card-store {
    .card-store-info {
      padding-left: 15px;
      padding-right: 15px;

      .card-store-heading {
        margin-bottom: 24px;
      }

      >ul {
        gap: 20px;
      }
    }
  }

  .tf-store-list.style-row {
    margin-bottom: 40px;
  }

  .px-xl {
    padding-left: 15px;
    padding-right: 15px;
  }

  .pl-xl {
    padding-left: 15px;
  }

  .modal-search .modal-dialog .modal-content {
    padding: 15px;
    gap: 20px;

    .tf-grid-layout {
      padding-right: 10px;
      margin-right: -15px;
    }
  }

  .modal-size-guide .modal-dialog {
    width: 700px;

    .modal-content {
      padding: 15px;

      .icon-close-popup {
        width: 25px;
        height: 25px;
        font-size: 10px;
      }

      .widget-size {
        gap: 16px;

        .box-title-size {
          .title-size {
            width: 70px;
          }
        }
      }

      .header {
        margin-bottom: 20px;
      }
    }
  }

  .tab-size {
    gap: 16px;

    .size-button-wrap {
      gap: 10px;

      .size-button-item {
        padding: 10px 0;
      }
    }
  }

  .logo-header {
    display: flex;
    justify-content: center;
  }

  .header-default .wrapper-header .nav-icon {
    gap: 10px;
  }

  .header-style-3 {
    .box-navigation {
      display: none;
    }

    .wrapper-header-left {
      justify-content: center;
    }
  }

  .list-color-product {
    .list-color-item {
      width: 20px;
      height: 20px;
    }
  }

  .slider-scroll,
  .thumbs-slider {
    flex-direction: column !important;

    >div {
      width: 100%;
    }

    .tf-product-media-thumbs {
      order: 1;
    }
  }

  .slider-nav-sw {
    .nav-sw {
      display: none;
    }

    .wrap-pagination {
      display: block;
    }
  }

  .tf-slideshow {
    .nav-sw {
      display: none;
    }
  }

  .flat-spacing-5 {
    padding: 40px 30px;
  }
}

@media only screen and (max-width: 1024px) {
  .coming-soon .coming-soon-inner .content {
    .heading {
      margin-bottom: 16px;
    }
  }

  .title-display {
    font-size: 50px;
    line-height: 58px;
  }

  h1 {
    font-size: 46px;
    line-height: 58px;
  }

  h2 {
    font-size: 34px;
    line-height: 40px;
  }

  h3 {
    font-size: 30px;
    line-height: 38px;
  }

  h4 {
    font-size: 26px;
    line-height: 36px;
  }

  h5 {
    font-size: 20px;
    line-height: 26px;
  }

  h6 {
    font-size: 18px;
    line-height: 26px;
  }
}

@media only screen and (max-width: 991px) {
  .tf-sticky-btn-atc {
    bottom: 67px;
  }

  .tf-toolbar-bottom {
    display: flex;
  }

  .tf-add-cart-success {
    display: none;
  }

  .modal-search .tf-loading {
    height: 42px;
  }

  .icv__label {
    padding: 8px 15px;
  }

  #image-compare {
    width: 100%;
    height: 400px;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .section-flash-sale .wrap {
    gap: 30px;

    .image {
      display: none;
    }
  }

  .wg-big-save .content {
    left: 15px;
  }

  .tf-list-categories.style-1 {
    display: none;
  }

  .slider-pet-store .wrap-slider .content-slider {
    padding-left: 15px;
    padding-right: 15px;
  }

  .grid-card-product {
    gap: 20px;
  }

  .tf-shop-control {
    .tf-control-layout {
      gap: 6px;
    }
  }

  .wg-pagination {
    .pagination-item {
      width: 36px;
      height: 36px;
    }
  }

  .wrap-sidebar-account {
    display: none;
  }

  .terms-of-use-wrap {
    gap: 30px;

    .left {
      width: 300px;
    }

    .right {
      .heading {
        margin-bottom: 32px;
      }
    }
  }

  .contact-us-map {
    flex-direction: column;

    .right {
      padding: 30px 15px;
      max-width: unset;
    }
  }

  .tf-product-deals {
    padding-left: 15px;
    padding-right: 15px;
  }

  .product-fixed-scroll {
    .accordion-product-wrap {
      padding-top: 20px;
    }
  }

  .product-fixed-price {
    .left-desc {
      max-width: unset;
      padding-top: 40px !important;
    }
  }

  .product-fixed-price {
    .grid-image-top {
      gap: 16px 15px;
      margin-bottom: 40px;

      .item-3 {
        margin-bottom: 16px;
      }
    }
  }

  .tf-sticky-btn-atc {
    .tf-sticky-atc-infos {
      gap: 15px;

      .tf-sticky-atc-btns {
        width: 150px;
      }

      .wg-quantity {
        width: 120px;
      }
    }
  }

  .tab-shipping {
    flex-direction: column;
  }

  .tf-slideshow {
    .wrap-slider {
      height: 500px;
    }

    .box-title-slider {
      p br {
        display: none;
      }
    }

    .card-box {
      br {
        display: none;
      }
    }
  }

  .sw-dots {
    &.type-circle {
      .swiper-pagination-bullet {
        width: 16px !important;
        height: 16px !important;

        &::after {
          width: 6px;
          height: 6px;
        }
      }
    }

    &.type-square {
      .swiper-pagination-bullet {
        width: 15px;

        &.swiper-pagination-bullet-active {
          width: 30px;
        }
      }
    }
  }

  .product-description-list .product-description-list-item .product-description-list-content,
  .accordion-product-wrap .accordion-product-item .accordion-content {
    padding: 30px 15px;
  }

  .widget-tabs {
    &.style-menu-tabs {
      flex-wrap: wrap;
      gap: 28px;

      .widget-menu-tab {
        flex-direction: row;
        width: 100%;

        .item-title {
          padding-left: 0 !important;
          flex-shrink: 0;

          &::after {
            bottom: 0px;
            left: 0;
            width: 0;
            height: 1px !important;
            top: unset;
          }

          &.active::after {
            width: 100%;
          }
        }
      }

      .widget-content-inner {
        padding: 30px 15px;
      }
    }

    &.style-1 {
      .widget-menu-tab {
        justify-content: flex-start;
      }

      .widget-content-inner {
        padding: 30px 15px;
      }
    }
  }

  .tab-description {
    flex-wrap: wrap;
  }

  .blog-detail-wrap {
    >.inner {
      padding: 15px 15px 0;
    }
  }

  .wg-blog {
    .image {
      margin-bottom: 16px;
    }

    &.style-1 {
      .image {
        margin-bottom: 15px;
      }
    }
  }

  .tf-btn,
  button {
    padding: 12px 20px;
    font-size: 14px;
    line-height: 22px;

    .icon {
      font-size: 18px;
    }
  }

  .btn-line {
    font-size: 14px;
    line-height: 22px;
  }

  footer {
    padding-bottom: 67px;

    .footer-menu {
      margin-bottom: 30px;

      >div {
        width: 50%;
      }
    }

    .footer-infor {
      margin-bottom: 30px;
    }

    .footer-body {
      padding: 40px 0;
    }

    &.has-pb {
      padding-bottom: 165px;
    }
  }

  .mb-lg-30 {
    margin-bottom: 30px;
  }

  .flat-spacing-8,
  .flat-spacing {
    padding-top: 60px;
    padding-bottom: 60px;
  }

  .flat-spacing-2 {
    padding-top: 45px;
    padding-bottom: 45px;
  }

  .flat-spacing-3 {
    padding-top: 60px;
    padding-bottom: 60px;
  }

  .flat-spacing-4 {
    padding-top: 40px;
    padding-bottom: 40px;
  }

  .flat-spacing-5 {
    padding: 40px 15px;
  }

  .flat-spacing-6 {
    padding-top: 60px;
    padding-bottom: 60px;
  }

  .space-30 {
    padding-left: 15px;
    padding-right: 15px;
  }

  .heading-section-2,
  .heading-section {
    margin-bottom: 30px;
  }

  [data-grid="grid-4"] {
    grid-template-columns: repeat(3, 1fr);
  }

  .flat-countdown-banner {
    .banner-right {
      margin-top: 30px;
    }
  }

  .testimonial-item {
    .content {
      padding: 20px;
    }

    .content-top {
      padding-bottom: 15px;
      margin-bottom: 15px;
    }

    .box-avt {
      gap: 8px;
    }
  }

  .banner-lookbook {
    height: 300px;
  }

  .loobook-product {
    &::before {
      content: none;
    }
  }

  .testimonial-item {
    &.style-2 {
      padding: 15px;
    }
  }

  .list-collection {
    &.style-lg {
      .cls-item {
        .text {
          font-size: 30px;
          line-height: 38px;
        }
      }
    }
  }

  #scroll-top {
    width: 40px;
    height: 40px;

    svg {
      width: 20px;
      height: 20px;
    }
  }
}

@media only screen and (max-width: 767px) {
  footer.has-pb {
    padding-bottom: 197px;
  }

  .loobook-product:not(.style-row) .img-style {
    height: 120px;
  }

  .tab-product .nav-tab-item a {
    font-size: 18px;
    line-height: 30px;
  }

  .md-overflow-x {
    overflow-x: scroll;
    flex-wrap: nowrap;
    justify-content: flex-start;
  }

  .footer {
    .footer-heading-mobile {
      display: block;
      position: relative;

      &::after {
        position: absolute;
        content: "";
        right: 10px;
        top: 50%;
        transform: translateY(-50%);
        width: 12px;
        height: 1px;
        background-color: var(--main);
        transition: 0.25s ease-in-out;
      }

      &::before {
        position: absolute;
        content: "";
        right: 15px;
        top: 50%;
        transform: translate(-50%, -50%);
        width: 1px;
        height: 12px;
        background-color: var(--main);
        transition: 0.25s ease-in-out;
      }
    }

    .footer-col-block {
      &.open {
        .footer-heading-mobile {
          &::before {
            opacity: 0;
          }

          &::after {
            transform: translate(0%, -50%) rotate(180deg);
          }
        }
      }

      .tf-collapse-content {
        display: none;
      }
    }

    .footer-menu {
      flex-direction: column;
      gap: 18px;
      margin-bottom: 18px;

      >div {
        width: 100%;
      }
    }
  }

  .section-flash-sale .wrap {
    flex-direction: column;
    padding: 30px 0;

    >div {
      width: 100%;
      text-align: center !important;
    }

    p {
      margin-bottom: 16px !important;
    }
  }

  .tf-compare-row {
    &:not(:first-child) {
      .tf-compare-col {
        &:nth-child(2) {
          border-left: 1px solid var(--line);
        }
      }
    }
  }

  .wg-pet-store {
    .pet-store-heading {
      padding: 8px 15px;
    }

    .pet-store-list .pet-store-item {
      padding: 8px 15px;
    }
  }

  .wg-benefit .benefit-item {
    justify-content: center;
  }

  .collection-position-2 {
    &.style-3 {
      .cls-btn {
        h6 {
          flex-grow: 1;
        }

        .count-item {
          display: none;
        }
      }
    }
  }

  .collection-position.style-1 .content {
    bottom: 24px;
    left: 15px;
  }

  .wg-free-delivery .free-delivery-info {
    padding-left: 20px;
    padding-right: 20px;
  }

  .tf-countdown.style-2 .countdown__timer {
    gap: 30px;

    .countdown__item {
      width: 50px;
      height: 50px;

      .countdown__value {
        font-size: 24px;
        line-height: 32px;
      }

      .countdown__label {
        font-size: 12px;
        line-height: 20px;
        left: 42px;
      }
    }
  }

  .tf-control-layout .sw-layout-3 {
    display: none;
  }

  .wg-blog {
    &.style-row {
      flex-direction: column;

      >* {
        width: 100% !important;
      }

      .content {
        padding: 16px 0 0 0;
        gap: 12px;
      }
    }
  }

  .login-wrap {
    flex-direction: column;
    gap: 40px;

    &::before {
      display: none;
    }
  }

  .terms-of-use-wrap {
    flex-direction: column;

    .left {
      width: 100%;
      position: unset;
    }

    .right {
      .heading {
        margin-bottom: 20px;
      }
    }
  }

  .page-faqs-wrap {
    flex-direction: column;

    .ask-question {
      width: unset;
      padding: 24px 15px;
    }
  }

  .page-404 {
    .page-404-inner {
      flex-direction: column;

      .content {
        gap: 16px;
        text-align: center;

        .heading {
          font-size: 36px;
          line-height: 48px;
        }

        a {
          margin-left: auto;
          margin-right: auto;
        }
      }
    }
  }

  .contact-us-content {
    flex-direction: column;

    .right {
      max-width: unset;

      h4 {
        margin-bottom: 20px;
      }
    }
  }

  .tf-store-list {
    margin-bottom: 40px;

    .tf-store-item {
      padding: 20px 15px;
    }
  }

  .map-contact {
    height: 500px;
  }

  .team-item .tf-social-icon a {
    width: 40px;
    height: 40px;
    font-size: 18px;
  }

  .form-bundle-product {
    padding: 24px 15px;

    .tf-bundle-product-item {
      gap: 10px;

      .tf-check {
        margin: 0;
      }
    }
  }

  .reply-comment.style-1 .type-reply {
    margin-left: 15px;
  }

  .modal-wishlist .modal-content {
    max-width: min(500px, 90vw) !important;

    .tf-mini-cart-bottom,
    .header {
      padding: 20px 15px;
    }

    .tf-mini-cart-item {
      margin-left: 15px;
      margin-right: 15px;
    }
  }

  .modal-shopping-cart .modal-content {
    max-width: min(500px, 90vw) !important;
  }

  .modal-quick-view .modal-content {
    max-width: min(500px, 90vw) !important;
    flex-direction: column;
    overflow-y: auto !important;

    .tf-quick-view-image {
      width: 100%;
      display: block;

      .wrap-quick-view {
        padding: 15px;
        display: flex;
        direction: ltr;
        height: unset;

        .quickView-item {
          width: 200px;
          flex-shrink: 0;
          border-radius: 0;

          &:not(:last-child) {
            margin-bottom: 0;
            margin-right: 15px;
          }
        }
      }
    }

    .wrap {
      overflow: unset;
      height: unset;
      padding: 20px 15px;
    }
  }

  .form-sticky-atc {
    .tf-sticky-atc-product {
      display: none;
    }

    .tf-sticky-atc-infos {
      // flex-wrap: wrap;
      width: 100%;
      gap: 10px;

      // .tf-sticky-atc-size {
      //     width: 100%;
      //     justify-content: center;
      // }
      .tf-sticky-atc-infos-title {
        display: none;
      }

      .tf-sticky-atc-btns {
        width: calc(100% - 135px);
      }
    }
  }

  .offcanvas-compare .tf-compare-list {
    flex-direction: column;

    .tf-compare-head {
      br {
        display: none;
      }
    }

    .tf-compare-wrap {
      margin: 0;
      padding: 15px 0;
      width: 100%;

      .tf-compare-item {
        flex-direction: column;
        width: 150px;
        gap: 7px;

        .image {
          width: unset;
          height: unset;
          max-height: 168px;
        }
      }
    }

    .tf-compare-buttons {
      width: 100%;

      .tf-compare-buttons-wrap {
        display: flex;
        gap: 10px;
        width: 100%;
        flex-direction: column;

        a {
          font-size: 12px;
          height: 40px;
        }

        .tf-compapre-button-clear-all {
          margin: 0;
          height: 40px;
          font-size: 12px;
        }
      }
    }
  }

  .modal-size-guide .modal-dialog {
    width: 550px;
  }

  .modal-shopping-cart {
    .tf-minicart-recommendations {
      padding: 10px 10px;
      width: 115px;
      display: none;
    }

    .header {
      padding: 12px;
    }

    .tf-mini-cart-threshold {
      margin: 0 12px;
      padding: 12px;
    }

    .tf-mini-cart-item {
      margin: 0 12px;
      padding: 12px 0;
      gap: 12px;

      .tf-mini-cart-info>div {
        gap: 6px;
        margin-bottom: 6px;
      }
    }

    .tf-mini-cart-tool {
      padding: 0 12px;
      height: 40px;

      .tf-mini-cart-tool-btn {
        gap: 6px;

        div {
          font-size: 10px;
          line-height: 20px;
        }
      }
    }

    .tf-mini-cart-bottom-wrap {
      padding: 12px;

      .tf-cart-checkbox {
        margin-bottom: 12px;

        label {
          font-size: 13px;
          line-height: 20px;
        }
      }

      .tf-mini-cart-view-checkout {
        gap: 10px;
        margin-bottom: 12px;

        .tf-btn {
          padding: 8px 14px;
        }
      }
    }

    .tf-mini-cart-tool-openable .tf-mini-cart-tool-content {

      .tf-mini-cart-tool-wrap,
      .tf-mini-cart-tool-text {
        padding: 12px;
      }
    }
  }

  .tf-main-product.full-width {
    flex-direction: column;

    >div {
      width: 100%;
    }
  }

  .tf-slideshow {
    .wrap-slider {
      height: 400px;

      &.h-600 {
        height: 600px;
      }
    }

    .title-display {
      font-size: 30px;
      line-height: 42px;
    }
  }

  .wrap-slider {
    .box-title-slider {
      gap: 4px;
    }

    .content-slider {
      gap: 12px;
    }
  }

  .slider-effect {
    display: flex;
    flex-direction: column-reverse;

    .img-slider {
      width: 100%;
      height: 300px;
    }

    .content-left {
      position: unset;
      padding: 40px 0px;
      text-align: center;
    }
  }

  .tab-reviews {
    .tab-reviews-heading {
      flex-direction: column;
      gap: 20px;
      margin-bottom: 30px;
    }
  }

  .tf-product-info-wrap {
    padding-top: 30px;
  }

  .title-display {
    font-size: 40px;
    line-height: 48px;
  }

  h1 {
    font-size: 36px;
    line-height: 48px;
  }

  h2 {
    font-size: 30px;
    line-height: 36px;
  }

  h3 {
    font-size: 28px;
    line-height: 36px;
  }

  h4 {
    font-size: 24px;
    line-height: 34px;
  }

  h5 {
    font-size: 18px;
    line-height: 24px;
  }

  h6 {
    font-size: 16px;
    line-height: 24px;
  }

  #header {
    .nav-icon {

      .nav-account,
      .nav-wishlist,
      .nav-compare {
        display: none;
      }
    }
  }

  [data-grid="grid-4"] {
    grid-template-columns: repeat(2, 1fr);
  }

  .card-product {

    .countdown-box,
    .size-list,
    .wishlist,
    .compare {
      display: none;
    }
  }

  .flat-countdown-banner {
    .banner-img {
      display: none;
    }
  }

  .flat-img-with-text {
    .banner-right {
      height: 400px;
    }

    .banner-left {
      height: 400px;
    }
  }

  .flat-img-with-text-v3 {
    .banner-left {
      padding-bottom: 40px;
    }
  }

  .flat-with-text-lookbook {
    .lookbook-content {
      margin-top: 30px;
    }
  }

  .new-item {
    flex-direction: column;

    .img-style {
      max-width: 100%;
    }
  }

  .grid-cls-v2 {

    .banner-cls,
    .collection-position-2 {
      .img-style {
        height: 400px;
      }
    }
  }

  .banner-cls-discover {
    .img-style {
      height: 400px;
    }
  }

  .tf-table-page-cart {
    thead {
      display: none;
    }

    .tf-cart-item {
      padding-left: 98px;
      min-height: 140px;
      display: block;
      position: relative;

      &:not(:last-child) {
        margin-bottom: 15px;
      }

      td {
        display: flex;
        align-items: center;
        flex-direction: row;
        flex-wrap: wrap;
        padding: 10px 0;
        margin: 0;

        &:not(:last-child) {
          border-bottom: 1px dashed var(--line);
        }
      }

      .img-box {
        position: absolute;
        top: 0;
        left: 0;
        overflow: hidden;
        margin-bottom: 0;
        padding-bottom: 0;
        border-bottom: none;
        width: 80px;
        max-height: 110px;
      }
    }

    .tf-cart-item td[data-cart-title]:before {
      content: attr(data-cart-title);
      color: var(--main);
      text-align: start;
      flex: 1 1 auto;
    }
  }

  .ip-discount-code {
    .tf-btn {
      padding: 6px 10px;
    }

    input {
      padding: 12px 15px;
      padding-right: 120px;
    }
  }

  .heading-section,
  .heading-section-2 {
    h3 {
      font-size: 24px;
      line-height: 30px;
    }
  }

  .heading-section {
    h3 {
      margin-bottom: 8px;
    }
  }

  .heading-section-2 {
    justify-content: center;
    flex-direction: column;
    align-items: center;
    gap: 8px;
  }

  .sw-dots {
    gap: 4px;
  }

  .blog-detail-wrap>.image,
  .flat-banner-parallax-v3,
  .flat-banner-parallax-v2 {
    background-attachment: scroll;
  }
}

@media only screen and (max-width: 575px) {
  .form-sticky-atc {
    .tf-sticky-atc-infos {
      flex-wrap: wrap;
      justify-content: center;

      .tf-sticky-atc-btns {
        width: 100%;
      }

      .tf-dropdown-sort {
        min-width: 100px;
      }
    }
  }

  .tf-topbar {
    .topbar-left {
      flex-grow: 1;
      justify-content: space-between;
      overflow: auto;

      a {
        white-space: nowrap;
      }
    }
  }

  .section-newsletter form {
    input {
      padding-right: 126px;
    }

    button {
      padding: 12px 16px;
    }
  }

  .coming-soon {
    .coming-soon-inner {
      .content {
        max-width: unset;

        .countdown__timer {
          gap: 32px;
        }

        .countdown__item::after {
          right: -25%;
        }

        .countdown__value {
          font-size: 20px;
        }
      }
    }
  }

  .wg-card-store .card-store-info>ul {
    grid-template-columns: 1fr;
  }

  .product-fixed-price {
    .grid-image-top {
      flex-direction: column;
    }
  }

  .tf-product-modal .modal-dialog {
    max-width: calc(100vw - 30px);
    margin-left: 15px;
    margin-right: 15px;
    align-items: flex-end;

    .modal-content {
      padding: 25px 15px;
    }
  }

  .modal-size-guide .modal-dialog {
    width: unset;
    margin-left: 15px;
    margin-right: 15px;
  }

  .card-product {
    .description {
      display: none;
    }
  }

  .testimonial-item {
    flex-direction: column;

    .img-style {
      max-width: 100%;
      height: 273px;
    }
  }

  .tf-page-checkout {
    .grid-2 {
      grid-template-columns: 1fr;
    }
  }
}