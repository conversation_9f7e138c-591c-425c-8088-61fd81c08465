@use "../abstracts/mixin";

.offcanvas {
  z-index: 3000;
  border: none !important;

  .icon-close-popup {
    @include mixin.transition3;

    &:hover {
      color: var(--primary);
      transform: rotate(90deg);
    }
  }
}

.offcanvas-backdrop {
  background-color: var(--backdrop);
  cursor: url(/images/cursor-close.svg), auto;

  &.show {
    opacity: 1;
  }
}

.overflow-x-auto,
.overflow-y-auto {
  &::-webkit-scrollbar {
    width: 0px;
  }
}

// modal
.modal-backdrop {
  background-color: var(--backdrop);

  &.show {
    opacity: 1;
  }
}

.modal {
  cursor: url(/images/cursor-close.svg), auto;

  .icon-close,
  .icon-close-popup {
    @include mixin.transition3;

    &:hover {
      color: var(--primary);
      transform: rotate(90deg);
    }
  }

  // Right
  &.fullRight {
    .modal-dialog {
      transform: translate(100%, 0);
      min-width: 100%;
      height: 100%;
      margin: 0;
      transition: transform 1s ease-out;

      .modal-content {
        border-radius: 0;
        border: 0;
        margin: auto;
        overflow: hidden;
        position: absolute;
        right: 0;
        bottom: 0;
        top: 0;
        padding: 0;

        .modal-body {
          overflow: auto;
          padding: 0;
          padding-bottom: 30px;
        }
      }
    }

    &.show {
      .modal-dialog {
        transform: none;
        transition: transform 0.4s ease-out;
      }
    }
  }

  // Left
  &.fullLeft {
    .modal-dialog {
      transform: translate(-100%, 0) !important;
      min-width: 100%;
      height: 100%;
      margin: 0;
      transition: all 0.3s !important;

      .modal-content {
        border-radius: 0;
        border: 0;
        margin: auto;
        overflow: hidden;
        position: absolute;
        left: 0;
        bottom: 0;
        top: 0;
        padding: 0;

        .modal-body {
          overflow: auto;
          padding: 0;
          padding-bottom: 30px;
        }
      }
    }

    &.show {
      .modal-dialog {
        transform: translate(0, 0) !important;
      }
    }
  }

  &.fullBottom {
    .modal-dialog {
      transform: translate(0, 100%);
      min-width: 100%;
      height: 100%;
      max-height: unset;
      margin: 0;
      transition: transform 0.3s linear !important;

      .modal-content {
        border-radius: 0;
        border: 0;
        margin: auto;
        overflow: hidden;
        position: absolute;
        right: 0;
        bottom: 0;
        padding: 0;
        max-height: max-content;

        .modal-body {
          overflow: auto;
          padding: 0;
          padding-bottom: 30px;
        }
      }
    }

    &.show {
      .modal-dialog {
        transform: translate(0, 0);
      }
    }
  }

  // modalCentered
  &.modalCentered {
    .modal-dialog {
      transform: translate(0, 0) !important;
    }
  }

  &.fade:not(.show) {
    opacity: 0;
  }

  .modal-content {
    cursor: default !important;
  }
}

.modalDemo {
  .demo-title {
    margin-top: 50px;
    margin-bottom: 44px;
    font-weight: 500;
    text-align: center;
  }

  .modal-dialog {
    max-width: 1540px;
    margin-top: 8px;
    margin-bottom: 8px;
    height: calc(100vh - 16px);
  }

  .modal-content {
    padding: 32px 0px;
    background-color: var(--white);
    width: 100%;
    border-radius: 20px;
    margin: 0 30px;
    max-height: calc(100vh - 60px);
    border: 0;
    cursor: default;
    overflow: hidden;
  }

  .mega-menu {
    padding: 0 32px;
    overscroll-behavior-y: contain;
    overflow-y: auto;

    &::-webkit-scrollbar {
      width: 6px;

      &:hover {
        width: 12px;
        height: 12px;
      }
    }

    &::-webkit-scrollbar-thumb {
      background: var(--line);
      transition: all 0.3s ease;
    }
  }

  .header {
    position: relative;

    .icon-close-popup {
      position: absolute;
      top: 18px;
      right: 0;
      background-color: transparent;
      border: none;
      cursor: pointer;
      color: rgb(134 134 134);
    }
  }
}

.tf-product-modal {
  .modal-dialog {
    max-width: min(625px, 90vw);

    .modal-content {
      padding: 38px 36px 40px;
      margin-left: 0;
      margin-right: 0;
      border: 0;

      .header {
        margin-bottom: 24px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .demo-title {
          margin: 0;
          text-align: start;
          font-size: 26px;
          font-weight: 400;
          line-height: 31px;
        }

        span {
          position: unset;
          color: var(--main);
          cursor: pointer;
          @include mixin.transition3;

          &:hover {
            color: var(--primary);
          }
        }
      }

      h6 {
        font-weight: 600;
        line-height: 24px;
        margin-bottom: 25px;
      }

      .title {
        font-size: 18px;
        font-weight: 600;
        line-height: 22px;
        margin-top: 15px;
      }

      p {
        margin-top: 15px;
        margin-bottom: 20px;
      }
    }
  }

  .tf-social-icon {
    .box-icon {
      width: 40px;
      height: 40px;
      border-radius: 999px;
      font-size: 16px;
      color: var(--white);

      &.social-twiter {
        font-size: 12px;
        background: var(--twitter-cl);
      }

      &.social-facebook {
        background: var(--facebook-cl);
      }

      &.social-instagram {
        background: var(--instagram-cl);
      }

      &.social-tiktok {
        background: var(--tiktok-cl);
      }

      &.social-pinterest {
        background: var(--pinterest-cl);
      }
    }
  }

  .form-share {
    margin-top: 20px;
    position: relative;

    .button-submit {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      right: 0px;

      .tf-btn {
        padding: 10px 18px;
      }
    }

    input {
      padding-right: 80px;
    }
  }
}

#ask_question {
  fieldset {
    label {
      margin-bottom: 5px;
      font-weight: 400;
      color: var(--text);
    }

    input {
      height: 50px;
    }

    margin-bottom: 15px;
  }

  textarea {
    height: 176px;
  }

  button {
    border-radius: 4px;
  }
}

#delivery_return {
  .tf-product-popup-delivery {
    .title {
      font-size: 20px;
      font-weight: 400;
      line-height: 36px;
      margin-bottom: 25px;
      margin-top: 0;
    }

    p {
      color: rgb(134, 134, 134);
      line-height: 22.4px;
      margin-bottom: 10px;

      a {
        color: rgb(134, 134, 134);
        text-decoration: underline;
        text-underline-offset: 3px;

        &:hover {
          color: var(--main);
          text-decoration-thickness: 2px;
        }
      }
    }

    &:not(:last-child) {
      margin-bottom: 20px;
    }
  }
}

#quick_add {
  .modal-dialog {
    max-width: min(466px, 90vw);
  }

  .modal-content {
    margin: 8px;
    padding: 30px 0px 30px;

    >.wrap {
      overflow-y: auto;
      padding: 0px 20px;

      &::-webkit-scrollbar {
        width: 2px;
      }
    }

    .icon-close-popup {
      top: 0px;
      right: 20px;
    }
  }

  .tf-product-info-item {
    margin-bottom: 15px;
    display: flex;
    gap: 18px;
    align-items: center;

    .image {
      img {
        width: 70px;
        height: 98px;
      }
    }

    .content {
      display: flex;
      flex-direction: column;
      gap: 6px;

      a {
        font-size: 20px;
        line-height: 24px;
      }

      .price {
        font-size: 20px;
        line-height: 20px;
      }
    }
  }

  .payment-more-option {
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }
}

.bg-color-beige {
  background: conic-gradient(#c8ad7f 0deg 360deg);
}

.bg-color-black {
  background: conic-gradient(#000000 0deg 360deg);
}

.bg-color-blue {
  background: conic-gradient(#a8bcd4 0deg 360deg);
}

.bg-color-white {
  background: conic-gradient(#ffffff 0deg 360deg);
}

.bg-color-pink {
  background: conic-gradient(#fcc6de 0deg 360deg);
}

.bg-color-brown {
  background: conic-gradient(#977945 0deg 360deg);
}

.bg-color-light-purple {
  background: conic-gradient(#d966d9 0deg 360deg);
}

.bg-color-light-green {
  background: conic-gradient(#caffd6 0deg 360deg);
}

.bg-color-orange {
  background: conic-gradient(#ffa500 0deg 360deg);
}

.bg-color-light-blue {
  background: conic-gradient(#add8e6 0deg 360deg);
}

.bg-color-gray {
  background-color: var(--secondary);
}

.bg-color-beige1 {
  background: rgba(223, 198, 184, 1);
}

.bg-color-grey {
  background-color: rgba(158, 155, 150, 1);
}

.bg-color-red {
  background-color: #dc2a35;
}

// Filter Shop

.canvas-wrapper {
  padding: 0;
  isolation: isolate;
  height: 100%;
  width: 100%;
  max-height: none;
  display: grid;
  grid-auto-rows: auto minmax(0, 1fr) auto;
  align-content: start;
}

.canvas-body {
  background-color: var(--white);
  padding: 15px 20px;
  overscroll-behavior-y: contain;
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 5px;
  }

  &::-webkit-scrollbar-track {
    background-color: var(--bg-scrollbar-track);
  }

  &::-webkit-scrollbar-thumb {
    background: var(--bg-scrollbar-thumb);
    border-radius: 4px;
  }
}

.canvas-sidebar {
  max-width: min(90vw, 360px);

  .canvas-header {
    @include mixin.flex(center, space-between);
    padding: 0px 15px;
    background-color: var(--surface);
    min-height: 40px;

    .icon-close-popup {
      font-size: 16px;
    }
  }

  .canvas-body {
    padding: 15px;

    .sidebar-account {
      padding: 32px 15px;
      border-radius: 12px;

      .account-avatar {
        margin-bottom: 16px;
      }

      .my-account-nav {
        .my-account-nav-item {
          padding: 10px;
          border-radius: 8px;
        }
      }
    }
  }
}

.canvas-compare {
  height: max-content !important;
  z-index: 5000;

  .close-popup {
    position: absolute;
    top: 30px;
    right: 30px;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    cursor: pointer;
  }

  .canvas-body {
    padding: 28px 0;
  }
}

.offcanvas-backdrop {
  z-index: 2000;
}

.modal-shopping-cart {
  .modal-content {
    max-width: 708px !important;
    cursor: default !important;
    display: flex;
    flex-direction: row;
  }

  .tf-minicart-recommendations {
    width: 228px;
    flex-shrink: 0;
    padding: 24px 23px 24px 24px;
    border-right: 1px solid var(--line);
    display: flex;
    flex-direction: column;

    .title {
      margin-bottom: 12px;
    }

    >.wrap-recommendations {
      flex-grow: 1;
      overflow-y: auto;

      &::-webkit-scrollbar {
        width: 0px;
      }
    }

    .list-cart-item {
      &:not(:last-child) {
        padding-bottom: 16px;
        border-bottom: 1px solid var(--line);
        margin-bottom: 16px;
      }

      .image {
        width: 100%;
        height: 100%;
        max-height: 240px;
        margin-bottom: 12px;
        border-radius: 8px;
        overflow: hidden;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .name {
        margin-bottom: 4px;
      }

      .cart-item-bot {
        position: relative;
        overflow: hidden;

        a {
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          transform: translateY(30px);
        }
      }

      .price {
        transform: translateY(0px);
        @include mixin.transition3();
      }

      &:hover {
        .price {
          transform: translateY(-30px);
        }

        .cart-item-bot a {
          transform: translateY(0);
        }
      }
    }
  }

  .header {
    padding: 24px 24px 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .title {
      font-size: 20px;
    }

    .icon-close-popup {
      font-size: 16px;
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: var(--surface);
      border-radius: 50%;
      cursor: pointer;
    }
  }

  .wrap {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
  }

  .tf-mini-cart-threshold {
    margin: 0 24px;
    padding: 16px;
    background-color: rgba(245, 246, 236, 1);
    border-radius: 12px;

    .tf-progress-bar {
      margin-top: 12px;
      margin-bottom: 16px;
      width: 100%;
      background-color: var(--white);
      height: 8px;
      position: relative;

      div {
        height: 100%;
        background: linear-gradient(90deg, #19450f 0%, #3dab25 100%);
        position: relative;
        transition: width 2s ease;
      }

      .icon {
        position: absolute;
        left: 95%;
        top: 50%;
        transform: translateY(-50%);
        width: 32px;
        height: 32px;
        border: 2px solid var(--success);
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: var(--white);
        border-radius: 50%;
        font-size: 20px;
      }
    }
  }

  .tf-mini-cart-wrap {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    position: relative;

    .tf-mini-cart-main {
      flex: 1 1 auto;
      position: relative;

      .tf-mini-cart-sroll {
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
        overflow: auto;

        &::-webkit-scrollbar {
          width: 8px;
        }

        &::-webkit-scrollbar-thumb {
          background: var(--secondary-2);
        }

        &::-webkit-scrollbar-track {
          background: var(--line);
        }
      }
    }

    .tf-mini-cart-bottom {
      box-shadow: 5px 5px 18px 5px rgba(64, 72, 87, 0.15);
      flex-shrink: 0;
    }
  }

  .tf-mini-cart-item {
    margin: 0 24px;
    padding: 20px 0 19px;
    display: flex;
    align-items: center;
    gap: 24px;

    &:not(:last-child) {
      border-bottom: 1px solid var(--line);
    }

    .tf-mini-cart-image {
      width: 100px;
      height: 100px;
      flex-shrink: 0;
      border-radius: 8px;
      overflow: hidden;

      img {
        width: 100%;
        height: 100px;
        object-fit: cover;
      }
    }

    .tf-btn-remove {
      color: var(--critical);
      text-decoration: underline;
      cursor: pointer;
    }
  }

  .tf-mini-cart-tool {
    padding: 0 32px;
    height: 58px;
    display: flex;
    justify-content: center;
    border-bottom: 1px solid var(--line);

    .tf-mini-cart-tool-btn {
      width: 100%;
      height: 100%;
      cursor: pointer;
      display: flex;
      gap: 12px;
      align-items: center;
      justify-content: center;
      font-size: 16px;
      @include mixin.transition3;
    }
  }

  .tf-mini-cart-bottom-wrap {
    padding: 24px 24px 20px;

    .tf-cart-totals-discounts {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 12px;
    }

    .tf-cart-checkbox {
      margin-bottom: 24px;
    }

    .tf-mini-cart-view-checkout {
      display: flex;
      gap: 16px;
      margin-bottom: 16px;
    }
  }
}

.modal-wishlist {
  .modal-content {
    max-width: 540px !important;
    cursor: default !important;
  }

  .wrap {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
  }

  .header {
    padding: 24px 24px 12px 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .tf-mini-cart-wrap {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    position: relative;

    .tf-mini-cart-main {
      flex: 1 1 auto;
      position: relative;

      .tf-mini-cart-sroll {
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
        overflow: auto;

        &::-webkit-scrollbar {
          width: 8px;
        }

        &::-webkit-scrollbar-thumb {
          background: var(--secondary-2);
        }

        &::-webkit-scrollbar-track {
          background: var(--line);
        }
      }
    }
  }

  .tf-mini-cart-item {
    margin: 0 24px;
    padding: 20px 0 19px;
    display: flex;
    align-items: center;
    gap: 24px;

    &:not(:last-child) {
      border-bottom: 1px solid var(--line);
    }

    .tf-mini-cart-image {
      width: 100px;
      height: 100px;
      flex-shrink: 0;
      border-radius: 8px;
      overflow: hidden;

      img {
        width: 100%;
        height: 100px;
        object-fit: cover;
      }
    }

    .tf-btn-remove {
      color: var(--critical);
      text-decoration: underline;
      cursor: pointer;
    }
  }

  .tf-mini-cart-bottom {
    box-shadow: 0px 5px 18px 5px #40485726;
    padding: 32px 24px 20px;
    display: flex;
    flex-direction: column;
    text-align: center;
    gap: 16px;

    .view-all-wishlist {
      border-radius: 4px;
      padding: 16px;
    }
  }
}

.tf-cart-checkbox {
  display: flex;
  align-items: center;
  cursor: pointer;

  .tf-checkbox-wrapp {
    min-width: 1.6rem;
    place-items: center;
    position: relative;
    overflow: hidden;
    display: flex;

    input {
      cursor: pointer;
      display: block;
      width: 18px;
      height: 18px;
      transition: 0.2s ease-in-out;
      background-color: var(--white);
      opacity: 0;

      &:checked+div {
        background-color: var(--main);

        i {
          transform: scale(1);
        }
      }
    }

    div {
      position: absolute;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 18px;
      height: 18px;
      transition: 0.25s ease-in-out;
      z-index: 5;
      border: 1px solid var(--line);
      background-color: var(--white);
      color: var(--white);
      pointer-events: none;

      i {
        font-size: 11px;
        transform: scale(0);
      }
    }
  }

  label {
    font-weight: 400;
    cursor: pointer;

    a {
      text-decoration: underline;
      text-underline-offset: 2px;
    }
  }

  .wrap-content {
    display: none;
  }

  &.check {
    .wrap-content {
      display: block;
    }
  }
}

.tf-mini-cart-tool-openable {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  // transform: translateY(100%);
  transition: transform 0.25s ease-in-out;
  z-index: 70;
  box-shadow: 0px 5px 18px 5px rgba(64, 72, 87, 0.15);
  display: none;

  &.open {
    // transform: translateY(0);
    display: block;

    >.overplay {
      opacity: 1;
      visibility: visible;
    }
  }

  .tf-mini-cart-tool-close {
    cursor: pointer;
  }

  .tf-btn {
    height: 52px;
  }

  .tf-mini-cart-tool-content {
    position: relative;
    z-index: 80;
    background-color: var(--white);

    .tf-mini-cart-tool-text {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 18px 32px 17px;
      border-bottom: 1px solid var(--line);

      .icon {
        display: flex;
      }
    }

    .tf-mini-cart-tool-wrap {
      padding: 16px 24px 20px;
    }

    .tf-cart-tool-btns {
      display: flex;
      flex-direction: column;
      gap: 16px;
      align-items: center;
      margin-top: 16px;

      button {
        border-radius: 4px;
      }
    }
  }

  &.add-note {
    textarea {
      background-color: var(--line);
      color: var(--main);

      &::placeholder {
        color: var(--main);
      }
    }
  }
}

.modal-size-guide {
  .modal-dialog {
    max-width: 1157px;
  }

  .modal-content {
    border: 0;
    padding: 40px;

    .header {
      width: max-content;
      margin-bottom: 32px;

      .icon-close-popup {
        position: absolute;
        top: 20px;
        right: 20px;
        font-size: 16px;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: var(--surface);
        border-radius: 50%;
        cursor: pointer;
      }
    }

    .wrap {
      min-height: 325px;
    }
  }
}

.widget-size {
  display: flex;
  gap: 32px;
  align-items: center;

  .box-title-size {
    display: flex;
    align-items: center;

    .title-size {
      width: 92px;
    }

    .number-size {
      width: 80px;
      display: flex;
      align-items: center;
      gap: 4px;
      padding: 6px 11px;
      border: 1px solid var(--line);
      border-radius: 8px;
    }
  }

  .tow-bar-block {
    position: relative;
    background: var(--line);
    height: 9px;
    border-radius: 0px;

    .progress-size {
      position: absolute;
      height: 9px;
      background: var(--main);
      left: 0;
    }
  }

  .range-input {
    position: relative;
    flex-grow: 1;

    input {
      position: absolute;
      top: 0;
      height: 9px;
      width: 100%;
      background: none;
      outline: none;
      border: none;
      pointer-events: none;
      appearance: none;

      &::-webkit-slider-thumb {
        cursor: pointer;
        width: 24px;
        height: 24px;
        border-radius: 50%;
        border: 3px solid var(--main);
        outline: none;
        pointer-events: auto;
        -webkit-appearance: none;
        background: var(--white);
      }
    }
  }
}

.canvas-search {
  width: 100% !important;
  max-width: 463px;
  padding-top: 55px;
  border: 0 !important;

  .tf-search-head {
    padding: 0 22px;
    border-bottom: 1px solid var(--line);
    margin-bottom: 22px;
    box-shadow: unset;

    .title {
      font-size: 28px;
      line-height: 34px;
      margin-bottom: 19px;
      @include mixin.flex(center, space-between);
    }

    .close {
      width: 30px;
      height: 30px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
    }

    .tf-search-sticky {
      margin-bottom: 30px;
    }
  }

  .tf-search-content {
    padding: 0 22px 16px 22px;
  }

  .tf-search-content-title {
    font-size: 20px;
    line-height: 24px;
    margin-bottom: 30px;
  }

  .tf-col-quicklink {
    margin-bottom: 32px;

    .tf-search-content-title {
      margin-bottom: 14px;
    }

    .tf-quicklink-item a {
      padding: 4px 0;
      line-height: 22.4px;
    }
  }

  .tf-search-hidden-inner {
    padding-top: 5px;
  }

  .tf-loop-item {
    display: flex;
    gap: 19px;
    align-items: flex-start;

    &:not(:last-child) {
      padding-bottom: 16px;
      border-bottom: 1px solid var(--line);
      margin-bottom: 16px;
    }

    .image {
      width: 68px;
      max-height: 95px;
    }

    .tf-product-info-price {
      >div {
        font-size: 14px;
      }
    }
  }
}

.offcanvas-compare {
  height: max-content !important;

  .offcanvas-content {
    box-shadow: 0px 5px 18px 5px rgba(64, 72, 87, 0.15);

    .icon-close-popup {
      position: absolute;
      top: 24px;
      right: 24px;
      font-size: 16px;
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: var(--surface);
      border-radius: 50%;
      cursor: pointer;
    }
  }

  .tf-compare-list {
    display: flex;
    align-items: center;
    gap: 40px;
    padding: 24px 0;

    .tf-compare-wrap {
      display: flex;
      align-items: center;
      flex-grow: 1;
      overflow: auto hidden;
      gap: 48px;
      padding: 24px 0;
      margin: -24px 0;
      padding-right: 20px;
      margin-right: -20px;

      &::-webkit-scrollbar {
        height: 8px;
      }

      &::-webkit-scrollbar-thumb {
        background: var(--secondary-2);
      }

      &::-webkit-scrollbar-track {
        background: var(--line);
      }

      .tf-compare-item {
        width: 263px;
        flex-shrink: 0;
        position: relative;
        padding: 11px;
        border-radius: 12px;
        border: 1px solid var(--line);
        display: flex;
        align-items: center;
        gap: 16px;

        .image {
          flex-shrink: 0;
          width: 92px;
          height: 123px;
          border-radius: 8px;
          overflow: hidden;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }

        .text-title {
          margin-bottom: 8px;
        }

        >.icon-close {
          position: absolute;
          top: 0;
          right: 0;
          transform: translate(50%, -50%);
          display: flex;
          align-items: center;
          justify-content: center;
          width: 32px;
          height: 32px;
          background-color: var(--critical);
          border-radius: 50%;
          font-size: 12px;
          color: var(--white);
          z-index: 5;
          cursor: pointer;
        }

        >.btns-repeat {
          position: absolute;
          top: 50%;
          right: -32px;
          transform: translateY(-50%);
          display: flex;
          cursor: pointer;
        }

        &:last-child {
          >.btns-repeat {
            display: none;
          }
        }
      }
    }

    .tf-compare-buttons {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      width: 220px;
      flex-shrink: 0;

      a {
        height: 52px;
      }

      .tf-compapre-button-clear-all {
        margin-top: 16px;
        height: 48px;
        padding: 13px 0;
        cursor: pointer;
      }
    }
  }
}

.modal-quick-view {
  .modal-content {
    max-width: min(856px, 90vw) !important;
    cursor: default !important;
    display: flex;
    flex-direction: row;
    overflow-y: scroll;
    max-height: 100vh;
  }

  .tf-quick-view-image {
    width: 42.525%;
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    position: relative;

    >.wrap-quick-view {
      flex-grow: 1;
      position: relative;
      padding: 24px;
      padding-right: 0;
      overflow: auto;
      height: 100vh;
      direction: rtl;

      &::-webkit-scrollbar {
        width: 8px;
        height: 8px;
      }

      &::-webkit-scrollbar-track {
        background-color: var(--line);
      }

      &::-webkit-scrollbar-thumb {
        background: var(--secondary-2);
      }
    }

    .quickView-item {
      max-height: 440px;
      height: 100%;
      border-radius: 12px;
      overflow: hidden;

      &:not(:last-child) {
        margin-bottom: 30px;
      }

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }

  .wrap {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    height: 100%;
    padding: 24px 24px 48px;
    overflow-y: auto;

    &::-webkit-scrollbar {
      width: 8px;
      position: absolute;
      left: 0;
    }

    &::-webkit-scrollbar-thumb {
      background: var(--secondary-2);
    }

    &::-webkit-scrollbar-track {
      background: var(--line);
    }

    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .icon-close-popup {
        font-size: 16px;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: var(--surface);
        border-radius: 50%;
        cursor: pointer;
      }
    }
  }
}

.modal-quick-add {
  .modal-content {
    cursor: default !important;
    padding: 24px;
    border: 0;

    .icon-close-popup {
      position: absolute;
      top: 24px;
      right: 24px;
      font-size: 16px;
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: var(--surface);
      border-radius: 50%;
      cursor: pointer;
    }

    .tf-product-info-item {
      margin-bottom: 15px;
      display: flex;
      gap: 18px;
      align-items: center;

      .image {
        img {
          width: 80px;
          height: 100px;
        }
      }

      .content {
        display: flex;
        flex-direction: column;
        gap: 6px;
      }
    }
  }
}

.tf-mini-search-frm {
  position: relative;

  input {
    height: 42px;
    padding: 12px 20px 12px 44px;
    font-size: 16px;
    line-height: 26px;
    color: var(--main);

    &::placeholder {
      font-size: 16px;
      line-height: 26px;
      color: var(--main);
    }
  }

  button {
    position: absolute;
    left: 14px;
    top: 0;
    font-size: 16px;
    margin: 13px 0;
    background-color: transparent;
    border: 0;
    outline: none;
  }
}

.form-sign-in {
  .modal-dialog {
    max-width: 640px;

    .modal-content {
      border: 0;
      padding: 37px 35px;
      border-radius: 3px;
    }
  }

  .header {
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .demo-title {
      font-size: 28px;
      line-height: 33.6px;
    }

    .icon-close-popup {
      padding: 0 6px;
      font-size: 16px;
      cursor: pointer;
    }
  }

  .tf-login-form form {
    >div {
      margin-top: 15px;
    }

    .btn-link {
      margin: 10px 0;
      text-decoration: underline !important;
      text-underline-offset: 3px;
      color: var(--text);

      .icon {
        font-size: 8px;
      }
    }

    .bottom {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 30px;

      .btn-link {
        color: var(--main);
      }
    }
  }
}

.toolbar-shop-mobile {
  max-width: min(90%, 430px) !important;

  .mb-canvas-content {
    max-width: 100%;
    padding-top: 70px;
    padding-left: 0;
  }

  .mb-body {
    padding: 0 20px 0 21px;
  }

  ul.nav-ul-mb>li {
    padding: 0 !important;
    border: 0 !important;
  }

  .tf-category-link {
    gap: 16px;
    min-height: 50px !important;
    padding: 4px 0 6px;
    position: relative;

    .image {
      width: 34px;
      height: 34px;
      position: relative;

      &::before {
        position: absolute;
        z-index: 1;
        content: "";
        top: -3px;
        bottom: -3px;
        left: -3px;
        right: -3px;
        width: calc(100% + 6px);
        height: calc(100% + 6px);
        border: solid 1px var(--line);
        margin: auto;
        pointer-events: none;
        border-radius: 50%;
      }

      img {
        width: 100%;
        height: 100%;
        border-radius: 50%;
      }
    }

    >span:nth-child(2) {
      display: block;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      flex-grow: 1;
    }

    &::after {
      position: absolute;
      bottom: 0;
      content: "";
      height: 1px;
      width: calc(100% - 53px);
      right: 0;
      left: 53px;
      background-color: rgba(0, 0, 0, 0.12);
    }

    .btn-open-sub {
      width: 40px;
      height: 40px;
      border-left: 1px solid var(--line);
    }

    &.current {
      &::after {
        display: none;
      }
    }

    &.has-children:not(.collapsed) {
      &::after {
        display: none;
      }
    }
  }

  .sub-nav-menu {
    margin: 0px 0 17px 50px !important;
    padding: 0 !important;

    .tf-category-link {
      padding: 4px 0 4px 15px;
      margin-bottom: 1px;
      min-height: 30px !important;

      &::after {
        display: none;
      }
    }
  }

  .sub-menu-level-2 {
    margin-left: 65px !important;
  }

  .mb-bottom {
    min-height: 50px;
    clear: both;
    padding: 15px 26px;
    background-color: rgba(0, 0, 0, 0.05);

    a {
      line-height: 13px;
    }
  }

  .list-cate {
    position: relative;

    &.show::after {
      position: absolute;
      bottom: -17px;
      content: "";
      height: 1px;
      width: calc(100% - 53px);
      right: 0;
      left: 53px;
      background-color: rgba(0, 0, 0, 0.12);
    }
  }
}

.canvas-sidebar-blog {
  .canvas-header {
    background-color: var(--white);
    padding: 14px 20px;

    .title {
      font-size: 16px;
      line-height: 19.2px;
      font-weight: 400;
    }
  }

  .canvas-body {
    padding: 20px;
  }
}

.modal-search {
  .icon-close-popup {
    font-size: 16px;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--surface);
    border-radius: 50%;
    cursor: pointer;
  }

  .modal-dialog {
    max-width: 1100px;
  }

  .modal-content {
    margin-right: 15px;
    margin-left: 15px;
    border: 0;
    padding: 40px;
    display: flex;
    flex-direction: column;
    gap: 32px;
  }

  .tf-loading {
    border-radius: 4px;
    border: 1px solid var(--main);
    margin-left: auto;
    margin-right: auto;
    width: 163px;
    height: 52px;
    justify-content: center;

    &::before {
      border-color: var(--white);
    }
  }

  .tf-grid-layout {
    overflow-y: auto;
    padding-right: 34px;
    margin-right: -40px;
    min-height: 100px;
    /* Placeholder để tránh giật */
    transition: height 0.3s ease;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: var(--secondary);
    }
  }
}

.modal-newleter {
  .modal-dialog {
    max-width: 660px !important;
  }

  .modal-content {
    border: 0;

    .modal-top {
      position: relative;
      border-radius: 10px 10px 0px 0px;
      overflow: hidden;

      .icon {
        position: absolute;
        cursor: pointer;
        top: 12px;
        right: 12px;
        width: 40px;
        height: 40px;
        @include mixin.flex(center, center);
        border-radius: 999px;
        background-color: var(--white);
        font-size: 16px;
      }
    }

    .modal-bottom {
      border-radius: 0px 0px 10px 10px;
      background-color: var(--white);
      padding: 40px 15px;
      max-width: 397px;
      margin-left: auto;
      margin-right: auto;

      p {
        margin-bottom: 8px;
      }

      h5 {
        margin-bottom: 28px;
      }
    }

    form {
      max-width: 320px;
      margin-left: auto;
      margin-right: auto;
      margin-bottom: 6px;

      input {
        margin-bottom: 16px;
        height: 40px;
      }

      button {
        height: 40px;
      }
    }
  }
}

.hover-cursor-img {
  .hover-image {
    display: none;
  }
}

.canvas-categories {
  .canvas-header {
    .icon-close {
      font-size: 14px;
    }

    .icon-left {
      font-size: 24px;
    }
  }

  .wd-facet-categories {
    padding: 12px 0px;
    border-bottom: 1px solid var(--line);

    &:first-child {
      padding-top: 0;
    }

    &:last-child {
      border: none;
    }

    .avt {
      width: 32px;
      height: 32px;
      border-radius: 50%;
    }

    .facet-title {
      display: flex;
      align-items: center;
      gap: 16px;

      .title {
        flex-grow: 1;
        font-size: 16px;
        line-height: 24px;
        font-weight: 500;
      }

      .icon {
        font-size: 16px;
      }
    }

    .facet-body {
      margin-top: 12px;
      padding: 8px 10px;

      li {
        .item {
          display: flex;
          align-items: center;
          gap: 8px;
        }

        &:not(:last-child) {
          .item {
            margin-bottom: 16px;
          }
        }
      }
    }
  }
}