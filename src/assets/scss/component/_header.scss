@use "../abstracts/mixin";
@use "../abstracts/variable";

// top bar
.tf-topbar {
    padding: 12px 0px;

    .topbar-left {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    &.has-line-bot {
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    &.style-2 {
        padding: 8px 0px;
    }

    &.topbar-white {
        .top-bar-text {
            color: var(--white);
        }

        .navigation-topbar {
            .icon {
                color: var(--white);
            }
        }
    }

    .wrapper-slider-topbar {
        position: relative;

        .tf-sw-top_bar {
            margin-left: 40px;
            margin-right: 40px;
        }
    }

    .navigation-topbar {
        position: absolute;
        width: 30px;
        height: 30px;
        @include mixin.flex(center, center);
        top: 50%;
        transform: translateY(-50%);

        .icon {
            font-size: 18px;
        }

        &.nav-next-topbar {
            left: 0;
        }

        &.nav-prev-topbar {
            right: 0;
        }
    }
}



.header-default {
    .wrapper-header {
        min-height: 64px;

        .nav-icon {
            gap: 16px;

        }
    }

    .box-nav-ul {
        gap: 20px;
    }

    .wrapper-header-right {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        gap: 16px;

        .tf-currencies {
            padding-right: 20px;
            border-right: 1px solid var(--line);
        }
    }

    .header-bottom {
        .wrapper-header {
            min-height: 58px;
        }

        &.style-2 {
            .wrapper-header {
                min-height: 65px;

                .menu-item {
                    padding: 20px 0px;
                }
            }
        }

        .box-right {
            display: flex;
            gap: 8px;
            align-items: center;
            justify-content: flex-end;

            .icon {
                font-size: 20px;
            }
        }

        .box-left {
            display: flex;
            align-items: center;
        }
    }

    .sub-categories2 {
        position: relative;

        .list-categories-inner {
            left: 101%;
            margin: 0;
            top: 0;
            min-width: 250px;

            &::before {
                height: 70px;
                position: absolute;
                width: 25px;
                top: 0;
                left: -12px;
                bottom: 0;
                content: "";
            }

            &::after {
                content: none !important;
            }
        }

        &:hover {
            .list-categories-inner {
                opacity: 1;
                visibility: visible;
                transform: none;
                pointer-events: all;
            }
        }
    }

    .main-header {
        .wrapper-header {
            min-height: auto;
            padding-top: 22px;
            padding-bottom: 22px;
        }

    }

    .mobile-menu {
        display: inline-flex;

        .icon {
            font-size: 24px;
        }
    }

    .box-support {
        display: flex;
        align-items: center;
        gap: 12px;

        .icon {
            font-size: 32px;
        }
    }

}

.tf-list-categories {
    position: relative;

    .categories-title {
        padding: 14px 16px;
        background-color: var(--main);
        color: var(--header);
        border-radius: 5px;
        display: inline-flex;
        align-items: center;
        gap: 12px;

        .text {
            font-size: 12px;
            line-height: 20px;
            letter-spacing: 1px;
            font-weight: 600;
        }

        .icon {
            font-size: 20px;
        }
    }

    .list-categories-inner {
        left: 0;
        border-radius: 8px;
        position: absolute;
        background-color: var(--white);
        min-width: 300px;
        z-index: 3;
        opacity: 0;
        visibility: hidden;
        transform: translateY(15px);
        @include mixin.transition3;
        text-align: left;
        box-shadow: var(--shadow1);
        top: 100%;
        margin-top: 10px;
        color: var(--main);
        pointer-events: none;
        padding: 8px 0px;

        &::before {
            height: 20px;
            position: absolute;
            top: -15px;
            left: 0;
            right: 0;
            content: "";
        }

    }

    .categories-item {
        padding: 9px 20px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 12px;

        .icon {
            font-size: 20px;
            @include mixin.transition3;
        }

        .inner-left {
            display: flex;
            align-items: center;
            flex-grow: 1;

            .icon {
                font-size: 24px;
                margin-right: 12px;
                color: var(--secondary-2);
            }
        }

        &:hover {
            color: var(--primary);

            .icon {
                color: var(--primary);
            }
        }
    }

    &:hover {
        >.list-categories-inner {
            opacity: 1;
            visibility: visible;
            transform: none;
            pointer-events: all;
        }
    }

    .box-cate-bottom {
        position: relative;
        padding-top: 9px;
        margin-top: 9px;

        &::before {
            content: "";
            position: absolute;
            height: 1px;
            background-color: var(--line);
            top: 0;
            left: 20px;
            right: 20px;
        }
    }

    &.style-1 {
        border: 1px solid var(--line);
        border-radius: 8px;

        .categories-title {
            border-radius: 8px 8px 0 0;
            padding: 13px 20px;
            display: flex;
            background-color: rgba(45, 48, 84, 1);

            .text {
                font-size: 16px;
                font-weight: 600;
                line-height: 26px;
            }
        }

        .categories-item {
            padding: 14px 20px;

            &:hover {
                .icon {
                    color: var(--primary);
                }
            }
        }

        .icon-left {
            font-size: 24px;
        }

        .sub-categories2 {
            position: relative;

            .list-categories-inner {
                left: 100%;
                margin: 0;
            }

            &:hover {
                .list-categories-inner {
                    opacity: 1;
                    visibility: visible;
                    top: 0;
                    pointer-events: all;
                    transform: translate(0);
                }
            }
        }

        ul li {
            &:not(:last-child) {
                border-bottom: 1px solid var(--line);
            }
        }

        >.list-categories-inner {
            position: unset;
            opacity: 1;
            visibility: visible;
            transform: none;
            pointer-events: all;
            margin: 0;
            min-width: unset;
            box-shadow: none;
            padding: 0;

            .icon {
                color: var(--main);
            }
        }
    }
}

#header {
    .box-nav-ul {
        .item-link {
            font-size: 16px;
            line-height: 26px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 4px;
            @include mixin.transition3;
            position: relative;

            .icon {
                font-size: 12px;
                font-weight: 600;
            }

            &::after {
                position: absolute;
                content: "";
                top: 50%;
                left: 50%;
                -webkit-transform: translate(-50%, -50%);
                -moz-transform: translate(-50%, -50%);
                -ms-transform: translate(-50%, -50%);
                -o-transform: translate(-50%, -50%);
                transform: translate(-50%, -50%);
                width: calc(100% + 40px);
                height: 82px;
                display: none;
            }

        }

        .menu-item {

            &.active,
            &:hover {
                .item-link {
                    color: var(--active-link);
                }
            }

            &.active {
                .demo-item.active {
                    .demo-name {
                        color: var(--active-link);
                    }
                }
            }

            &:hover {
                .item-link {
                    &::after {
                        display: block;
                    }
                }
            }
        }
    }

    .nav-icon {
        .icon {
            font-size: 24px;
            @include mixin.transition3;
        }

        >li {
            display: inline-flex;
        }

        .nav-icon-item {
            position: relative;
            display: inline-flex;

            svg path {
                @include mixin.transition3;
            }

            &:hover {
                svg path {
                    stroke: var(--primary);
                }

                .icon {
                    color: var(--primary);
                }
            }

            .text {
                font-size: 16px;
                line-height: 25.6px;
                font-weight: 500;
                display: inline-block;
            }
        }

        .count-box {
            position: absolute;
            line-height: 14px;
            height: 14px;
            min-width: 14px;
            text-align: center;
            padding: 0 3px;
            font-size: 10px;
            font-weight: 500;
            border-radius: 9999px;
            white-space: nowrap;
            text-overflow: ellipsis;
            display: block;
            overflow: hidden;
            z-index: 2;
            top: -2px;
            right: -2px;
            color: var(--white);
            background-color: var(--primary);
        }

    }

    .tf-product-header {
        .card-product {
            .box-icon {
                width: 32px;
                height: 32px;
                min-width: 32px;

                .icon {
                    font-size: 20px;
                }
            }

            .btn-main-product {
                padding: 5px 10px;
            }

        }
    }

    .sub-menu {
        .hover-sw-nav {
            .nav-sw {
                width: 36px;
                height: 36px;
                top: 38%;
            }
        }

        .list-color-item {
            .tooltip {
                display: none;
            }
        }
    }
}

.header-absolute {
    margin-bottom: -64px;
    background-color: transparent;
    z-index: 999;
}

.header-white {
    .box-nav-ul {
        .item-link {
            color: var(--header);
        }
    }

    .nav-icon {
        .icon {
            color: var(--header);
        }

        svg path {
            stroke: var(--header);
        }
    }

    .mobile-menu {
        .icon {
            color: var(--header);
        }
    }

    &.header-bg {
        background-color: var(--main);
    }
}

.logo-header img {
    width: 144px;
}

.box-nav-ul {
    .menu-item {

        padding: 28px 0px;

        &:hover {
            >.sub-menu {
                pointer-events: all;
                opacity: 1;
                visibility: visible;
                transform: translateX(0px) translateY(0px);
            }
        }
    }

    .menu-item-2 {
        position: relative;
        cursor: pointer;

        &::after {
            position: absolute;
            content: "\e906";
            font-family: variable.$fontIcon;
            font-size: 11px;
            right: 0;
            top: 50%;
            transform: translateY(-50%);
        }

        .sub-menu {
            top: -22px;
            left: 110%;
            min-width: 200px;

            &::after {
                position: absolute;
                display: block;
                content: "";
                width: 60px;
                height: 45px;
                background-color: transparent;
                left: -18%;
                top: 3px;
            }

        }

        &:hover {
            >.sub-menu {
                pointer-events: all;
                opacity: 1;
                visibility: visible;
                transform: translateX(0px) translateY(0px);
            }
        }
    }

    .sub-menu {
        pointer-events: none;
        position: absolute;
        background-color: var(--white);
        min-width: 200px;
        z-index: 999;
        visibility: hidden;
        text-align: left;
        padding: 8px 20px;
        top: 100%;
        opacity: 0;
        visibility: hidden;
        border-radius: 8px;
        transform: translateX(0px) translateY(10px);
        box-shadow: var(--shadow1);
        @include mixin.transition3;
    }

    .wrapper-sub-shop {
        margin-left: -10px;

        .menu-heading {
            margin-bottom: 15px;
        }
    }

    .mega-menu {
        padding-top: 32px;
        padding-bottom: 36px;
        border: none;
        max-height: calc(100vh - 74px);
        overflow: auto;
        left: 0;
        right: 0;

        .card-product {
            .card-product-wrapper {
                max-height: 290px;
            }
        }

        .wrap-sw-over {
            padding-bottom: 40px;
            margin-bottom: -40px;
        }

    }

    .menu-heading {
        font-size: 12px;
        line-height: 20px;
        font-weight: 700;
        color: var(--main);
        text-transform: uppercase;
        margin-bottom: 8px;
        letter-spacing: 1px;
    }

    .menu-list {
        .menu-link-text {
            padding: 7px 0px;
            color: var(--secondary);
            text-transform: capitalize;
            position: relative;

            &::before {
                content: "";
                width: 0;
                height: 1px;
                top: 80%;
                position: absolute;
                left: auto;
                right: 0;
                z-index: 1;
                -webkit-transition: width 0.4s cubic-bezier(0.25, 0.8, 0.25, 1) 0s;
                -o-transition: width 0.4s cubic-bezier(0.25, 0.8, 0.25, 1) 0s;
                transition: width 0.4s cubic-bezier(0.25, 0.8, 0.25, 1) 0s;
                background: var(--main);
            }

            &:hover {
                color: var(--main);

                &::before {
                    width: 100%;
                    left: 0;
                    right: auto;
                }
            }

            &.active {
                color: var(--main);

                &::before {
                    width: 100%;
                    left: 0;
                    right: auto;
                }
            }
        }

        .demo-label {
            top: -14px;
            right: -35px;
        }
    }

    .submenu-default {
        left: -30px;
    }

    .view-all-demo {
        .tf-btn {
            padding: 10px 28px;
        }
    }

    .sec-cls-header {
        margin-top: 15px;
        padding-bottom: 78px;
    }

    .collection-position {
        .content {
            gap: 28px;

            .title-top {
                display: grid;
                gap: 8px;
            }
        }
    }
}

.mega-menu {
    .row-demo {
        display: grid;
        grid-template-columns: repeat(6, 1fr);
        gap: 20px;
        overflow-y: auto;
        padding-right: 10px;
        margin-right: -16px;

        &::-webkit-scrollbar {
            width: 6px;
        }

        &::-webkit-scrollbar-thumb {
            background: var(--line);
        }
    }

    .demo-item {

        border-radius: 8px;
        background-color: var(--surface);
        padding: 8px 8px 0px;
        border: solid 1px var(--line);
        transition: border 0.4s;
        margin-bottom: 1px;
        display: inline-flex;

        .demo-name {
            font-size: 14px;
            line-height: 42px;
            display: block;
            text-wrap: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            text-align: center;
            font-weight: 600;
            @include mixin.transition3;

            &:hover {
                color: var(--primary);
            }
        }

        &:hover {
            border-color: var(--main);
        }
    }

    .view-all-demo {
        margin-top: 28px;
    }

    .collection-item {
        .collection-title {
            background-color: #f2f2f2;
            border-radius: 3px;
        }

        .collection-content {
            bottom: 40px;
        }

        .tf-btn {
            .icon {
                margin: 0px;
            }
        }
    }
}

.mega-page {
    max-width: 900px;
    margin: auto;
}

.demo-label {
    position: absolute;
    top: 9px;
    right: 7px;
    gap: 5px;
    display: flex;

    span {
        font-size: 10px;
        line-height: 19px;
        padding: 0 8px;
        background-color: rgb(131 183 53);
        color: var(--white);
        border-radius: 3px;
        @include mixin.flex(center, center);
    }

    .demo-new {
        background-color: rgb(72 212 187);
    }

    .demo-hot {
        background-color: rgb(252 87 50);
    }
}

.canvas-mb {
    width: 100% !important;
    max-width: min(90%, 320px);
    border-right: 0 !important;

    .mb-canvas-content {
        padding-top: 60px;
        min-width: 100%;
        max-width: min(90%, 320px);
        grid-auto-rows: minmax(0, 1fr) auto;

        isolation: isolate;
        height: 100%;
        width: 100%;
        display: grid;
        align-content: start;
    }

    .mb-body {
        padding-right: 20px;
        padding-left: 20px;
        padding-bottom: 20px;
        border-bottom: 1px solid var(--line);
        overscroll-behavior-y: contain;
        overflow-y: auto;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
    }

    .icon-close-popup {
        position: absolute;
        font-size: 16px;
        z-index: 3;
        top: 20px;
        left: 15px;
        background-color: transparent;
        border: none;
        height: 30px;
        width: 30px;
        padding: 0;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        color: var(--main);
    }

    .mb-bottom {
        padding-left: 20px;
        padding-right: 20px;

        .site-nav-icon {
            margin-bottom: 18px;
        }

        .bottom-bar-language {
            min-height: 40px;
            max-width: 100%;
            display: grid;
            grid-template-columns: 1fr 1fr;

            .image-select.type-currencies>.dropdown-menu {
                margin-left: 0 !important;
            }

            .image-select>.dropdown-menu {
                &::before {
                    display: none;
                }
            }

            .tf-currencies,
            .tf-languages {
                @include mixin.flex(center, center);
            }

            .tf-currencies {
                border-right: 1px solid var(--line);
            }
        }
    }

    .site-nav-icon {
        padding: 0 9px;
        line-height: 40px;
        border: solid 1px var(--line);
        gap: 4px;
        background-color: var(--line);
        color: var(--main);
        border-radius: 8px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        font-weight: 500;

        .icon {
            font-size: 14px;
        }

        svg path {
            @include mixin.transition3;
        }

        &:hover {
            color: var(--white);
            background-color: var(--main);
            border-color: var(--main);

            svg path {
                stroke: var(--white);
            }
        }
    }

    .mb-other-content {
        .group-icon {
            margin-top: 10px;
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 20px;

        }

        .text-need {
            font-weight: 500;
            margin-bottom: 12px;
            font-size: 16px;
            line-height: 24px;
            font-weight: 500;
        }

        .mb-info {
            li {
                font-size: 14px;
                line-height: 22px;
                display: flex;
                align-items: center;

                .icon {
                    font-size: 20px;
                    margin-right: 12px;
                }

                &:not(:last-child) {
                    margin-bottom: 8px;
                }
            }
        }

        .mb-contact {
            display: grid;
            gap: 8px;
            margin-bottom: 12px;

            .tf-btn-default i {
                font-size: 16px;
            }
        }
    }

    .form-search {
        margin-bottom: 8px;

        input {
            padding: 12px;
            padding-left: 44px;
            font-size: 14px;
            line-height: 22px;
            color: var(--secondary-2);
            border-width: 1px;
        }

        button {
            right: auto;
            left: 12px;
        }

    }
}

.nav-ul-mb {
    .nav-mb-item {
        padding: 2px 0px;

        &:not(:last-child) {
            border-bottom: 1px solid var(--line);
        }

        .mb-menu-link {
            min-height: 48px;
            font-weight: 500;
            font-size: 16px;
            line-height: 24px;
            color: var(--main);
            display: flex;
            align-items: center;
            justify-content: space-between;

            &:not(.collapsed) {
                .btn-open-sub {
                    &::before {
                        transform: rotate(90deg);
                    }
                }
            }
        }

        &.active {
            .mb-menu-link {
                font-weight: 600;
            }

            .sub-nav-link.active {
                font-weight: 600;
            }
        }
    }

    .btn-open-sub {
        position: relative;
        width: 20px;
        height: 30px;
        display: inline-flex;
        align-items: center;
        justify-content: center;

        &:after,
        &::before {
            content: "";
            position: absolute;
            z-index: 1;
            top: 0;
            bottom: 0;
            left: 0;
            right: 0;
            background-color: var(--main);
            transition: 0.4s ease 0.1s;
            margin: auto;
        }

        &::before {
            width: 2px;
            height: 12px;
        }

        &::after {
            width: 12px;
            height: 2px;
        }
    }

    .sub-nav-menu {
        padding-left: 10px;
        margin-bottom: 15px;
    }

    .sub-menu-level-2 {
        margin-bottom: 5px;
    }

    .sub-nav-link {
        display: flex;
        align-items: center;
        justify-content: space-between;
        min-height: 36px;
        line-height: 36px;
        font-size: 14px;
        color: var(--secondary);

        &:not(.collapsed) {
            .btn-open-sub {
                &::before {
                    transform: rotate(90deg);
                }
            }
        }

        .btn-open-sub {

            &::after,
            &::before {
                background-color: var(--secondary);
            }
        }

        &.line-clamp {
            position: relative;
            display: inline-flex;

            .demo-label {
                top: -5px;
                right: -38px;
            }
        }
    }

}

.header-list-categories {
    display: flex;
    align-items: center;
    gap: 26px;

    .categories-item {
        position: relative;
        display: flex;
        align-items: center;

        a {
            font-weight: 600;
            font-size: 16px;
            line-height: 26px;
            transition: all 0.2s;
            padding: 4px 0px;

            &::after {
                content: "";
                position: absolute;
                width: 0;
                bottom: 0;
                height: 2px;
                left: 50%;
                transform: translateX(-50%);
                -webkit-transition: all 0.2s;
                -ms-transition: all 0.2s;
                transition: all 0.2s;
                background-color: var(--main);
            }
        }

        &.active,
        &:hover {
            a {
                &::after {
                    width: 100%;
                }
            }
        }
    }
}

.header-style-2 {
    .logo-header {
        margin-left: 30px;

        img {
            width: 130px;
        }
    }
}

.header-style-4 {
    .main-header .wrapper-header {
        padding-bottom: 26px;
    }

    .wrapper-header-right {
        .tf-currencies {
            padding-right: 17px;
        }

        .image-select.style-default>.dropdown-toggle {
            padding-right: 27px;
        }
    }
}

.header-style-5 {
    .main-header {
        .wrapper-header {
            min-height: 68px;
            padding-top: 12px;
            padding-bottom: 12px;
        }
    }

    .logo-header img {
        width: 130px;
    }

    .wrapper-header-left {
        .form-search-select {
            margin-left: 60px;
        }
    }

    .header-bottom {
        .wrapper-header {
            min-height: 50px;

        }

        .box-nav-ul {
            margin-left: 40px;

            .menu-item {
                padding: 12px 0px;
            }
        }

        .btn-select {
            font-size: 16px;
            line-height: 24px;
            font-weight: 500;
            color: var(--white);
        }

        .tf-dropdown-sort {
            padding: 5px 0px;
            min-width: 146px;
            border: none;

            .dropdown-menu {
                margin-top: 6px !important;
            }
        }
    }

    .tf-list-categories {
        .categories-title {
            background-color: transparent;
            padding: 4px 0px;
            gap: 8px;
            padding-right: 40px;
            border-right: 1px solid rgba(233, 233, 233, 0.1);

            .text {
                font-size: 16px;
                line-height: 26px;
                letter-spacing: 0;
            }

            .icon-left {
                font-size: 24px;
            }

            .icon {
                font-size: 17px;
            }
        }

        .list-categories-inner {
            margin-top: 8px;

            &::after {
                content: "";
                position: absolute;
                bottom: calc(100% - 16px);
                left: 26px;
                border-top: 20px solid var(--white);
                border-right: 20px solid transparent;
                -webkit-transform: rotate(45deg);
                transform: rotate(45deg);
            }
        }
    }


}

.header-bottom .box-nav-ul .menu-item {
    padding: 16px 0px;
}

.wrapper-header-left {
    display: flex;
    align-items: center;

    .box-navigation {
        padding-left: 60px;
    }

}


header {
    position: sticky;
    position: -webkit-sticky;
    left: 0;
    right: 0;
    -webkit-transition: 0.2s ease-out;
    -o-transition: 0.2s ease-out;
    transition: 0.2s ease-out;
    z-index: 888;
    background-color: var(--white);

    &.header-bg {
        background-color: var(--white);
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

    }

}

.header-scroll-white {
    .dark-logo {
        display: none;
    }

    &.header-bg {
        background-color: var(--white);

        .dark-logo {
            display: block;
        }

        .white-logo {
            display: none;
        }

        .box-nav-ul .item-link,
        .nav-icon .nav-icon-item {
            color: var(--main);
        }

        .box-nav-ul .item-link::before {
            background-color: var(--main) !important;
        }

        .btn-mobile {
            svg path {
                fill: var(--main);
            }
        }

    }
}

.header-dark {
    background-color: var(--main);

    .box-nav-ul {
        .item-link {
            color: var(--white);
        }
    }

    .header-list-categories {
        .categories-item {
            a {
                color: var(--white);
                font-size: 12px;
                line-height: 22px;

                &::after {
                    background-color: var(--white);
                    height: 1px;
                }
            }
        }
    }
}

.header-fullwidth {
    padding-left: 15px;
    padding-right: 15px;
}

.nav-account {
    position: relative;

    .dropdown-account {
        pointer-events: none;
        position: absolute;
        background-color: var(--white);
        min-width: 200px;
        z-index: 999;
        visibility: hidden;
        text-align: left;
        top: calc(100% + 28px);
        opacity: 0;
        visibility: hidden;
        border-radius: 8px;
        transform: translateX(0px) translateY(15px);
        box-shadow: var(--shadow1);
        @include mixin.transition3;
        left: -100px;
        padding: 8px 28px;

        .list-menu-item {
            li {
                &:not(:last-child) {
                    margin-bottom: 8px;

                    a {
                        border-bottom: 1px solid var(--line);
                    }
                }
            }

            a {
                padding: 12px 0px;
                display: block;
            }
        }
    }

    &:hover {
        .dropdown-account {
            pointer-events: all;
            opacity: 1;
            visibility: visible;
            transform: translateX(0px) translateY(0px);
        }

        .nav-icon-item {
            &::after {
                display: block;
            }
        }

    }

    .dropdown-login {
        left: -200px;
        min-width: 290px;
        padding: 24px;

        .tf-btn {
            border-radius: 4px;
            width: 100%;
            font-size: 12px;
            line-height: 20px;
            text-transform: uppercase;
            font-weight: 600;
            letter-spacing: 0.1em;
        }

        .sub-top {
            display: grid;
            gap: 12px;
            padding-bottom: 16px;
            border-bottom: 1px solid var(--line);
        }

        .sub-bot {
            padding: 16px 0px;
        }
    }

    .nav-icon-item {
        position: relative;

        &::after {
            position: absolute;
            content: "";
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 40px;
            height: 82px;
            display: none;
        }
    }
}

.modalDemo {
    .demo-item.active {
        .demo-name {
            color: var(--primary);
        }
    }
}