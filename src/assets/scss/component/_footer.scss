@use "../abstracts/mixin";

footer {
    .footer-wrap {
        border-top: 1px solid var(--line);
    }
    .footer-body {
        padding: 80px 0;
    }
    .footer-bottom-wrap {
        padding-top: 14px;
        border-top: 1px solid var(--line);
        padding-bottom: 17px;
        display: flex;
        gap: 15px 30px;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        .left {
            display: flex;
            gap: 15px 40px;
            align-items: center;
            flex-wrap: wrap;
        }
    }
    .footer-infor {
        display: flex;
        flex-direction: column;
        gap: 16px;
        .footer-logo {
            > a {
                display: flex;
            }
        }
    }
    .footer-address {
        display: flex;
        flex-direction: column;
        gap: 8px;
    }
    .tf-btn-default {
        letter-spacing: 0.1em;
        padding-bottom: 2px;
    }
    .footer-info {
        display: flex;
        flex-direction: column;
        gap: 8px;
        li {
            display: flex;
            align-items: center;
            gap: 12px;
            i {
                color: var(--main);
                font-size: 20px;
            }
        }
    }
    .footer-menu {
        display: flex;
        gap: 15px;
        .footer-menu_item {
            color: var(--secondary);
            &:hover {
                color: var(--primary);
            }
        }
        .footer-menu-list {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
    }
    .footer-newsletter {
        display: flex;
        flex-direction: column;
        gap: 16px;
    }
    .tf-payment ul img {
        width: 38px;
        height: 24px;
    }
    p {
        color: var(--secondary);
    }
    .footer-heading {
        margin-bottom: 12px;
    }
    &.bg-main {
        .footer-wrap {
            border: 0;
        }
        .footer-heading {
            color: var(--white);
        }
        .footer-info {
            li i {
                color: var(--white);
            }
        }
        .tf-cart-checkbox {
            a {
                color: var(--white);
            }
            label {
                color: var(--secondary-2);
            }
        }
        p {
            color: var(--secondary-2);
        }
        .footer-newsletter {
            p a {
                color: var(--white);
            }
        }
        .footer-menu .footer-menu_item {
            color: var(--secondary-2);
            &:hover {
                color: var(--primary);
            }
        }
        .footer-bottom-wrap {
            border-color: rgba(255, 255, 255, 0.1);
        }
        .footer-heading-mobile::after,
        .footer-heading-mobile::before {
            background-color: var(--white);
        }
    }
    &.has-pb {
        padding-bottom: 98px;
    }
}

.tf-payment {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
    ul {
        display: flex;
        align-items: center;
        gap: 8px;
        flex-wrap: wrap;
        img {
            width: 36px;
        }
    }
}

.tf-cur {
    display: flex;
    gap: 20px;
}

.tf-toolbar-bottom {
    display: none; 
    padding: 15px 0px;
    overflow-x: auto;
    overflow-y: hidden;
    position: fixed;
    z-index: 60;
    bottom: 0;
    right: 0;
    left: 0;
    background-color: var(--white);
    box-shadow: var(--shadow2);
    .toolbar-item {
        flex: 1 0 20%;
        position: relative;
        a {
            width: 100%;
            padding-right: 10px;
            padding-left: 10px;
            height: 40px;
            gap: 4px;
            @include mixin.flex(center,center);
            flex-direction: column;
            .toolbar-icon {
                
                position: relative;
                i {
                    font-size: 20px;
                    color: var(--main);
                }
                .toolbar-count {
                    position: absolute;
                    top: -4px;
                    right: -8px;
                    width: 16px;
                    height: 16px;
                    @include mixin.flex(center,center);
                    background-color: var(--primary);
                    border-radius: 50%;
                    font-size: 10px;
                    font-weight: 500;
                    line-height: 18px;
                    color: var(--white); 
                }
            }
            .toolbar-label {
                font-size: 12px;
                font-weight: 500;
                line-height: 16px;
                color: var(--secondary);
                @include mixin.transition3;
            }
            &:hover{
                .toolbar-label{
                    color: var(--main);
                    font-weight: 600;
                }
            }
        }
        
    }

}