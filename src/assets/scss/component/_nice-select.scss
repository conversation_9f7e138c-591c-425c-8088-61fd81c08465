@use "../abstracts/mixin";

.nice-select {
  -webkit-tap-highlight-color: transparent;
  background-color: #fff;
  border: 0;
  padding: 0;
  padding-right: 16px;
  box-sizing: border-box;
  clear: both;
  cursor: pointer;
  display: flex;
  font-size: 14px;
  line-height: 22px;
  font-weight: 500;
  outline: none;
  position: relative;
  transition: all linear 0.2s;
  user-select: none;
  white-space: nowrap;
  width: max-content;
  border-radius: 0;
  color: var(--main);
}

.nice-select:active,
.nice-select.open,
.nice-select:focus {
  border-color: var(--line);
}

.nice-select:after {
  border-bottom: 1.7px solid var(--main);
  border-right: 1.7px solid var(--main);
  content: "";
  height: 8px;
  width: 8px;
  margin-top: -6px;
  pointer-events: none;
  position: absolute;
  right: 0;
  top: 50%;
  -webkit-transform-origin: 66% 66%;
  -ms-transform-origin: 66% 66%;
  transform-origin: 66% 66%;
  -webkit-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  transform: rotate(45deg);
  -webkit-transition: all 0.15s ease-in-out;
  transition: all 0.15s ease-in-out;
}

.nice-select.open:after {
  -webkit-transform: rotate(-135deg);
  -ms-transform: rotate(-135deg);
  transform: rotate(-135deg);
  -moz-transform: rotate(-135deg);
  -o-transform: rotate(-135deg);
}

.nice-select.open .list {
  opacity: 1;
  z-index: 10;
  pointer-events: auto;
  -webkit-transform: scale(1) translateY(0);
  -ms-transform: scale(1) translateY(0);
  transform: scale(1) translateY(0);
  width: 100%;
  -moz-transform: scale(1) translateY(0);
  -o-transform: scale(1) translateY(0);
}

.nice-select.disabled {
  border-color: #ededed;
  color: #999;
  pointer-events: none;
}

.nice-select.disabled:after {
  border-color: #cccccc;
}

.nice-select.wide {
  width: 100%;
}

.nice-select.wide .list {
  left: 0 !important;
  right: 0 !important;
}

.nice-select.right {
  float: right;
}

.nice-select.right .list {
  left: auto;
  right: 0;
}

.nice-select.small {
  font-size: 12px;
  height: 36px;
  line-height: 34px;
}

.nice-select.small:after {
  height: 4px;
  width: 4px;
}

.nice-select.small .option {
  line-height: 34px;
  min-height: 34px;
}

.nice-select .list {
  background-color: var(--white);
  border-radius: 5px;
  box-shadow: 0 0 0 1px rgba(68, 68, 68, 0.11);
  box-sizing: border-box;
  margin-top: 4px;
  opacity: 0;
  overflow: hidden;
  padding: 0;
  pointer-events: none;
  position: absolute;
  top: 100%;
  left: 0;
  -webkit-transform-origin: 50% 0;
  -ms-transform-origin: 50% 0;
  transform-origin: 50% 0;
  -webkit-transform: scale(0.75) translateY(-21px);
  -ms-transform: scale(0.75) translateY(-21px);
  transform: scale(0.75) translateY(-21px);
  -webkit-transition: all 0.2s cubic-bezier(0.5, 0, 0, 1.25),
    opacity 0.15s ease-out;
  transition: all 0.2s cubic-bezier(0.5, 0, 0, 1.25), opacity 0.15s ease-out;
  z-index: 9;
  width: 100%;
  font-size: 14px;
  max-height: 155px;
  overflow: auto;
}

.nice-select .list.style {
  max-height: unset;
}

.nice-select .list::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgb(0 0 0 / 30%);
  background-color: #f5f5f5;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  border-radius: 5px;
  -webkit-backface-visibility: hidden;
  -moz-backface-visibility: hidden;
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
}
.nice-select .list::-webkit-scrollbar-thumb {
  background-color: #a7a7a7;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  border-radius: 5px;
  -webkit-backface-visibility: hidden;
  -moz-backface-visibility: hidden;
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
}

.nice-select .list::-webkit-scrollbar {
  width: 6px;
  height: 4px;
  background-color: #f5f5f5;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  border-radius: 5px;
  -webkit-backface-visibility: hidden;
  -moz-backface-visibility: hidden;
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
}

.nice-select .option {
  cursor: pointer;
  font-weight: 500;
  line-height: 40px;
  list-style: none;
  min-height: 40px;
  outline: none;
  padding-left: 18px;
  padding-right: 29px;
  font-size: 16px;
  text-align: left;
  -webkit-transition: all 0.2s;
  transition: all 0.2s;
  color: var(--main);
}

.nice-select .option:hover,
.nice-select .option.focus,
.nice-select .option.selected.focus {
  background-color: var(--white);
  color: var(--primary);
}
.nice-select .option.selected {
  font-weight: 600;
}
.nice-select .option.disabled {
  color: var(--main);
  cursor: default;
}

.no-csspointerevents .nice-select .list {
  display: none;
}

.no-csspointerevents .nice-select.open .list {
  display: block;
}

// image select
@import url(/css/bootstrap-select.min.css);

.image-select {
  &.style-default {
    width: unset !important;
    display: flex;
    > select {
      display: none !important;
    }
    > .dropdown-toggle {
      padding: 0;
      padding-right: 20px;
      background-color: transparent !important;
      border: 0 !important;
      outline: none !important;
      color: var(--main);
      &::after {
        border: 0;
        position: absolute;
        right: 0;
        content: "\e935";
        font-family: "icomoon";
        font-size: 12px;
        color: var(--main);
      }
    }
    .filter-option-inner-inner {
      @include mixin.flex(center, flex-start);
      gap: 8px;
      font-weight: 400;
      font-size: 14px;
      line-height: 22px;
      img {
        width: 20px;
        height: 15px;
      }
    }
    > .dropdown-menu {
      overflow: unset !important;
      margin-top: 17px !important;
      margin-bottom: 17px !important;
      padding: 15px 20px;
      border-radius: 0;
      border: 0;
      background-color: var(--white);
      box-shadow: rgba(0, 0, 0, 0.2) 0px 0px 18px 0px;
      a {
        padding: 5px 0;
        .text {
          @include mixin.flex(center, flex-start);
          gap: 5px;
          font-weight: 400;
          font-size: 14px;
          line-height: 16px;
          img {
            width: 16px;
            height: 12px;
          }
        }
        &:hover,
        &:active,
        &.active {
          color: var(--primary) !important;
          background-color: unset !important;
        }
      }
      &::after {
        position: absolute;
        content: "";
        width: 16px;
        height: 16px;
        transform: translate(-50%, -50%) rotate(45deg);
        background-color: var(--white);
        top: 0;
        left: 50%;
        z-index: 2;
      }
      &[data-popper-placement="top-start"] {
        &::after {
          display: none;
        }
        &::before {
          position: absolute;
          content: "";
          width: 16px;
          height: 16px;
          transform: translate(-50%, 50%) rotate(45deg);
          background-color: var(--white);
          bottom: 00%;
          left: 50%;
          z-index: 2;
        }
      }
    }
  }
  &.type-currencies {
    > .dropdown-menu {
      width: 120px !important;
    }
  }
  &.type-languages {
    > .dropdown-menu {
      width: 96px !important;
    }
  }
  &.color-secondary-2 {
    > .dropdown-toggle {
      color: var(--secondary-2);
      &::after {
        color: var(--secondary-2);
      }
      .filter-option {
        .filter-option-inner {
          color: var(--secondary-2);
        }
      }
    }
  }
  &.color-white {
    > .dropdown-toggle {
      color: var(--white);
      &::after {
        color: var(--white);
      }
      .filter-option {
        .filter-option-inner {
          color: var(--white);
        }
      }
    }
  }
}
