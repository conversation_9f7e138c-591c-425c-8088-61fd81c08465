@use "../abstracts/mixin";
@use "../abstracts/variable";


form {
    position: relative;
    .cols {
        display: flex;
        gap: 20px 16px;
        width: 100%;
        > * {
            width: 100%;
        }
    }
}

.form-leave-comment {
    >.wrap {
        display: flex;
        flex-direction: column;
        gap: 20px;
        margin-bottom: 24px;
    }
    button {
        border: 1px solid var(--main);
    }
}

.form-newsletter {
    input {
        height: 56px;
        border-radius: 999px;
        border: 1px solid var(--main);
        padding-right: 56px;
    }
    button {
        position: absolute;
        top: 4px;
        right: 4px;
        width: 48px;
        height: 48px;
        border-radius: 50%;
        font-size: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0;
        border: 1px solid var(--main);
    }
    &.style-black {
        input {
            background-color: var(--main);
            border: 1px solid var(--white);
            color: var(--white);
        }
        button {
            background-color: var(--white);
            color: var(--main);
            &:hover {
                color: var(--white);
                background-color: var(--main);
                border: 1px solid var(--white);
            }
        }
    }
}

.form-search {
    input {
        padding-right: 40px;
        padding-left: 14px;
    }
    button {
        position: absolute;
        right: 16px;
        top: 50%;
        transform: translateY(-50%);
        padding: 0;
        background-color: transparent;
        border: 0;
        &:hover {
            svg path {
                stroke: var(--primary);
            }
        }
    }
}

// dropdown select
.tf-dropdown-sort {
    border-radius: 4px;
    padding: 5px 8px;
    min-width: 100px;
    border: 2px solid var(--line);
    cursor: pointer;
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -ms-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
    transition: all 0.3s ease;
    .icon{
        font-size: 14px;
    }
    .btn-select {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 4px;
    }
    .btn-select {
        text-wrap: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        font-size: 14px;
        line-height: 22px;
    }
    .dropdown-menu {
        box-shadow: var(--shadow1);
        min-width: 164px;
        border: 0;
        padding: 15px 5px;
        border-radius: 0;
        max-height: 68vh;
        isolation: isolate;
        overscroll-behavior-y: contain;
        overflow-y: auto;
        &::-webkit-scrollbar {
            width: 5px;
        }
        &::-webkit-scrollbar-track {
            background-color: var(--bg-scrollbar-track);
        }
        &::-webkit-scrollbar-thumb {
            background: var(--bg-scrollbar-thumb);
            border-radius: 4px;
        }
    }
    .select-item {
        position: relative;
        font-size: 14px;
        font-weight: 500;
        color: var(--secondary);
        padding: 0 15px;
        line-height: 30px;
        width: 100%;
        @include mixin.transition3;
       
        &.active {
            background-color: var(--line);
            color: var(--main);
            padding: 0 15px !important;
            border: 0 !important;
        }
        &:hover {
            background-color: var(--line);
            color: var(--main);
        }
    }
    &:hover {
        border-color: var(--main);
    }
    &.full {
        .dropdown-menu {
            width: 100%;
            margin-left: 0 !important;
            margin-right: 0 !important;
        }
    }
    &.style-1 {
        padding: 10px 14px;
        border-radius: 8px;
        border: 2px solid var(--line);
    }
    &.has-color {
        .select-item {
            display: flex;
            gap: 8px;
            align-items: center;
        }
        .box-color {
            width: 20px;
            height: 20px;
        }
    }
    &.style-2 {
        min-width: unset;
        padding: 9px 11px;
        border-radius: 12px;
        background-color: var(--surface);
        
        .btn-select {
            gap: 16px;
            .text-sort-value {
                font-size: 18px;
                font-weight: 600;
                line-height: 24px;
                letter-spacing: 0.1em;
            }
        }
    }
}
.form-search-select{
    display: flex;
    min-width: 610px;
    border-radius: 4px;
    overflow: hidden;
    .tf-dropdown-sort{
        border-radius: 0;
        flex-shrink: 0;
        min-width: 120px;
        border: none;
        background-color: var(--surface);
        padding: 8px 12px;
        font-weight: 600;
        .icon{
            font-size: 20px;
        }
        .btn-select{
            font-size: 16px;
            line-height: 26px;
        }
    }
    input{
        border-color: transparent !important;
        border-radius: 0;
        padding: 6px 16px;
    }
    .tf-btn{
        flex-shrink: 0;
        padding: 7px 28px;
        border-radius: 0;
    }
}

.form-write-review {
    .heading {
        display: flex;
        align-items: center;
        gap: 24px;
        flex-wrap: wrap;
        margin-bottom: 24px;
    }
    textarea {
        height: 100px;
    }
    button {
        border: 1px solid var(--main);
        padding: 15px 39px;
        letter-spacing: 0.1em;

    }
}

.list-rating-check {
    display: flex;
    flex-direction: row-reverse;
    justify-content: left;
    gap: 10px;
    position: relative;
}

.list-rating-check:not(:checked)>input {
    position: absolute;
    opacity: 0;
    visibility: hidden;
    width: 0;
}

.list-rating-check:not(:checked)>label {
    font-size: 40px;
    cursor: pointer;
    white-space: nowrap;
    width: 40px;
    color: var(--line);
}

.list-rating-check:not(:checked)>label:before {
    font-family: variable.$fontIcon;
    content: "\e92a";
}

.list-rating-check>input:checked~label {
    color: var(--yellow);
}

.list-rating-check:not(:checked)>label:hover,
.list-rating-check:not(:checked)>label:hover~label {
    color: var(--yellow);
}

.list-rating-check>input:checked+label:hover,
.list-rating-check>input:checked+label:hover~label,
.list-rating-check>input:checked~label:hover,
.list-rating-check>input:checked~label:hover~label,
.list-rating-check>label:hover~input:checked~label {
    color: var(--yellow);
}

.tf-check {
    position: relative;
    background: transparent;
    cursor: pointer;
    outline: 0;
    -webkit-appearance: none;
    width: 20px;
    height: 20px;
    min-width: 20px;
    border: 1px solid var(--secondary-2);
    display: inline-flex;
    justify-content: center;
    align-items: center;
    border-radius: 3px;
    &:checked {
        border-color: var(--main);
        background-color: var(--main);
        &::before {
            opacity: 1;
            transform: scale(1);
        }
    }
    &::before {
        font-weight: 500;
        font-family: variable.$fontIcon;
        content: "\e937";
        position: absolute;
        color: var(--white);
        opacity: 0;
        font-size: 12px;
        transform: scale(0);
        @include mixin.transition3;
    }
}
.tf-check-rounded{
    position: relative;
    border: 1px solid var(--secondary-2);
    border-radius: 50%;
    background: none;
    cursor: pointer;
    outline: 0;
    height: 14px;
    width: 14px;
    @include mixin.flex(center,center);
    -webkit-appearance: none;
    &::before {
        content: "";
        position: absolute;
        border-radius: 50%;
        width: 8px;
        height: 8px;
        background-color: var(--main);
        opacity: 0;
    }
        &:checked {
            border-color: var(--main);
            &::before {
                opacity: 1;
            }
        }
}

.tf-select {
    position: relative;
    select {
        width: 100%;
        padding: 7px 16px;
        border: 2px solid var(--line);
        border-radius: 999px;
        -webkit-appearance: none;
        appearance: none;
        background-color: transparent;
        @include mixin.transition3;
    }
    &::after {
        font-family: variable.$fontIcon;
        position: absolute;
        content: "\e935";
        right: 16px;
        top: 50%;
        transform: translateY(-50%);
        font-size: 20px;
        z-index: -1;
    }
    &:hover{
        select{
            border-color: var(--main);
        }
    }
}

.form-bundle-product {
    padding: 23px;
    border-radius: 12px;
    border: 1px solid var(--line);
    display: flex;
    gap: 16px;
    flex-direction: column;
    .tf-bundle-product-total-submit {
        display: flex;
        gap: 12px;
        align-items: center;
    }
    .tf-bundle-product-btn {
        padding: 14px 48px;
    }
    &.type-cols {
        .tf-bundle-products-wrap {
            display: flex;
            gap: 16px 24px;
            overflow-x: auto;
            &::-webkit-scrollbar {
                height: 8px;
            }
            &::-webkit-scrollbar-thumb {
                background: var(--secondary-2);
            }
            &::-webkit-scrollbar-track {
                background: var(--line);
            }
        }
        .tf-bundle-product-item {
            width: 173px;
            flex-shrink: 0;
            flex-direction: column;
            .tf-product-bundle-infos {
                gap: 7px;
            }
            .tf-product-bundle-image {
                width: 100%;
                height: 226px;
            }
        }
    }
    &.type-product-grouped {
        padding: 0;
        border: 0;
        border-radius: 0;
        gap: 20px;
        .tf-bundle-product-item {
            padding-bottom: 20px;
            border-bottom: 1px solid var(--line);
        }
    }
}

.form-login {
    .wrap {
        display: flex;
        flex-direction: column;
        gap: 20px;
        margin-bottom: 28px;
        .forget-password {
            text-decoration: underline;
        }
    }
    .tf-cart-checkbox .tf-checkbox-wrapp {
        width: 20px;
        height: 20px;
        gap: 8px;
    }
    .tf-cart-checkbox .tf-checkbox-wrapp input,
    .tf-cart-checkbox .tf-checkbox-wrapp div {
        width: 20px;
        height: 20px;
        min-width: 20px;
        border-radius: 3px;
    }
    button {
        padding: 10px 32px;
    }
}

.form-has-password {
    .toggle-password {
        position: absolute;
        right: 16px;
        top: 50%;
        transform: translateY(-50%);
        display: inline-flex;
        cursor: pointer;
        i {
            font-size: 20px;
            color: var(--secondary-2);
        }
        &:not(.unshow) {
            i::before {
                content: '\e938';
            }
        }
    }
}

.account-address {
    .tf-btn {
        cursor: pointer;
        padding: 10px 32px;
    }
}

.wd-form-address{
    margin: 20px 0px 40px;
    border-radius: 10px;
    padding: 20px 15px;
    border: 1px solid var(--line);
    .title{
        font-size: 28px;
        line-height: 33.6px;
        margin: 20px 0px;
    }
    .box-field{
        margin-bottom: 15px;
    }
    .tf-select select{
        border-radius: 8px;
        height: 46px;
    }
}