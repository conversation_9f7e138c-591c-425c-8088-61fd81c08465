@use "../abstracts/mixin";

.tf-btn{
    @include mixin.transition3;
    background-color: var(--main);
    color: var(--white);
    padding: 15px 32px;
    border-radius: 99px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-size: 16px;
    line-height: 26px;
    font-weight: 600;
    text-transform: capitalize;
    border:  1px solid var(--main);
    position: relative;
    overflow: hidden;
    span{
        color: inherit;
    }
    .icon {
        font-size: 22px;
    }
    .text,.icon{
        position: relative;
        z-index: 2;
    }
    &.btn-md{
        padding: 10px 24px;
    }
    &:not(.btn-reset){
        &:after {
            content: "";
            position: absolute;
            bottom: -50%;
            // left: 0;
            width: 102%;
            height: 100%;
            background-color: var(--white);
            transform-origin: bottom center;
            transition: transform 600ms cubic-bezier(0.48, 0, 0.12, 1);
            transform: skewY(9.3deg) scaleY(0);
            z-index: 1;
        }
        &:hover {
            color: var(--main);
            &::after{
                transform-origin: bottom center;
                transform: skewY(9.3deg) scaleY(2);
            }
        }
    }
    &.btn-reset{
        &:hover{
            background-color: var(--primary);
            border-color: var(--primary);
            color: var(--white);
        }
    }
    
   
    &.btn-white {
        background-color: var(--white);
        border: none;
        color: var(--main);
        &:hover{
            color: var(--white);
        }
        &::after{
            background-color: var(--primary);
        }
        &.has-border {
            border:  1px solid var(--main);
            &:hover{
                border-color: var(--primary);
            }
        }
    }
    &.btn-square{
        border-radius: 8px;
    } 
    &.radius-4 {
        border-radius: 4px;
    }
}
.btn-line{
    font-size: 16px;
    line-height: 26px;
    padding-bottom: 4px;
    font-weight: 600;
    color: var(--main);
    position: relative;
    @include mixin.transition3;
    display: inline-flex;
    align-items: center;
    gap: 4px;
    background: linear-gradient(to right, var(--primary) 50%, var(--main) 50%);
    background-size: 200% 100%;
    background-position: right;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    transition: background-position 0.3s linear;

    &::after{
        position: absolute;
        content: "";
        left: 0;
        bottom: 0;
        right: 0;
        height: 2px;
        background-color: var(--main);
        @include mixin.transition3;
    }
    &::before {
        position: absolute;
        content: "";
        left: 0;
        width: 0;
        bottom: 0;
        height: 2px;
        background-color: var(--primary);
        transition: width 0.3s linear;
        z-index: 1;
    }
    &.style-white{
        color: var(--white);
        background: linear-gradient(to right, var(--primary) 50%, var(--white) 50%);
        background-size: 200% 100%;
        background-position: right;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        transition: background-position 0.3s linear;
        &::after{
            background-color: var(--white);
        }
    }
    &:hover{
        background-position: left;
        &::before {
            width: 100%;
        }
    }
    
}

.tf-btn-default {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    line-height: 20px;
    i {
        font-size: 20px;
    }
    &:hover {
        color: var(--primary);
    }
    &.style-white {
        color: var(--white);
        &:hover {
            color: var(--primary);
        }
    }
}

.btn-style-1 {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 7px 15px;
    border: 1px solid var(--line); 
    border-radius: 999px;
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
    height: 36px;
}

.btn-style-2 {
    display: flex;
    align-items: center;
    justify-content: center;
    width: max-content;
    padding: 14px 15px;
    border-radius: 999px;
    background-color: var(--main);
    color: var(--white);
    letter-spacing: 0.1em;
    border: 0;
    &:hover {
        background-color: var(--primary);
        color: var(--white);
    }
}

.btn-style-3 {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 14px 15px;
    border-radius: 999px;
    background-color: var(--primary);
    color: var(--white);
    letter-spacing: 0.1em;
    &:hover {
        background-color: var(--main);
    }
}

.btn-style-4 {
    display: flex;
    align-items: center;
    justify-content: center;
    width: max-content;
    padding: 15px 39px;
    border-radius: 4px;
    border: 1px solid var(--main);
    @include mixin.transition3();
    cursor: pointer;
    &:hover {
        background-color: var(--main);
        color: var(--white);
    }
}

.btn-style-5 {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    width: max-content;
    padding: 15px 39px;
    border-radius: 4px;
    border: 1px solid rgba(254, 167, 36, 1);
    background-color: rgba(254, 167, 36, 1);
    @include mixin.transition3();
    cursor: pointer;
    &:hover {
        background-color: var(--white);
        border-color: var(--main);
        color: var(--main);
    }
}

.btn-sold-out {
    pointer-events: none;
    background-color: var(--line) !important;
    color: var(--secondary-2) !important;
    cursor: no-drop;
}

.btn-out-line{
    padding: 10px 40px;
    @include mixin.transition3;
    cursor: pointer;
    border-radius: 44px;
    border: 2px solid var(--line);
    background-color: var(--white);
    color: var(--main);
    &:hover{
        color: var(--primary);
    }
}
.load-more-btn{
    width: auto;
    min-width: 163px;
    height: 42px;
    font-size: 12px;
    line-height: 20px;
    text-transform: uppercase;
    font-weight: 600;
    letter-spacing: 0.1em;
}

.tf-loading{
    position: relative;
    &::before{
        width: 18px;
        height: 18px;
        border: solid 2px transparent;
        border-top-color: transparent !important;
        content: "";
        position: absolute;
        z-index: 2;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        margin: auto;
        border-radius: 50%;
        animation: tf_rotator 0.6s linear infinite paused;
        opacity: 0;
    }
    .text-btn{
        color: inherit;
        
    }
    &.loading{
        &::before {
           
            border: solid 2px var(--main);
            opacity: 1;
            animation-play-state: running;
        }
        .text-btn{
            display: none;
        }
    }
   
}
.btn-infinite-scroll{
    height: 32px;
    padding: 0;
    border: none;
    &::before {
        width: 22px;
        height: 22px;
        border: solid 2px var(--main);
        opacity: 1;
        animation-play-state: running;
    }
}

@keyframes tf_rotator {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.btn-fixed-cart{
    position: fixed;
    top: 40%;
    right: 20px;
    background-color: var(--main);
    width: 60px;
    height: 60px;
    display: block;
    @include mixin.flex(center,center);
    z-index: 999;
    border-radius: 99px 0px 99px 99px;
    .count-box{
        right: 6px;
        top: 16px;
        position: absolute;
        width: 14px;
        height: 14px;
        border-radius: 99px;
        background-color: var(--primary);
        font-size: 10px;
        line-height: 8px;
        color: var(--white);
        @include mixin.flex(center,center);
    }

}