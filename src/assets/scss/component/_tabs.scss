@use "../abstracts/mixin";

.widget-tabs {
    .widget-menu-tab {
        .item-title {
            cursor: pointer;
            position: relative;
            &::after {
                position: absolute;
                content: "";
                background-color: var(--main);
                @include mixin.transition3;
            }
        }
    }
    .widget-content-tab {
        position: relative;
        overflow: hidden;
        .widget-content-inner {
            display: block;
            pointer-events: none;
            opacity: 0;
            visibility: hidden;
            position: absolute;
            z-index: 1;
            top: 0;
            left: 0;
            right: 0;
            -webkit-transform: translateY(30px);
            -ms-transform: translateY(30px);
            transform: translateY(30px);
            transition-timing-function: ease-in;
            transition-duration: 0.2s;
            &.active {
                pointer-events: auto;
                opacity: 1;
                visibility: visible;
                position: relative;
                z-index: 2;
                -webkit-transform: none;
                -ms-transform: none;
                transform: none;
                transition-timing-function: ease-out;
                transition-duration: 0.3s;
                transition-delay: 0.3s;
            }
        }
    }
    &.style-1 {
        .widget-menu-tab {
            display: flex;
            align-items: center;
            gap: 40px;
            justify-content: center;
            padding-bottom: 28px;
            overflow-x: auto;
            .item-title {
                font-size: 20px;
                font-weight: 500;
                line-height: 26px;
                min-width: max-content;
                &::after {
                    position: absolute;
                    content: "";
                    background-color: var(--main);
                    @include mixin.transition3;
                    bottom: -1px;
                    left: 0;
                    width: 0;
                    height: 1px;
                }
                &.active {
                    &::after {
                        width: 100%;
                    }
                }
            }
        }
        .widget-content-inner {
            padding: 39px;
            border: 1px solid var(--line);
            border-radius: 8px;
        }
    }
    &.style-menu-tabs {
        display: flex;
        gap: 60px;
        .widget-menu-tab {
            display: flex;
            flex-direction: column;
            gap: 20px;
            width: 196px;
            flex-shrink: 0;
            height: max-content;
            overflow-x: auto;
            overflow-y: hidden;
            .item-title {
                font-size: 20px;
                font-weight: 500;
                line-height: 28px;
                @include mixin.transition3();
                &::after {
                    position: absolute;
                    content: "";
                    background-color: var(--main);
                    @include mixin.transition3;
                    top: 0;
                    left: 0;
                    width: 2px;
                    height: 0;
                }
                &.active {
                    padding-left: 16px;
                    &::after {
                        height: 100%;
                    }
                }
            }
        }
        .widget-content-tab {
            flex-grow: 1;
        }
        .widget-content-inner {
            border-radius: 8px;
            padding: 40px;
            border: 1px solid var(--line);
        }
    }
    &.style-2 {
        .widget-menu-tab {
            padding: 3px;
            border-radius: 12px;
            border: 1px solid var(--line);
            display: flex;
            align-items: center;
            gap: 8px;
            .item-title {
                padding: 7px 20px;
                border-radius: 12px;
                @include mixin.transition3();
                &.active {
                    background-color: var(--line);
                }
            }
        }
    }
    &.style-3 {
        .widget-menu-tab {
            display: flex;
            align-items: center;
            gap: 32px;
            margin-bottom: 16px;
            overflow-x: auto;
            border-bottom: 1px solid var(--line);
            .item-title {
                padding: 7px 0;
                min-width: max-content;
                &::after {
                    position: absolute;
                    content: "";
                    background-color: var(--primary);
                    @include mixin.transition3;
                    bottom: 0px;
                    left: 0;
                    width: 0;
                    height: 2px;
                }
                &.active {
                    &::after {
                        width: 100%;
                    }
                }
            }
        }
    }
}

.tab-description {
    display: flex;
    gap: 32px;
    .right,
    .left {
        width: 100%;
    }
    ul.list-text.type-disc li {
        padding-left: 24px;
        &:before {
            top: 11px;
            left: 11px;
            width: 3px;
            height: 3px;
            border-radius: 0;
        }
    }
}

.tab-reviews {
    .tab-reviews-heading {
        margin-bottom: 40px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 50px;
        flex-wrap: wrap;
        .top {
            display: flex;
            align-items: center;
            justify-content: space-between;
            flex-grow: 1;
            width: 100%;
            max-width: 597px;
            gap: 30px;
            .list-start {
                display: flex;
                gap: 2px;
                justify-content: center;
                margin-bottom: 7px;
                margin-top: 1px;
                font-size: 17px;
            }
        }
    }
    .rating-score {
        width: 100%;
        max-width: 365px;
        .item {
            width: 100%;
            display: flex;
            align-items: center;
            &:not(:last-child) {
                margin-bottom: 4px;
            }
        }
        .number-1 {
            width: 12px;
            text-align: end;
        }
        .icon {
            font-size: 15px;
            margin-left: 4px;
        }
        .number-2 {
            width: 17px;
        }
        .line-bg {
            margin: 0 8px;
            width: 100%;
            height: 8px;
            background-color: var(--line);
            div {
                height: 100%;
                background-color: var(--main);
            }
        }
    }
}

.tab-shipping {
    display: flex;
    gap: 30px;
    p {
        color: var(--secondary);
    }
}

.tab-product{
    gap: 20px;
    margin-bottom: 20px;
    overflow-x: auto;  
    display: flex;
    .nav-tab-item {
        a {
            display: flex;
            width: 100%;
            font-size: 24px;
            line-height: 34px;
            font-weight: 500;
            white-space: nowrap;
            padding-bottom: 4px;
            border-bottom: 2px solid transparent;
            text-transform: capitalize;
            @include mixin.transition3;
            color: var(--secondary);
            &:hover,
            &.active {
                color: var(--main);
                border-bottom-color: var(--main);

            }
        }
    }
    &::-webkit-scrollbar {
        width: 0px;
        height: 0px;
    }
}

.tab-product-v2{
    gap: 26px;
    margin-top: 24px;
    overflow-x: auto;  
    display: flex;
    .nav-tab-item {
        a {
            display: flex;
            width: 100%;
            font-size: 16px;
            line-height: 24px;
            font-weight: 500;
            white-space: nowrap;
            padding-bottom: 4px;
            padding-top: 4px;
            border-bottom: 2px solid transparent;
            text-transform: capitalize;
            @include mixin.transition3;
            color: var(--secondary);
            &:hover,
            &.active {
                color: var(--main);
                border-bottom-color: var(--main);
            }
        }
    }
    &::-webkit-scrollbar {
        width: 0px;
        height: 0px;
    }
}

.tab-product-v3 {
    display: flex;
    gap: 8px;
    overflow-x: auto;
    a {
        width: max-content;
        padding: 4px 15px;
        border-radius: 99px;
        border: 1px solid var(--line);
        &:hover,
        &.active {
            color: var(--primary);
            background-color: #E431311A;
            border-color: var(--Critical);
        }
    }
}

.flat-animate-tab {
    overflow: hidden;
    .tab-content {
        position: relative;
    }
    .tab-pane {
        display: block;
        pointer-events: none;
        opacity: 0;
        visibility: hidden;
        position: absolute;
        z-index: 1;
        top: 0;
        left: 0;
        right: 0;
        -webkit-transform: translateY(30px);
        -ms-transform: translateY(30px);
        transform: translateY(30px);
        transition-timing-function: ease-in;
        transition-duration: 0.2s;
        &.active {
            pointer-events: auto;
            opacity: 1;
            visibility: visible;
            position: relative;
            z-index: 2;
            -webkit-transform: none;
            -ms-transform: none;
            transform: none;
            transition-timing-function: ease-out;
            transition-duration: 0.3s;
            transition-delay: 0.3s;
        }
    }
    .sec-btn{
        margin-top: 20px;
    }
}

.tab-policies {
    ul.list-text li {
        padding-left: 24px;
    }
    ul.list-text.type-number {
        margin-bottom: 6px;
        gap: 0;
    }
}

.tab-size {
    display: flex;
    flex-direction: column;
    gap: 32px;
    .size-button-wrap {
        display: flex;
        gap: 32px;
        .size-button-item {
            width: 100%;
            padding: 24px 0;
            border-radius: 12px;
            border: 1px solid var(--line);
            background-color: var(--line);
            text-align: center;
            @include mixin.transition3();
            cursor: pointer;
            &.select-option {
                border-color: var(--main);
            }
            h5 {
                text-transform: capitalize;
            }
        }
    }
    .suggests-title {
        margin-bottom: 16px;
    }
    .suggests-list {
        display: flex;
        gap: 16px;
        align-items: center;
        flex-wrap: wrap;
        .suggests-item {
            background-color: var(--line);
            border-radius: 999px;
            padding: 7px 20px;
        }
    }
}
.tab-sizeguide-table {
    border: 1px solid var(--line);
    border-radius: 5px;
    width: 100%;
    th {
        border: 1px solid var(--line);
        padding: 10px;
        font-weight: 600;
        line-height: 20px;
    }
    td {
        border: 1px solid var(--line);
        border-width: 0 1px 1px 0;
        padding: 10px;
        line-height: 20px;
    }
}

.tab-banner{
    .nav-tab-item{
        .nav-tab-link{
            padding-top: 15px;
            padding-bottom: 15px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-bottom: 1px solid var(--line);
            gap: 8px;
            position: relative;
            &::after {
                position: absolute;
                content: '';
                width: 0;
                height: 1px;
                left: auto;
                right: 0;
                bottom: 0;
                background-color: var(--main);
                @include mixin.transition3();
            }

            .arr-link{
                display: flex;
                align-items: center;
                gap: 8px;
            }
            .text-more{
                opacity: 0;
                visibility: hidden;
                @include mixin.transition3;
            }
            .icon{
                font-size: 16px;
            }
            &:hover,
            &.active{
                &::after {
                    width: 100%;
                    left: 0;
                    right: auto;
                }

                .text-more{
                    opacity: 1;
                    visibility: visible;
                }
            }   
        }
        &:first-child{
            .nav-tab-link{
                padding-top: 0;
            }
        }
       
    }
}

