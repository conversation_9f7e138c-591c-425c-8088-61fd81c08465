// import { BlogThumbnail } from "@/types/blog/blog-thumbnail";

// export const blogPosts: BlogThumbnail[] = [
//     {
//         id: 1,
//         // imgSrc: "/images/blog/blog-grid-1.jpg",
//         imgSrc: "https://picsum.photos/615/461?random=1",
//         imgWidth: 615,
//         imgHeight: 461,
//         date: "13 August",
//         title: "Top 10 Summer Fashion Trends You Can't Miss in 2024",
//         description:
//             "Lorem ipsum dolor sit amet, consectetur adipiscing elit. In sed vulputate massa.",
//         delay: "0s",
//     },
//     {
//         id: 2,
//         // imgSrc: "/images/blog/blog-grid-8.jpg",
//         imgSrc: "https://picsum.photos/615/461?random=2",
//         imgWidth: 615,
//         imgHeight: 461,
//         date: "13 August",
//         title: "How to Effortlessly Style Your Office Wear for a Modern Look",
//         description:
//             "Lorem ipsum dolor sit amet, consectetur adipiscing elit. In sed vulputate massa.",
//         delay: "0.1s",
//     },
//     {
//         id: 3,
//         // imgSrc: "/images/blog/blog-grid-6.jpg",
//         imgSrc: "https://picsum.photos/615/461?random=3",
//         imgWidth: 615,
//         imgHeight: 461,
//         date: "13 August",
//         title: "Sustainable Fashion: Eco-Friendly Brands to Watch This Year",
//         description:
//             "Lorem ipsum dolor sit amet, consectetur adipiscing elit. In sed vulputate massa.",
//         delay: "0.2s",
//     },
// ];

// export const blogPosts2 = [
//     {
//         id: 4,
//         imgSrc: "/images/blog/new-1.jpg",
//         imgAlt: "img",
//         wowDelay: "0s",
//         date: "13 August",
//         title: "Top 10 Summer Fashion Trends You Can't Miss in 2024",
//         desc: "Lorem ipsum dolor sit amet, consectetur adipiscing elit. In sed vulputate massa.",
//     },
//     {
//         id: 5,
//         imgSrc: "/images/blog/new-2.jpg",
//         imgAlt: "img",
//         wowDelay: "0.1s",
//         date: "13 August",
//         title: "How to Effortlessly Style Your Office Wear for a Modern Look",
//         desc: "Lorem ipsum dolor sit amet, consectetur adipiscing elit. In sed vulputate massa.",
//     },
// ];

// export const blogPosts3 = [
//     {
//         id: 6,
//         title: "5 ways to enhance your business website in 2024",
//         date: "February 28, 2024",
//         author: "Avitex",
//         imgSrc: "/images/blog/sidebar-2.jpg",
//         delay: "0s",
//     },
//     {
//         id: 7,
//         title: "5 ways to enhance your business website in 2024",
//         date: "February 28, 2024",
//         author: "Avitex",
//         imgSrc: "/images/blog/sidebar-3.jpg",
//         delay: "0.1s",
//     },
//     {
//         id: 8,
//         title: "5 ways to enhance your business website in 2024",
//         date: "February 28, 2024",
//         author: "Avitex",
//         imgSrc: "/images/blog/sidebar-4.jpg",
//         delay: "0.2s",
//     },
// ];

// export const blogItems = [
//     {
//         id: 9,
//         imgSrc: "/images/blog/new-beauty1.jpg",
//         date: "13 August",
//         title: "Must-Have Beauty Products for Glowing Skin",
//         delay: "0s",
//     },
//     {
//         id: 10,
//         imgSrc: "/images/blog/new-beauty2.jpg",
//         date: "13 August",
//         title: "Top 5 Makeup Essentials for a Flawless Look",
//         delay: "0.1s",
//     },
//     {
//         id: 11,
//         imgSrc: "/images/blog/new-beauty3.jpg",
//         date: "13 August",
//         title: "Trending Beauty Products You Need to Try Right Now",
//         delay: "0.2s",
//     },
//     {
//         id: 12,
//         imgSrc: "/images/blog/new-beauty4.jpg",
//         date: "13 August",
//         title: "A Beginner's Guide to Building Your Own Beauty Kit",
//         delay: "0.3s",
//     },
// ];

// export const blogPosts4 = [
//     {
//         id: 13,
//         imgSrc: "/images/blog/blog-grid-10.jpg",
//         alt: "",
//         date: "13 August",
//         title: "Top 10 Summer Fashion Trends You Can't Miss in 2024",
//         description:
//             "Lorem ipsum dolor sit amet, consectetur adipiscing elit. In sed vulputate massa.",
//         delay: "0s",
//     },
//     {
//         id: 14,
//         imgSrc: "/images/blog/blog-grid-11.jpg",
//         alt: "",
//         date: "13 August",
//         title: "How to Effortlessly Style Your Office Wear for a Modern Look",
//         description:
//             "Lorem ipsum dolor sit amet, consectetur adipiscing elit. In sed vulputate massa.",
//         delay: "0.1s",
//     },
//     {
//         id: 15,
//         imgSrc: "/images/blog/blog-grid-12.jpg",
//         alt: "",
//         date: "13 August",
//         title: "Sustainable Fashion: Eco-Friendly Brands to Watch This Year",
//         description:
//             "Lorem ipsum dolor sit amet, consectetur adipiscing elit. In sed vulputate massa.",
//         delay: "0.2s",
//     },
// ];

// export const newsItems = [
//     {
//         id: 16,
//         wowDelay: "0s",
//         imgSrc: "/images/section/news-1.jpg",
//         date: "13 August",
//         title: "Choosing the Perfect Tent for Your Adventure",
//         description:
//             "Lorem ipsum dolor sit amet, consectetur adipiscing elit. In sed vulputate massa.",
//     },
//     {
//         id: 17,
//         wowDelay: "0.1s",
//         imgSrc: "/images/section/news-2.jpg",
//         date: "13 August",
//         title: "Choosing the Perfect Tent for Your Adventure",
//         description:
//             "Lorem ipsum dolor sit amet, consectetur adipiscing elit. In sed vulputate massa.",
//     },
// ];

// export const blogPosts5 = [
//     {
//         id: 18,
//         date: "13 August",
//         imgSrc: "/images/blog/blog-grid-10.jpg",
//         title: "Top 10 Summer Fashion Trends You Can't Miss in 2024",
//         description:
//             "Lorem ipsum dolor sit amet, consectetur adipiscing elit. In sed vulputate massa.",
//         delay: "0s",
//     },
//     {
//         id: 19,
//         date: "13 August",
//         imgSrc: "/images/blog/blog-grid-11.jpg",
//         title: "How to Effortlessly Style Your Office Wear for a Modern Look",
//         description:
//             "Lorem ipsum dolor sit amet, consectetur adipiscing elit. In sed vulputate massa.",
//         delay: "0.1s",
//     },
//     {
//         id: 20,
//         date: "13 August",
//         imgSrc: "/images/blog/blog-grid-12.jpg",
//         title: "Sustainable Fashion: Eco-Friendly Brands to Watch This Year",
//         description:
//             "Lorem ipsum dolor sit amet, consectetur adipiscing elit. In sed vulputate massa.",
//         delay: "0.2s",
//     },
// ];

// export const blogPosts6 = [
//     {
//         id: 21,
//         imgSrc: "/images/blog/blog-grid-1.jpg",
//         date: "February 28, 2024",
//         author: "Themesflat",
//         title: "How Technology is Transforming the Industry",
//         description:
//             "Lorem ipsum dolor sit amet, consectetur adipiscing elit. In sed vulputate massa.",
//     },
//     {
//         id: 22,
//         imgSrc: "/images/blog/blog-grid-2.jpg",
//         date: "February 28, 2024",
//         author: "Themesflat",
//         title: "The Future of Fashion How Technology Transforms the Industry",
//         description:
//             "Lorem ipsum dolor sit amet, consectetur adipiscing elit. In sed vulputate massa.",
//     },
//     {
//         id: 23,
//         imgSrc: "/images/blog/blog-grid-3.jpg",
//         date: "February 28, 2024",
//         author: "Themesflat",
//         title: "From Concept to Closet The Journey of Sustainable Fashion",
//         description:
//             "Lorem ipsum dolor sit amet, consectetur adipiscing elit. In sed vulputate massa.",
//     },
//     {
//         id: 24,
//         imgSrc: "/images/blog/blog-grid-4.jpg",
//         date: "February 28, 2024",
//         author: "Themesflat",
//         title: "Unlocking Style Potential Personalization in Fashion Retail",
//         description:
//             "Lorem ipsum dolor sit amet, consectetur adipiscing elit. In sed vulputate massa.",
//     },
//     {
//         id: 25,
//         imgSrc: "/images/blog/blog-grid-5.jpg",
//         date: "February 28, 2024",
//         author: "Themesflat",
//         title: "Fashion Forward Embracing Diversity and Inclusion in Design",
//         description:
//             "Lorem ipsum dolor sit amet, consectetur adipiscing elit. In sed vulputate massa.",
//     },
//     {
//         id: 26,
//         imgSrc: "/images/blog/blog-grid-6.jpg",
//         date: "February 28, 2024",
//         author: "Themesflat",
//         title: "The Ultimate Guide: Dressing Stylishly with Minimal Effort",
//         description:
//             "Lorem ipsum dolor sit amet, consectetur adipiscing elit. In sed vulputate massa.",
//     },
//     {
//         id: 27,
//         imgSrc: "/images/blog/blog-grid-7.jpg",
//         date: "February 28, 2024",
//         author: "Themesflat",
//         title: "How to Transition Your Wardrobe from Day to Night",
//         description:
//             "Lorem ipsum dolor sit amet, consectetur adipiscing elit. In sed vulputate massa.",
//     },
//     {
//         id: 28,
//         imgSrc: "/images/blog/blog-grid-8.jpg",
//         date: "February 28, 2024",
//         author: "Themesflat",
//         title: "Embracing Change: The Role of AI in the Fashion Industry",
//         description:
//             "Lorem ipsum dolor sit amet, consectetur adipiscing elit. In sed vulputate massa.",
//     },
// ];

// export const allBlogs = [
//     ...blogPosts,
//     ...blogPosts2,
//     ...blogPosts3,
//     ...blogItems,
//     ...blogPosts4,
//     ...newsItems,
//     ...blogPosts5,
//     ...blogPosts6,
// ];
