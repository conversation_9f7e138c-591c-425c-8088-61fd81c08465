// import { BannerCollection, BannerCountdown } from "@/types/product/banner-collection";

// export const bannerCollections: BannerCollection[] = [
//     {
//         // imgSrc: "/images/collections/banner-collection/banner-cls1.jpg",
//         imgSrc: "https://picsum.photos/945/709?random=1",
//         blurHash: "",
//         width: 945,
//         height: 709,
//         title: "Crossbody bag",
//         titleRedirectUrl: "",
//         description: "From beach to party: Perfect styles for every occasion.",
//         action: "Shop Now",
//         actionUrl: "",
//         defaultStyle: true,
//     },
//     {
//         // imgSrc: "/images/collections/banner-collection/banner-cls2.jpg",
//         imgSrc: "https://picsum.photos/945/945?random=2",
//         blurHash: "",
//         width: 945,
//         height: 945,
//         title: "Capsule Collection",
//         titleRedirectUrl: "",
//         description: "Reserved for special occasions",
//         action: "Shop Now",
//         actionUrl: "",
//         defaultStyle: false,
//     }
// ]

// export const bannerCountdown: BannerCountdown = {
//     title: "Limited-Time Deals On!",
//     subTitle: "Up to 50% Off Selected Styles. Don't Miss Out.",
//     actionText: "Shop Now",
//     ctaUrl: "",
//     // imgSrc: "/images/banner/img-countdown1.png",
//     imgSrc: "https://picsum.photos/607/655?random=4",
//     blurHash: "",
//     width: 607,
//     height: 655,
//     endsOn: "2025-05-12T23:59:59"
// }