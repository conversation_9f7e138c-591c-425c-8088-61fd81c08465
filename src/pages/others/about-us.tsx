import Features from "@/components/common/features";
import QueryWrapper from "@/components/common/query-wrapper";
import { TestimonialsLoader } from "@/components/common/testimonials";
import Testimonials from "@/components/common/testimonials1";
import Footer1 from "@/components/footers/footer1";
import Header from "@/components/headers/header";
import About from "@/components/others/about-us";
import Brands from "@/components/others/brands";
import Breadcrumb from "@/components/products/breadcrumb1";
import { useHomePageData } from "@/hooks/home-quries";
import { HomeConfigData } from "@/utils/home-config";

export const metadata = {
    title: "About Us || Modave - Multipurpose React Nextjs eCommerce Template",
    description: "Modave - Multipurpose React Nextjs eCommerce Template",
};

export default function AboutUs() {
    const config = HomeConfigData()
    const {
        testimonials,
    } = useHomePageData()
    return (
        <>
            <Header />
            <Breadcrumb title="About Our Store" />
            <About />
            {config.policyItems && <Features config={config.policyItems} parentClass="flat-spacing line-bottom-container" />}
            <Brands parentClass="flat-spacing-5 bg-surface" />
            <QueryWrapper query={testimonials} loader={<TestimonialsLoader />}>
                {(data) => <Testimonials testimonials={data} />}
            </QueryWrapper>
            <Footer1 />
        </>
    );
}