import Header from "../../components/headers/header";
import LoginComponent from "../../components/auth/login";
import { GoogleOAuthProvider } from "@react-oauth/google";
import Footer1 from "../../components/footers/footer1";
import Breadcrumb from "@/components/products/breadcrumb1";

export default function Login() {
    return (
        <>
            <GoogleOAuthProvider clientId={import.meta.env.VITE_API_GOOGLE_CLIENT_ID ?? ""}>
                <Header />
                <Breadcrumb title="Login" />
                <LoginComponent />
                <Footer1 />
            </GoogleOAuthProvider>
        </>
    )
}