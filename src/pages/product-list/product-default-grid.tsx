import Footer1 from "@/components/footers/footer1"
import Header from "@/components/headers/header"
import Breadcrumb from "@/components/products/breadcrumb"
import Product from "@/components/products/product2"
import { useContextElement } from "@/layout/context"
import Page404 from "../others/page404"
import { Category } from "@/types/product/collection"

export default function ProductDefaultGrid({ showCategory }: { showCategory?: boolean | undefined }) {
    const path = window.location.pathname
    const paths = path.split("/")

    const data = useContextElement()

    let category: Category | undefined;

    if (showCategory) {
        category = data?.navData?.find((c) => c.slug === paths[paths.length - 1]) ?? undefined;
    } else {
        const subCategory = data?.navData?.find((c) =>
            c.sub_categories?.some((s) => s.slug === paths[paths.length - 1])
        );

        category = subCategory ?? undefined;
    }

    if (!category) return (<Page404 />)
    const subCategories = showCategory ? data.navData?.find((c) => c.slug == paths[paths.length - 1])?.sub_categories : undefined
    return (
        <>
            <Header />
            <Breadcrumb />
            <Product categoryId={category.id.toString()} subCategories={subCategories} parentClass="flat-spacing pt-0" />
            <Footer1 />
        </>
    )
}