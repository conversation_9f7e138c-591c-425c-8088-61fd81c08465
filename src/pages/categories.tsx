import { Loader } from "@/components/common/query-wrapper";
import { useNavData } from "@/hooks/home-quries";
import Page404 from "./others/page404";
import Header from "@/components/headers/header";
import AllCategories from "@/components/products/all-categories";
import Footer1 from "@/components/footers/footer1";
import Breadcrumb from "@/components/products/breadcrumb1";

export default function Categories() {
    const { isLoading, data } = useNavData()
    if (isLoading) return <Loader />
    if (!data) return <Page404 />

    const categories = data.flatMap(category => category.sub_categories).filter(Boolean)

    return (<>
        <Header />
        <Breadcrumb title="Collection" />
        <AllCategories categories={categories} />
        <Footer1 />
    </>)
}