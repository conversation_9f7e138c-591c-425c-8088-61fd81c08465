import ShopCart from "@/components/checkout/shop-cart";
import Breadcrumb from "@/components/common/breadcrumb-left";
import QueryWrapper from "@/components/common/query-wrapper";
import Footer1 from "@/components/footers/footer1";
import Header from "@/components/headers/header";
import RelatedProducts, { RecommendedProductsSkeleton } from "@/components/product-details/releated-product";
import { useShopInstagram } from "@/hooks/product-quries";

export default function Cart() {
    const recommendedProducts = useShopInstagram()

    return (
        <>
            <Header />
            <Breadcrumb title="Shopping Cart" />
            <ShopCart />
            <QueryWrapper query={recommendedProducts} loader={<RecommendedProductsSkeleton />}>
                {(data) => <RelatedProducts products={data} />}
            </QueryWrapper>
            <Footer1 />
        </>
    )
}