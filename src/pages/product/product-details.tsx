import Header from "../../components/headers/header";
import Breadcumb from "../../components/product-details/breadcumb";
import Descriptions1 from "../../components/product-details/descriptions/descriptions";
import Details1 from "../../components/product-details/details/details1";
import RelatedProducts, { RecommendedProductsSkeleton } from "../../components/product-details/releated-product";
import Footer1 from "../../components/footers/footer1";
import { useParams } from "react-router-dom";
import { useProductDetails, useShopInstagram } from "@/hooks/product-quries";
import QueryWrapper, { Loader } from "@/components/common/query-wrapper";


export default function ProductDetails() {
    const { slug } = useParams();

    const { isLoading, data } = useProductDetails(slug ?? "")
    const recommendedProducts = useShopInstagram()

    if (isLoading) return (<Loader />)


    return (
        <>
            <Header />
            {data && <Breadcumb product={data.product} />}
            {data && <Details1 product={data.product} activeVarient={data.variant_details} />}
            {data && <Descriptions1 product={data.product} varient={data.variant_details} />}

            <QueryWrapper query={recommendedProducts} loader={<RecommendedProductsSkeleton />}>
                {(data) => <RelatedProducts products={data} />}
            </QueryWrapper>

            <Footer1 />
        </>
    );
}