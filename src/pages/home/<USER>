import Features from "@/components/common/features";
import QueryWrapper from "@/components/common/query-wrapper";
import { ShopGramLoader } from "@/components/common/shop-gram";
import Categories from "@/components/electronics/categories";
import Collections from "@/components/electronics/collections";
import Collections2 from "@/components/electronics/collections2";
import Products4 from "@/components/electronics/product4";
import Products3 from "@/components/electronics/products3";
import Footer1 from "@/components/footers/footer1";
import Header from "@/components/headers/header";
import { CollectionsLoading } from "@/components/home/<USER>";
import { HeroLoading } from "@/components/home/<USER>";
import Hero from "@/components/home/<USER>";
import { Product1Loader } from "@/components/products/product1";
import { useHomePageData } from "@/hooks/home-quries";
import { useContextElement } from "@/layout/context";
import { HomeConfigData } from "@/utils/home-config";
import React from "react";

const Products = React.lazy(() => import("@/components/products/product1"))

export default function HomeDefault() {
    const config = HomeConfigData()
    const { navData } = useContextElement()
    const {
        slides,
        collection,
        products,
        // countdownBanners,
        // testimonials,
        shopInsta,
    } = useHomePageData()



    return (
        <>
            <Header />
            <QueryWrapper query={slides} loader={<HeroLoading />}>
                {(data) => (<Hero slides={data} />)}
            </QueryWrapper>
            <QueryWrapper query={collection} loader={<CollectionsLoading />}>
                {(data) => (<Categories collections={data} />)}
            </QueryWrapper>
            <QueryWrapper query={products} loader={<Product1Loader />}>
                {(data) => <Products products={data} parentClass="flat-spacing-3" />}
            </QueryWrapper>
            <Collections />
            <QueryWrapper query={products} loader={<Product1Loader />}>
                {(data) => {
                    const product3 = [...data[0], ...data[1], ...data[2]]
                    return (<Products3 products={product3.slice(0, 9)} />)
                }}
            </QueryWrapper>
            <Collections2 slides={navData} />
            <QueryWrapper query={shopInsta} loader={<ShopGramLoader />}>
                {(data) => <Products4 products={data} />}
            </QueryWrapper>

            {config.policyItems && <Features config={config.policyItems} parentClass="flat-spacing" />}
            <Footer1 />
        </>
    );
}