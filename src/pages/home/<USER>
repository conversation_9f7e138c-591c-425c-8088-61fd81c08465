import { lazy, Suspense } from "react";

const HomeFashion = lazy(() => import('./home-default'));
const HomeElectronic = lazy(() => import('./home-electronic'));

export default function Home() {
    const type = import.meta.env.VITE_BASIC_TYPE || 'fashion';

    return (
        <Suspense fallback={<div></div>}>
            {type === 'fashion' ? (
                <HomeFashion />
            ) : (
                <HomeElectronic />
            )}
        </Suspense>
    );
}