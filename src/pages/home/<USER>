import QueryWrapper from "@/components/common/query-wrapper";
import { ShopGramLoader } from "@/components/common/shop-gram";
// import { TestimonialsLoader } from "@/components/common/testimonials";
import { CollectionsLoading } from "@/components/home/<USER>";
import { HeroLoading } from "@/components/home/<USER>";
import { Product1Loader } from "@/components/products/product1";
import { useHomePageData } from "@/hooks/home-quries";
import { HomeConfigData } from "@/utils/home-config";
import React from "react";
import { Shimmer } from "react-shimmer";

const Features = React.lazy(() => import("@/components/common/features"))
const ShopGram = React.lazy(() => import("@/components/common/shop-gram"))
// const Testimonials = React.lazy(() => import("@/components/common/testimonials"))
const Footer1 = React.lazy(() => import("@/components/footers/footer1"))
const Header1 = React.lazy(() => import("@/components/headers/header1"))
const BannerCountdown = React.lazy(() => import("@/components/home/<USER>"))
const Collections = React.lazy(() => import("@/components/home/<USER>"))
const Hero = React.lazy(() => import("@/components/home/<USER>"))
const Products = React.lazy(() => import("@/components/products/product1"))

export default function HomeDefault() {
    const config = HomeConfigData()
    const {
        slides,
        collection,
        products,
        countdownBanners,
        // testimonials,
        shopInsta,
    } = useHomePageData()

    return (
        <>
            <Header1 />
            <QueryWrapper query={slides} loader={<HeroLoading />}>
                {(data) => (<Hero slides={data} />)}
            </QueryWrapper>
            <QueryWrapper query={collection} loader={<CollectionsLoading />}>
                {(data) => (<Collections uri="sub" collections={data} />)}
            </QueryWrapper>
            <QueryWrapper query={products} loader={<Product1Loader />}>
                {(data) => <Products products={data} parentClass="flat-spacing-3" />}
            </QueryWrapper>
            <QueryWrapper query={countdownBanners} loader={<Shimmer height={328} width={3000} />}>
                {(data) => data && data.length > 0 ? <BannerCountdown banner={data[0]} /> : <></>}
            </QueryWrapper>
            {/* <QueryWrapper query={testimonials} loader={<TestimonialsLoader />}>
                {(data) => <Testimonials testimonials={data} parentClass="flat-spacing" />}
            </QueryWrapper> */}
            <QueryWrapper query={shopInsta} loader={<ShopGramLoader />}>
                {(data) => <ShopGram products={data} parentClass="" />}
            </QueryWrapper>
            {config.policyItems && <Features config={config.policyItems} parentClass="flat-spacing" />}
            <Footer1 />
        </>
    )
}