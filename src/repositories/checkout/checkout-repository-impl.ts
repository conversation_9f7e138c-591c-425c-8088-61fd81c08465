import { CartDetails } from "@/types/cart";
import { CheckoutRepository } from "./checkout-repository";
import fetchApi from "@/utils/cache-utils";
import { API_ENDPOINTS } from "@/utils/api-const";
import { Order } from "@/types/order";
import { ProductVariant } from "@/types/product/product";
import { UserAddress } from "@/types/user";

export class CheckoutRepositoryImpl extends CheckoutRepository {

    async getCartItems(coupon?: string): Promise<CartDetails> {
        return (await fetchApi(API_ENDPOINTS.getCartDetails(coupon)) as any).data
    }

    async addToCart(product: string, qty: number): Promise<void> {
        return await fetchApi(API_ENDPOINTS.addToCart, {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
                "user_id": 1,
                "product_id": product,
                "quantity": qty,
            })
        })
    }

    getWishlistItems(): Promise<ProductVariant[]> {
        throw new Error("Method not implemented.");
    }
    updateWishlist(_: ProductVariant, __: boolean): Promise<void> {
        throw new Error("Method not implemented.");
    }

    placeOrder(_: UserAddress, __?: string): Promise<[string, string]> {
        throw new Error("Method not implemented.");
    }

    async getOrders(_: number, __: number): Promise<Order[]> {
        throw new Error("Method not implemented.");
    }

    async getOrderDetails(_: string): Promise<Order | null> {
        throw new Error("Method not implemented.");
    }

    cancelOrder(_: string): Promise<void> {
        throw new Error("Method not implemented.");
    }

}