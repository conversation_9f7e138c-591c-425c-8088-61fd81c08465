import { CartDetails } from "@/types/cart";
import { Order } from "@/types/order";
import { CheckoutRepository } from "./checkout-repository";
import { addToCart, clearCart, getCartItems, getWishlistItems, updateWishlist } from "@/utils/cart-utils";
import { getJsonFile } from "@/utils/fb-utils";
import { ProductVariant } from "@/types/product/product";
import AppConfig from "@/utils/app-config";
import { getAuth } from "firebase/auth";
import { UserAddress } from "@/types/user";
import { addDoc, collection, doc, getDoc, getDocs, getFirestore, limit, orderBy, query, startAfter, Timestamp, where } from "firebase/firestore";

export class CheckoutRepositoryImpl extends CheckoutRepository {

    async getCartItems(coupon?: string, id?: string): Promise<CartDetails> {

        if (id) {
            const products = await getJsonFile("products.json")
            const variants = products.flatMap((product: any) => product.variants);
            const productData = variants.find((variant: any) => variant.slug === id);
            if (!productData) {
                throw new Error("Product not found");
            }

            return {
                order_summary: {
                    coupon_discount: 0,
                    discount: productData.mrp ? (productData.mrp - productData.selling_price) : 0,
                    items: [
                        {
                            id: productData.id,
                            slug: productData.slug,
                            name: productData.name,
                            quantity: 1,
                            selling_price: productData.selling_price,
                            mrp: productData.mrp,
                            options: productData.options,
                            images: productData.images,
                            product_total: productData.selling_price
                        }
                    ],
                    subtotal: productData.mrp ?? productData.selling_price,
                    shipping: 0,
                    total: productData.selling_price
                }
            }
        }
        return getCartItems(coupon);
    }

    async addToCart(product: string, qty: number): Promise<void> {
        const products = await getJsonFile("products.json")
        const variants = products.flatMap((product: any) => product.variants);
        const productData = variants.find((variant: any) => variant.id === product);
        if (!productData) {
            throw new Error("Product not found");
        }
        await addToCart(productData, qty);
    }

    async getWishlistItems(): Promise<ProductVariant[]> {
        return getWishlistItems()
    }

    async updateWishlist(product: ProductVariant, isAdded: boolean): Promise<void> {
        await updateWishlist(product, isAdded);
    }

    async placeOrder(address: UserAddress, _?: string, productId?: number | string): Promise<[string, string]> {

        const app = AppConfig()
        const user = getAuth(app).currentUser
        if (!user) {
            throw new Error("User not authenticated");
        }
        let orderSummary = {}
        let items: any[] = []
        const userId = user.uid;
        if (productId) {
            const products = await getJsonFile("products.json")
            const variants = products.flatMap((product: any) => product.variants);
            const productData = variants.find((variant: any) => variant.slug === productId);
            if (!productData) {
                throw new Error("Product not found");
            }
            orderSummary = {
                subtotal: productData.mrp ?? productData.selling_price,
                shipping_fee: 0,
                tax: 0,
                discount: productData.mrp ? (productData.mrp - productData.selling_price) : 0,
                coupon_discount: 0,
                total: productData.selling_price
            }
            items = [
                {
                    id: productData.id,
                    slug: productData.slug,
                    name: productData.name,
                    quantity: 1,
                    selling_price: productData.selling_price,
                    mrp: productData.mrp,
                    options: productData.options,
                    image: productData.images[0],
                    product_total: productData.selling_price
                }
            ]
        } else {
            const cartData = await getCartItems()
            orderSummary = {
                subtotal: cartData.order_summary.subtotal,
                shipping_fee: cartData.order_summary.shipping,
                tax: 0,
                discount: cartData.order_summary.discount,
                coupon_discount: cartData.order_summary.coupon_discount,
                total: cartData.order_summary.total
            }
            items = cartData.order_summary.items.map((item) => {
                return {
                    id: item.id,
                    slug: item.slug,
                    name: item.name,
                    quantity: item.quantity,
                    selling_price: item.selling_price,
                    mrp: item.mrp,
                    options: item.options,
                    image: item.images[0],
                    product_total: item.product_total
                }
            })
        }

        const order = {
            order_number: Math.random().toString(36).substring(2, 10),
            user_id: userId,
            shipping_address: address,
            billing_address: address,
            order_summary: orderSummary,
            payment_details: {
                payment_method: "COD",
                payment_status: "Pending",
                paid_amount: 0,
            },
            status: "Pending",
            created_at: Timestamp.now(),
            updated_at: Timestamp.now(),
            items: items,
        };

        const db = getFirestore(app)
        const orderRef = collection(db, "orders")
        const doc = await addDoc(orderRef, order)
        if (!productId) {
            await clearCart()
        }

        return [order.order_number, doc.id]
    }

    async getOrders(page: number, per_page: number): Promise<Order[]> {
        const app = AppConfig()
        const user = getAuth(app).currentUser
        if (!user) {
            throw new Error("User not authenticated");
        }

        const userId = user.uid;
        let orderQuery = query(collection(getFirestore(app), "orders"), where("user_id", "==", userId), orderBy("created_at", "desc"), limit(per_page))
        if (page !== 0) {
            const lastTimestamp = Timestamp.fromMillis(page)
            orderQuery = query(orderQuery, startAfter(lastTimestamp))
        }
        const orderSnapshot = await getDocs(orderQuery)
        const orders: Order[] = []
        orderSnapshot.forEach((doc) => {
            const order = doc.data() as Order
            order.id = doc.id
            orders.push(order)
        })
        return orders
    }

    async getOrderDetails(id: string): Promise<Order | null> {
        const app = AppConfig()
        const user = getAuth(app).currentUser
        if (!user) {
            throw new Error("User not authenticated");
        }
        const docRef = doc(getFirestore(app), "orders", id)
        const orderSnapshot = await getDoc(docRef)
        return orderSnapshot.data() as Order
    }

    cancelOrder(_: string): Promise<void> {
        throw new Error("Method not implemented.");
    }
}