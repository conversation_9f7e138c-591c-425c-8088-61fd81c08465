import { CartDetails } from "@/types/cart";
import { Order } from "@/types/order";
import { ProductVariant } from "@/types/product/product";
import { UserAddress } from "@/types/user";

export abstract class CheckoutRepository {
    abstract getCartItems(coupon?: string, id?: string): Promise<CartDetails>
    abstract addToCart(product: string, qty: number): Promise<void>
    abstract getWishlistItems(): Promise<ProductVariant[]>
    abstract updateWishlist(product: ProductVariant, isAdded: boolean): Promise<void>
    abstract placeOrder(address: UserAddress, coupon?: string, productId?: number | string): Promise<[string, string]>
    abstract getOrders(page: number, per_page: number): Promise<Order[]>
    abstract getOrderDetails(id: string): Promise<Order | null>
    abstract cancelOrder(id: string): Promise<void>
}

export const checkoutRepository: () => Promise<CheckoutRepository> = async () => {
    if (import.meta.env.VITE_BASIC_HOST === "FB") {
        return new (await import("./checkout-fb-repository")).CheckoutRepositoryImpl()
    } else if (import.meta.env.VITE_BASIC_ENV === "prod") {
        return new (await import("./checkout-repository-impl")).CheckoutRepositoryImpl()
    } else {
        throw new Error("Method not implemented.")
    }
}