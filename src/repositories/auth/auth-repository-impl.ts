import { UserAddress, UserResponse } from "@/types/user";
import { AuthRepository } from "./auth-repository";
import fetchApi from "@/utils/cache-utils";
import { API_ENDPOINTS } from "@/utils/api-const";

export class AuthRepositoryImpl implements AuthRepository {

    register(body: any): Promise<UserResponse> {
        throw new Error("Method not implemented." + body);
    }

    async login(method: string, body: any): Promise<UserResponse> {
        return await fetchApi(API_ENDPOINTS.login(method), {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify(body)
        })
    }

    async addAddress(data: any): Promise<any> {
        return await fetchApi(API_ENDPOINTS.addAddress, {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify(data)
        })
    }

    async getAddress(): Promise<UserAddress[]> {
        const response = await fetchApi(API_ENDPOINTS.getAddress(1, 100)) as any
        return response.data
    }

    async updateAddress(_: any): Promise<any> {
        throw new Error("Method not implemented.");
    }

    async deleteAddress(_: number | string): Promise<any> {
        throw new Error("Method not implemented.");
    }

}