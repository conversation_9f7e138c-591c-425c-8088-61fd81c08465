import { UserResponse, UserAddress } from "@/types/user";
import { AuthRepository } from "./auth-repository";
import AppConfig from "@/utils/app-config";
import { getAuth, createUserWithEmailAndPassword, updateProfile, signInWithEmailAndPassword, signInWithPopup, GoogleAuthProvider } from "firebase/auth";
import { getFirestore, query, collection, addDoc, getDocs, doc, setDoc, deleteDoc } from "firebase/firestore";

export class AuthRepositoryImpl extends AuthRepository {

    async register(body: any): Promise<UserResponse> {
        const app = AppConfig()
        const auth = getAuth(app)

        const user = await createUserWithEmailAndPassword(auth, body.email, body.password)
        if (!user) {
            throw new Error("User registration failed");
        }
        await updateProfile(auth.currentUser!, {
            displayName: body.name,
        })
        return {
            access_token: "",
            data: {
                exp: "",
                iat: "",
                refresh_expiry: "",
                user: {
                    email: body.email,
                    is_email_verified: user.user.emailVerified || false,
                    first_name: body.name,
                    id: user.user.uid,
                    is_active: true,
                }
            },
            refresh_token: "",
            success: true
        }

    }

    async login(method: string, body: any): Promise<UserResponse> {
        const app = AppConfig()
        const auth = getAuth(app)
        if (method === "email") {
            try {
                const user = await signInWithEmailAndPassword(auth, body.email, body.password)
                if (!user) {
                    throw new Error("User login failed");
                }

                return {
                    access_token: "",
                    data: {
                        exp: "",
                        iat: "",
                        refresh_expiry: "",
                        user: {
                            email: body.email,
                            is_email_verified: user.user.emailVerified || false,
                            first_name: body.name,
                            id: user.user.uid,
                            is_active: true,
                        }
                    },
                    refresh_token: "",
                    success: true
                }
            } catch (error) {
                throw new Error("User login failed");
            }
        } else if (method === "google") {
            const provider = new GoogleAuthProvider();
            const user = await signInWithPopup(auth, provider)
            if (!user) {
                throw new Error("User login failed");
            }
            return {
                access_token: "",
                data: {
                    exp: "",
                    iat: "",
                    refresh_expiry: "",
                    user: {
                        email: user.user.email || "",
                        is_email_verified: user.user.emailVerified || false,
                        first_name: user.user.displayName || "",
                        id: user.user.uid,
                        is_active: true,
                        image_url: user.user.photoURL || "",
                        primary_phone: user.user.phoneNumber || "",
                    }
                },
                refresh_token: "",
                success: true
            }
        }
        throw new Error("Method not implemented.");
    }

    async addAddress(data: any): Promise<any> {
        const app = AppConfig()
        const db = getFirestore(app)
        const user = getAuth(app).currentUser
        if (!user) {
            throw new Error("User not authenticated");
        }
        const userId = user.uid
        const addressRef = collection(db, "users", userId, "addresses")
        const addressData = {
            ...data,
            created_at: new Date(),
            updated_at: new Date(),
        }
        const docRef = await addDoc(addressRef, addressData)
        return {
            id: docRef.id,
            ...addressData,
        }
    }

    async getAddress(): Promise<UserAddress[]> {
        const app = AppConfig()
        const db = getFirestore(app)
        const user = getAuth(app).currentUser
        if (!user) {
            throw new Error("User not authenticated");
        }
        const userId = user.uid
        const addresses: UserAddress[] = []
        const q = query(collection(db, "users", userId, "addresses"))
        const data = await getDocs(q)
        data.forEach((doc) => {
            const address = doc.data() as UserAddress
            address.id = doc.id
            addresses.push(address)
        }
        )
        return addresses
    }

    async updateAddress(data: any): Promise<any> {
        const app = AppConfig()
        const db = getFirestore(app)
        const user = getAuth(app).currentUser
        if (!user) {
            throw new Error("User not authenticated");
        }
        if (!data.id || typeof data.id !== "string") {
            throw new Error("Address ID is required");
        }
        const userId = user.uid
        const addressRef = doc(db, "users", userId, "addresses", data.id)
        const addressData = {
            ...data,
            updated_at: new Date(),
        }
        await setDoc(addressRef, addressData)
        return {
            id: data.id,
            ...addressData,
        }
    }

    async deleteAddress(id: number | string): Promise<any> {
        const app = AppConfig()
        const db = getFirestore(app)
        const user = getAuth(app).currentUser
        if (!user) {
            throw new Error("User not authenticated");
        }
        if (!id || typeof id !== "string") {
            throw new Error("Address ID is required");
        }
        const userId = user.uid
        const addressRef = doc(db, "users", userId, "addresses", id)
        await deleteDoc(addressRef)
    }

}