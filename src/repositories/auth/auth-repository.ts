import { UserAddress, UserResponse } from "@/types/user";

export abstract class AuthRepository {
    abstract login(method: string, body: any): Promise<UserResponse>
    abstract register(body: any): Promise<UserResponse>
    abstract addAddress(data: any): Promise<any>
    abstract getAddress(): Promise<UserAddress[]>
    abstract updateAddress(data: any): Promise<any>
    abstract deleteAddress(id: number | string): Promise<any>
}

export const authRepository: () => Promise<AuthRepository> = async () => {
    if (import.meta.env.VITE_BASIC_HOST === "FB") {
        return new (await import("./auth-fb-repository")).AuthRepositoryImpl()
    } else if (import.meta.env.VITE_BASIC_ENV === "prod") {
        return new (await import("./auth-repository-impl")).AuthRepositoryImpl()
    } else {
        throw new Error("Method not implemented.")
    }
}