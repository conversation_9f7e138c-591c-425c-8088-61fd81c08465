import { ProductResponse, ProductVariant } from "../../types/product/product";
import { ProductRepository } from "./product-repository";
import { FilterModel } from "@/types/home";

export class ProductFakeRepositoryImpl extends ProductRepository {
    searchProducts(_: string, __: number, ___: number): Promise<Pagination<ProductVariant>> {
        throw new Error("Method not implemented.");
    }
    getUniqueProducts(_: number, __: number): Promise<Pagination<ProductVariant>> {
        throw new Error("Method not implemented.");
    }
    getFilters(_: string): Promise<FilterModel> {
        throw new Error("Method not implemented.");
    }

    getProducts(_?: string): Promise<Pagination<ProductVariant>> {
        throw new Error("Method not implemented.");
    }

    async getProductDetails(_: string): Promise<ProductResponse | undefined> {
        throw new Error("Method not implemented.");
    }

    async getReleatedProducts(_: string): Promise<ProductVariant[]> {
        throw new Error("Method not implemented.");
    }

}