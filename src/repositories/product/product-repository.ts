import { FilterModel } from "@/types/home";
import { ProductResponse, ProductVariant } from "@/types/product/product";

export abstract class ProductRepository {
    abstract getUniqueProducts(page: number, size: number): Promise<Pagination<ProductVariant>>
    abstract getProducts(params?: string): Promise<Pagination<ProductVariant>>
    abstract getFilters(categoryId: string): Promise<FilterModel>
    abstract getProductDetails(slug: string): Promise<ProductResponse | undefined>
    abstract getReleatedProducts(slug: string): Promise<ProductVariant[]>
    abstract searchProducts(query: string, page: number, size: number): Promise<Pagination<ProductVariant>>
}

export const productRepository = async () => {
    if (import.meta.env.VITE_BASIC_HOST === "FB") {
        return new (await import("./product-fb-repository")).ProductRepositoryImpl()
    } else if (import.meta.env.VITE_BASIC_ENV === "prod") {
        return new (await import("./product-respository-impl")).ProductRepositoryImpl()
    } else if (import.meta.env.VITE_BASIC_ENV === "dev") {
        return new (await import("./product-fake-repository-impl")).ProductFakeRepositoryImpl()
    } else {
        throw new Error("Method not implemented.")
    }
}