export const productRepository = async () => {
    // return new (await import(/* @vite-ignore */ import.meta.env.REPO_PATH)).default();
    if (import.meta.env.VITE_BASIC_ENV === "prod") {
        return new (await import("./product-respository-impl")).ProductRepositoryImpl()
    } else if (import.meta.env.VITE_BASIC_ENV === "dev") {
        return new (await import("./product-fake-repository-impl")).ProductFakeRepositoryImpl()
    } else {
        throw new Error("Method not implemented.")
    }
}