import fetchApi from "@/utils/cache-utils";
import { ProductResponse, ProductVariant } from "../../types/product/product";
import { ProductRepository } from "./product-repository";
import { API_ENDPOINTS } from "@/utils/api-const";
import { FilterModel } from "@/types/home";

export class ProductRepositoryImpl extends ProductRepository {

    async searchProducts(query: string, page: number, size: number): Promise<Pagination<ProductVariant>> {
        const response = await fetchApi(API_ENDPOINTS.searchProducts(query, page, size)) as any
        return response
    }

    async getFilters(categoryId: string): Promise<FilterModel> {
        const response = await fetchApi(API_ENDPOINTS.filters(categoryId)) as any
        return response.data
    }

    async getUniqueProducts(page: number, size: number): Promise<Pagination<ProductVariant>> {
        return await fetchApi(API_ENDPOINTS.uniqueProductsList(page, size))
    }

    async getProducts(params?: string): Promise<Pagination<ProductVariant>> {
        return await fetchApi(API_ENDPOINTS.productsList(params))
    }

    async getProductDetails(slug: string): Promise<ProductResponse | undefined> {
        return (await fetchApi(API_ENDPOINTS.productDetails(slug)) as any).data
    }

    async getReleatedProducts(slug: string): Promise<ProductVariant[]> {
        console.log(slug);
        return fetch(
            `${import.meta.env.VITE_API_BASE_URL}/api/products/list/uniq-product-variants?page=1&per_page=10`
        ).then(async (res) => {
            console.log(res);
            const data = await res.json()
            return data["data"]
        }
        );
    }

}