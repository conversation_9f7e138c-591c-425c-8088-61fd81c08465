import { FilterModel } from "@/types/home";
import { ProductVariant, ProductResponse } from "@/types/product/product";
import { ProductRepository } from "./product-repository"
import { getJsonFile } from "@/utils/fb-utils";
import Fuse from "fuse.js";

export class ProductRepositoryImpl extends ProductRepository {
    async getUniqueProducts(page: number, size: number): Promise<Pagination<ProductVariant>> {
        let products = await this.getProductsFromFb()
        products = products.filter((product: any) => product.variants.length > 0);
        const start = (page - 1) * size;
        const end = start + size;
        const paginatedProducts = products.slice(start, end);
        const total = products.length;
        const variants = paginatedProducts.map((product: any) => product.variants[0])
        return {
            current_page: page,
            data: variants,
            pages: Math.ceil(total / size),
            total: total
        }
    }
    async getProducts(params?: string): Promise<Pagination<ProductVariant>> {
        let products = await this.getProductsFromFb()

        const queryParams = new URLSearchParams(params);
        const page = Number(queryParams.get('page')) || 1;
        const size = Number(queryParams.get('per_page')) || 12;
        const categoryId = queryParams.get('category_id');
        const subCategoryId = queryParams.get('sub_category_id');
        const brandId = queryParams.get('brand_id');
        const sortBy = queryParams.get('sort_by');
        const sortOrder = queryParams.get('sort_order');


        if (categoryId) {
            products = products.filter((product: any) =>
                String(product.category_id) === categoryId
            );
        }

        if (subCategoryId) {
            products = products.filter((product: any) =>
                String(product.sub_category_id) === subCategoryId
            );
        }
        if (brandId) {
            const brandIds = brandId.split(",").map((id: string) => Number(id));
            products = products.filter((product: any) =>
                brandIds.includes(product.brand_id)
            );
        }

        let filteredVariants = products.flatMap((product: any) => product.variants);

        if (queryParams.get('options')) {
            const options = queryParams.get('options')?.split(",")
            let optionData = options?.map((option: string) => {
                const [key, value] = option.split(":");
                return { key, value };
            });
            const maxPrice = optionData?.find((option: any) => option.key === "max-price")?.value
            if (maxPrice) {
                filteredVariants = filteredVariants.filter((variant: any) => {
                    return variant.selling_price <= Number(maxPrice)
                })
                optionData = optionData?.filter((option: any) => option.key !== "max-price")
            }
            filteredVariants = filteredVariants.filter((variant: any) => {
                return optionData?.every(({ key, }) => key in variant.options)
            })
            filteredVariants = filteredVariants.filter((variant: any) => {
                return optionData?.every(({ key, value }) => {
                    const normalizedValue = (variant.options[key] as string).toLowerCase();
                    return normalizedValue === value.toLowerCase();
                });
            });
        }

        if (sortBy && sortOrder) {
            filteredVariants.sort((a: any, b: any) => {
                const aValue = a[sortBy];
                const bValue = b[sortBy];
                if (sortOrder === "asc") {
                    return aValue - bValue;
                } else {
                    return bValue - aValue;
                }
            });
        } else {
            filteredVariants.sort((a: any, b: any) => {
                const aDate = new Date(a.created_at);
                const bDate = new Date(b.created_at);
                return bDate.getTime() - aDate.getTime();
            });
        }

        const total = filteredVariants.length;
        const start = (page - 1) * size;
        const end = start + size;
        const paginatedVariants = filteredVariants.slice(start, end);

        return {
            current_page: page,
            data: paginatedVariants,
            pages: Math.ceil(total / size),
            total
        };
    }

    async getFilters(categoryId: string): Promise<FilterModel> {
        let products = await this.getProductsFromFb()
        const brandMap = new Map<number, string>();
        const optionsMap = new Map<string, Set<string>>();
        let maxPrice = 0;

        if (categoryId) {
            products = products.filter((product: any) =>
                String(product.category_id) === categoryId
            );
        }

        products.forEach((product: any) => {
            brandMap.set(product.brand_id, product.brand);

            product.variants.forEach((variant: any) => {
                maxPrice = Math.max(maxPrice, variant.selling_price);

                Object.entries(variant.options).forEach(([key, value]) => {
                    const normalizedValue = (value as string).toLowerCase();
                    if (!optionsMap.has(key)) {
                        optionsMap.set(key, new Set());
                    }
                    optionsMap.get(key)!.add(normalizedValue);
                });
            });
        });

        const options: any = {};
        optionsMap.forEach((values, key) => {
            options[key] = Array.from(values);
        });

        return {
            brands: Array.from(brandMap.entries()).map(([id, name]) => ({ id, name })),
            max_price: maxPrice,
            options,
        };
    }

    async getProductDetails(slug: string): Promise<ProductResponse | undefined> {
        const products = await this.getProductsFromFb()
        const variants = products.flatMap((product: any) => product.variants);
        const variant = variants.find((variant: any) => variant.slug === slug);
        if (!variant) return undefined
        const product = products.find((product: any) => product.id === variant.product_id)
        const minimalVariants = product.variants;
        product["variants_minimal"] = minimalVariants
        return {
            product: product,
            selected_id: variant.id,
            variant_details: variant
        }
    }

    async getReleatedProducts(slug: string): Promise<ProductVariant[]> {
        const products = await this.getProductsFromFb()
        const variants = products.flatMap((product: any) => product.variants);
        const variant = variants.find((variant: any) => variant.slug === slug);
        if (!variant) return []
        const product = products.find((product: any) => product.id === variant.product_id)
        const categoryId = product.category_id
        const relatedProducts = product.filter((p: any) => p.category_id === categoryId)
        return relatedProducts.flatMap((product: any) => product.variants);
    }

    async searchProducts(query: string, page: number, size: number): Promise<Pagination<ProductVariant>> {
        const products = await this.getProductsFromFb()
        const options = {
            includeScore: true,
            threshold: 0.3,
            ignoreLocation: true,
            useExtendedSearch: true,
            keys: [
                "name",
                "brand",
                "category",
                "sub_category",
                "variants.name",
                "variants.description",
                "variants.options.color",
                "variants.options.size",
            ]
        }
        const fuse = new Fuse(products, options);
        const result = fuse.search(query);
        const filteredProducts = result.map((result: any) => result.item);
        const variants = filteredProducts.flatMap((product: any) => product.variants);
        const total = variants.length;
        const start = (page - 1) * size;
        const end = start + size;
        const paginatedVariants = variants.slice(start, end);
        return {
            current_page: page,
            data: paginatedVariants,
            pages: Math.ceil(total / size),
            total: total
        };
    }

    async getProductsFromFb(): Promise<any> {
        return await getJsonFile("products.json")
    }

}