// import { slides } from "@/test/hero-slides";
import { testimonialsWithProduct9 } from "@/test/home-data";
// import { navData } from "@/test/nav-data";
// import { bannerCountdown } from "@/test/products/banner-collections";
import { Category } from "@/types/product/collection";
import { Slide } from "@/types/product/slide";
import { HomeRepository } from "./home-repository";
import { BannerCountdown, Testimonial } from "@/types/product/banner-collection";

export class HomeRepositoryImpl extends HomeRepository {

    async fetchCountdownBanner(): Promise<BannerCountdown[]> {
        throw Error()
        // return [bannerCountdown]
    }

    async getTestimonials(): Promise<Testimonial[]> {
        return testimonialsWithProduct9
    }
    async getSlides(): Promise<Slide[]> {
        throw Error()
        // return slides
    }

    async getNavData(): Promise<Category[]> {
        throw Error()
        // return navData
    }

}