import { BannerCountdown, Testimonial } from "@/types/product/banner-collection";
import { Category } from "@/types/product/collection";
import { Slide } from "@/types/product/slide";

export abstract class HomeRepository {
    abstract getSlides(): Promise<Slide[]>
    abstract getNavData(): Promise<Category[]>
    abstract fetchCountdownBanner(): Promise<BannerCountdown[]>
    abstract getTestimonials(): Promise<Testimonial[]>
}

export const homeRepository: () => Promise<HomeRepository> = async () => {
    if (import.meta.env.VITE_BASIC_HOST === "FB") {
        return new (await import("./home-fb-repository")).HomeRepositoryImpl()
    } else if (import.meta.env.VITE_BASIC_ENV === "prod") {
        return new (await import("./home-repository-impl")).HomeRepositoryImpl()
    } else if (import.meta.env.VITE_BASIC_ENV === "dev") {
        return new (await import("./home-fake-repository-impl")).HomeRepositoryImpl()
    } else {
        throw new Error("Method not implemented.")
    }
}