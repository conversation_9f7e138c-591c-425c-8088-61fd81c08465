import { Category } from "@/types/product/collection";
import { Slide } from "@/types/product/slide";
import { HomeRepository } from "./home-repository";
import fetchApi from "@/utils/cache-utils";
import { API_ENDPOINTS } from "@/utils/api-const";
import { BannerCountdown, Testimonial } from "@/types/product/banner-collection";
import { testimonialsWithProduct9 } from "@/test/home-data";

export class HomeRepositoryImpl extends HomeRepository {

    async getTestimonials(): Promise<Testimonial[]> {
        return testimonialsWithProduct9
    }

    async fetchCountdownBanner(): Promise<BannerCountdown[]> {
        return (await fetchApi<any>(API_ENDPOINTS.countdownBannerList(1, 1))).data
    }

    async getSlides(): Promise<Slide[]> {
        return (await fetchApi<any>(API_ENDPOINTS.sliders)).data
    }

    async getNavData(): Promise<Category[]> {
        return (await fetchApi<any>(API_ENDPOINTS.categories)).data
    }

}