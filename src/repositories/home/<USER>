import { BannerCountdown, Testimonial } from "@/types/product/banner-collection";
import { Category } from "@/types/product/collection";
import { Slide } from "@/types/product/slide";
import { HomeRepository } from "./home-repository";
import { testimonialsWithProduct9 } from "@/test/home-data";
import { getJsonFile } from "@/utils/fb-utils";

export class HomeRepositoryImpl extends HomeRepository {

    async getSlides(): Promise<Slide[]> {
        const others = await getJsonFile("others.json");
        return others.slides;
    }

    async getNavData(): Promise<Category[]> {
        return getJsonFile("categories.json")
    }

    async fetchCountdownBanner(): Promise<BannerCountdown[]> {
        const others = await getJsonFile("others.json");
        return others.countdown_banner;
    }

    async getTestimonials(): Promise<Testimonial[]> {
        return testimonialsWithProduct9
    }

}