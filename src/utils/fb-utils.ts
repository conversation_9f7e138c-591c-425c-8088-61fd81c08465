import { getStorage, ref, getDownloadURL } from 'firebase/storage';
import AppConfig from './app-config';
import { getLocalData, removeLocalData, setLocalData } from './file-utils';

export async function getJsonFile(fileName: string) {
    // Check local storage first
    const cachedData = await getLocalData(`json_${fileName}`);
    if (cachedData) {
        try {
            return JSON.parse(cachedData);
        } catch (error) {
            console.warn('Failed to decrypt cached data, fetching fresh data');
            removeLocalData(`json_${fileName}`);
        }
    }

    try {
        // If not in cache or decryption failed, fetch from Firebase storage
        const prefix = import.meta.env.VITE_API_STORAGE_BUCKET
        const app = AppConfig()
        const storage = getStorage(app);
        const fileRef = ref(storage, prefix + fileName);
        const downloadUrl = await getDownloadURL(fileRef);

        // Fetch the JSON data
        const response = await fetch(downloadUrl);
        const jsonData = await response.json();

        // Store the data in local storage
        setLocalData(`json_${fileName}`, JSON.stringify(jsonData));

        return jsonData;
    } catch (error) {
        console.error(`Error fetching ${fileName} from Firebase storage:`, error);
        throw error;
    }
}