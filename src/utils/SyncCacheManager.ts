import { doc, getDoc, getFirestore, onSnapshot } from 'firebase/firestore';
import { useQueryClient } from '@tanstack/react-query';
import { useEffect, useRef } from 'react';
import AppConfig from './app-config';
import { getLocalData, setLocalData } from './file-utils';

interface SyncData {
    categories?: any; // Firebase Timestamp
    brands?: any; // Firebase Timestamp  
    products?: any; // Firebase Timestamp
    sellers?: any; // Firebase Timestamp
    sliders?: any; // Firebase Timestamp
    is_syncing?: boolean;
}

interface CacheTimestamps {
    categories?: number;
    brands?: number;
    products?: number;
    sellers?: number;
    sliders?: number;
}

class SyncCacheManager {
    private static instance: SyncCacheManager;
    private queryClient: any = null;
    private unsubscribe: (() => void) | null = null;
    private isInitialized = false;

    private constructor() { }

    static getInstance(): SyncCacheManager {
        if (!SyncCacheManager.instance) {
            SyncCacheManager.instance = new SyncCacheManager();
        }
        return SyncCacheManager.instance;
    }

    initialize(queryClient: any) {
        if (this.isInitialized) return;

        this.queryClient = queryClient;
        this.isInitialized = true;
        this.startSyncDataListener();
    }

    private async startSyncDataListener() {
        try {
            const app = AppConfig();
            const db = getFirestore(app);
            const syncDataRef = doc(db, 'app_data/sync_data');

            // First check current sync data and compare with cached timestamps
            await this.checkAndInvalidateStaleCache();

            // Set up real-time listener for sync data changes
            this.unsubscribe = onSnapshot(syncDataRef, async (docSnapshot) => {
                if (docSnapshot.exists()) {
                    const syncData = docSnapshot.data() as SyncData;
                    await this.compareAndInvalidateCache(syncData);
                }
            }, (error) => {
                console.error('Error listening to sync data:', error);
            });

        } catch (error) {
            console.error('Error setting up sync data listener:', error);
        }
    }

    private async checkAndInvalidateStaleCache() {
        try {
            const app = AppConfig();
            const db = getFirestore(app);
            const syncDataRef = doc(db, 'app_data/sync_data');
            const docSnapshot = await getDoc(syncDataRef);

            if (docSnapshot.exists()) {
                const syncData = docSnapshot.data() as SyncData;
                await this.compareAndInvalidateCache(syncData);
            }
        } catch (error) {
            console.error('Error checking sync data:', error);
        }
    }

    private async compareAndInvalidateCache(syncData: SyncData) {
        try {
            // Get cached timestamps from local storage
            const cachedTimestampsStr = await getLocalData('cache_timestamps');
            const cachedTimestamps: CacheTimestamps = cachedTimestampsStr
                ? JSON.parse(cachedTimestampsStr)
                : {};

            let shouldUpdateCache = false;
            const newTimestamps: CacheTimestamps = { ...cachedTimestamps };

            // Check each data type (excluding is_syncing as it's not a data type we cache)
            const dataTypes: (keyof CacheTimestamps)[] = ['categories', 'brands', 'products', 'sellers', 'sliders'];

            for (const dataType of dataTypes) {
                const firebaseTimestamp = syncData[dataType];

                if (firebaseTimestamp && firebaseTimestamp.toMillis) {
                    const firebaseTime = firebaseTimestamp.toMillis();
                    const cachedTime = cachedTimestamps[dataType] || 0;

                    // If Firebase timestamp is newer than cached timestamp, invalidate cache
                    if (firebaseTime > cachedTime) {
                        var fileName = `json_${dataType}.json`;
                        if (['brands', 'sellers', 'sliders'].includes(dataType)) {
                            fileName = `json_others.json`;
                        }
                        await this.invalidateQueryCache(dataType);
                        await setLocalData(fileName, '');
                        newTimestamps[dataType] = firebaseTime;
                        shouldUpdateCache = true;
                    }
                }
            }

            // Update cached timestamps if any were invalidated
            if (shouldUpdateCache) {
                await setLocalData('cache_timestamps', JSON.stringify(newTimestamps));
            }

        } catch (error) {
            console.error('Error comparing and invalidating cache:', error);
        }
    }

    private async invalidateQueryCache(dataType: keyof CacheTimestamps) {
        if (!this.queryClient) return;

        try {
            switch (dataType) {
                case 'categories':
                    await this.queryClient.invalidateQueries({ queryKey: ['categories'] });
                    break;
                case 'brands':
                    await this.queryClient.invalidateQueries({ queryKey: ['brands'] });
                    break;
                case 'products':
                    await this.queryClient.invalidateQueries({ queryKey: ['products'] });
                    await this.queryClient.invalidateQueries({ queryKey: ['draft_products'] });
                    break;
                case 'sellers':
                    await this.queryClient.invalidateQueries({ queryKey: ['sellers'] });
                    break;
                case 'sliders':
                    await this.queryClient.invalidateQueries({ queryKey: ['sliders'] });
                    break;
            }

            console.log(`Successfully invalidated ${dataType} cache`);
        } catch (error) {
            console.error(`Error invalidating ${dataType} cache:`, error);
        }
    }

    destroy() {
        if (this.unsubscribe) {
            this.unsubscribe();
            this.unsubscribe = null;
        }
        this.isInitialized = false;
        this.queryClient = null;
    }
}

// React hook to use the sync cache manager
export function useSyncCacheManager() {
    const queryClient = useQueryClient();
    const managerRef = useRef<SyncCacheManager | null>(null);

    useEffect(() => {
        // Initialize sync cache manager
        managerRef.current = SyncCacheManager.getInstance();
        managerRef.current.initialize(queryClient);

        // Cleanup on unmount
        return () => {
            if (managerRef.current) {
                managerRef.current.destroy();
            }
        };
    }, [queryClient]);

    return managerRef.current;
}

export default SyncCacheManager;
