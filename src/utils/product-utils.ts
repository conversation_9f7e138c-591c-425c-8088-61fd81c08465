import { ProductResponse, ProductVariant, VariantDetails } from "../types/product/product";

interface FilteredProduct {
    id: number;
    key: string;
    value: string;
    stock: number;
}

export const filterProductsByColor = (options: VariantDetails[], variant: ProductVariant): FilteredProduct[][] => {
    const selectedOptions = variant.options;
    const initialKey = "color";
    var filterKey = initialKey;
    const keys = Object.keys(variant.options).filter((key) => key !== initialKey);

    const result = new Map<string, any[]>();

    var filteredOptions = options

    for (const key of keys) {
        const seenValues = new Set();
        const filteredList = [];
        filteredOptions = filteredOptions.filter((product: any) => selectedOptions[filterKey] ? product.options[filterKey] === selectedOptions[filterKey] : true);
        filterKey = key;
        for (const { id, slug, options, stock } of filteredOptions) {
            const value = options[key];

            if (!seenValues.has(value)) {
                seenValues.add(value);
                filteredList.push({
                    id,
                    slug,
                    key,
                    value,
                    stock,
                });
            }
        }
        result.set(key, filteredList);
    }

    return Object.values(Object.fromEntries(result));

};

declare global {
    interface String {
        capitalizeFirstLetter(): string;
    }
}

String.prototype.capitalizeFirstLetter = function (): string {
    if (this == null) {
        return '';
    }
    return this.charAt(0).toUpperCase() + this.slice(1);
};

export const compareSpec = (data: (ProductResponse | undefined)[]) => {
    const result = new Map<string, string[]>();
    const options = data.filter((item) => item?.variant_details).map((item) => item!.variant_details);

    // First pass: collect all possible keys
    for (const { spec } of options) {
        if (spec) {
            Object.keys(spec).forEach(key => {
                if (!result.has(key)) {
                    result.set(key, []);
                }
            });
        }
    }

    // Second pass: populate values for each key
    const keys = Array.from(result.keys());
    for (const { spec } of options) {
        for (const key of keys) {
            result.get(key)?.push(spec && spec[key] ? spec[key] : "-");
        }
    }

    return Array.from(result.entries()).map(([key, values]) => ({
        key,
        value: values
    }));
}