const ENCRYPTION_KEY = import.meta.env.VITE_ENCRYPTION_KEY || 'your-secret-key-32-characters-long';

// Utility function to convert string to Uint8Array
function str2ab(str: string): Uint8Array {
    const encoder = new TextEncoder();
    return encoder.encode(str);
}

// Utility function to convert Uint8Array to string
function ab2str(buf: ArrayBuffer): string {
    const decoder = new TextDecoder();
    return decoder.decode(buf);
}

// Convert ArrayBuffer to Base64 string safely
function arrayBufferToBase64(buffer: ArrayBuffer): string {
    const bytes = new Uint8Array(buffer);
    let binary = '';
    for (let i = 0; i < bytes.byteLength; i++) {
        binary += String.fromCharCode(bytes[i]);
    }
    return btoa(binary);
}

// Convert Base64 string to ArrayBuffer safely
function base64ToArrayBuffer(base64: string): ArrayBuffer {
    const binaryString = atob(base64);
    const bytes = new Uint8Array(binaryString.length);
    for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i);
    }
    return bytes.buffer;
}

// Function to derive encryption key from password
async function getKey(password: string): Promise<CryptoKey> {
    const keyMaterial = await crypto.subtle.importKey(
        'raw',
        str2ab(password),
        { name: 'PBKDF2' },
        false,
        ['deriveBits', 'deriveKey']
    );

    return crypto.subtle.deriveKey(
        {
            name: 'PBKDF2',
            salt: str2ab('salt'),
            iterations: 100000,
            hash: 'SHA-256'
        },
        keyMaterial,
        { name: 'AES-GCM', length: 256 },
        true,
        ['encrypt', 'decrypt']
    );
}

// Encrypt data
async function encryptData(data: string): Promise<string> {
    const key = await getKey(ENCRYPTION_KEY);
    const iv = crypto.getRandomValues(new Uint8Array(12));
    const encryptedContent = await crypto.subtle.encrypt(
        {
            name: 'AES-GCM',
            iv: iv
        },
        key,
        str2ab(data)
    );

    const encryptedContentArr = new Uint8Array(encryptedContent);
    const buf = new Uint8Array(iv.length + encryptedContentArr.length);
    buf.set(iv, 0);
    buf.set(encryptedContentArr, iv.length);

    return arrayBufferToBase64(buf.buffer);
}

// Decrypt data
async function decryptData(encryptedData: string): Promise<string> {
    const key = await getKey(ENCRYPTION_KEY);
    const data = new Uint8Array(base64ToArrayBuffer(encryptedData));
    const iv = data.slice(0, 12);
    const ciphertext = data.slice(12);

    const decrypted = await crypto.subtle.decrypt(
        {
            name: 'AES-GCM',
            iv: iv
        },
        key,
        ciphertext
    );

    return ab2str(decrypted);
}

export async function getLocalData(key: string): Promise<string | null> {
    const data = localStorage.getItem(key)
    if (data) {
        return await decryptData(data);;
    }
    return null;
}

export async function setLocalData(key: string, value: string): Promise<void> {
    const encryptedValue = await encryptData(value);
    localStorage.setItem(key, encryptedValue);
}
export async function removeLocalData(key: string): Promise<void> {
    localStorage.removeItem(key);
}
export async function clearLocalData(): Promise<void> {
    localStorage.clear();
}