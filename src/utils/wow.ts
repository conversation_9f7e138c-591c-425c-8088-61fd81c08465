// function isIn<T>(needle: T, haystack: T[]): boolean {
//     return haystack.indexOf(needle) >= 0;
// }

// function extend<T>(custom: T, defaults: T): T {
//     for (const key in defaults) {
//         if ((custom as any)[key] == null) {
//             const value = (defaults as any)[key];
//             (custom as any)[key] = value;
//         }
//     }
//     return custom;
// }

// function isMobile(agent: string): boolean {
//     return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(agent);
// }

// function createEvent(event: string, bubble = false, cancel = false, detail: any = null): CustomEvent {
//     let customEvent: CustomEvent;
//     if (document.createEvent != null) {
//         // W3C DOM
//         customEvent = document.createEvent("CustomEvent");
//         customEvent.initCustomEvent(event, bubble, cancel, detail);
//     } else {
//         customEvent = new CustomEvent(event, { detail, bubbles: bubble, cancelable: cancel });
//     }

//     return customEvent;
// }

// function emitEvent(elem: HTMLElement, event: Event): void {
//     if (elem.dispatchEvent != null) {
//         // W3C DOM
//         elem.dispatchEvent(event);
//     } else if ((event as any) in elem) {
//         (elem as any)[(event as any)]();
//     } else if (`on${event}` in elem) {
//         (elem as any)[`on${event}`]();
//     }
// }

// function addEvent(elem: EventTarget, event: string, fn: EventListener): void {
//     if (elem.addEventListener != null) {
//         // W3C DOM
//         elem.addEventListener(event, fn, false);
//     } else if ((elem as any).attachEvent != null) {
//         // IE DOM
//         (elem as any).attachEvent(`on${event}`, fn);
//     } else {
//         // fallback
//         (elem as any)[event] = fn;
//     }
// }

// function removeEvent(elem: EventTarget, event: string, fn: EventListener): void {
//     if (elem.removeEventListener != null) {
//         // W3C DOM
//         elem.removeEventListener(event, fn, false);
//     } else if ((elem as any).detachEvent != null) {
//         // IE DOM
//         (elem as any).detachEvent(`on${event}`, fn);
//     } else {
//         // fallback
//         delete (elem as any)[event];
//     }
// }

// function getInnerHeight(): number {
//     if ("innerHeight" in window) {
//         return window.innerHeight;
//     }

//     return document.documentElement.clientHeight;
// }

// // Minimalistic WeakMap shim, just in case.
// const WeakMap =
//     window.WeakMap ||
//     class WeakMap<K, V> {
//         private keys: K[] = [];
//         private values: V[] = [];

//         get(key: K): V | undefined {
//             for (let i = 0; i < this.keys.length; i++) {
//                 const item = this.keys[i];
//                 if (item === key) {
//                     return this.values[i];
//                 }
//             }
//             return undefined;
//         }

//         set(key: K, value: V): this {
//             for (let i = 0; i < this.keys.length; i++) {
//                 const item = this.keys[i];
//                 if (item === key) {
//                     this.values[i] = value;
//                     return this;
//                 }
//             }
//             this.keys.push(key);
//             this.values.push(value);
//             return this;
//         }
//     };

// // Dummy MutationObserver, to avoid raising exceptions.
// const MutationObserver =
//     window.MutationObserver ||
//     class MutationObserver {
//         static notSupported = true;

//         constructor() {
//             if (typeof console !== "undefined" && console !== null) {
//                 console.warn("MutationObserver is not supported by your browser.");
//                 console.warn(
//                     "WOW.js cannot detect dom mutations, please call .sync() after loading new content."
//                 );
//             }
//         }

//         observe() { }
//     };

// // getComputedStyle shim, from http://stackoverflow.com/a/21797294
// const getComputedStyle =
//     window.getComputedStyle ||
//     function getComputedStyle(el: Element) {
//         const getComputedStyleRX = /(\-([a-z]){1})/g;
//         return {
//             getPropertyValue(prop: string) {
//                 if (prop === "float") {
//                     prop = "styleFloat";
//                 }
//                 if (getComputedStyleRX.test(prop)) {
//                     prop = prop.replace(getComputedStyleRX, (_, _char) => _char.toUpperCase());
//                 }
//                 const { currentStyle } = el as any;
//                 return (currentStyle != null ? currentStyle[prop] : void 0) || null;
//             },
//         };
//     };

// interface WOWOptions {
//     boxClass?: string;
//     animateClass?: string;
//     offset?: number;
//     mobile?: boolean;
//     live?: boolean;
//     callback?: (box: HTMLElement) => void;
//     scrollContainer?: any | null;
// }

// export default class WOW {
//     defaults: WOWOptions = {
//         boxClass: "wow",
//         animateClass: "animated",
//         offset: 0,
//         mobile: true,
//         live: true,
//         callback: undefined,
//         scrollContainer: null,
//     };

//     config: WOWOptions;
//     element!: HTMLElement;
//     boxes!: HTMLElement[];
//     all!: HTMLElement[];
//     animationNameCache: WeakMap<HTMLElement, string>;
//     wowEvent: CustomEvent;
//     stopped!: boolean;
//     scrolled: boolean;
//     interval: ReturnType<typeof setInterval> | undefined;
//     finished!: HTMLElement[];

//     constructor(options: WOWOptions = {}) {
//         this.start = this.start.bind(this);
//         this.resetAnimation = this.resetAnimation.bind(this);
//         this.scrollHandler = this.scrollHandler.bind(this);
//         this.scrollCallback = this.scrollCallback.bind(this);
//         this.scrolled = true;
//         this.config = extend(options, this.defaults);
//         if (options.scrollContainer != null) {
//             this.config.scrollContainer = options.scrollContainer;
//         }
//         // Map of elements to animation names:
//         this.animationNameCache = new WeakMap();
//         this.wowEvent = createEvent(this.config.boxClass!);
//     }

//     init() {
//         this.element = window.document.documentElement;
//         if (isIn(document.readyState, ["interactive", "complete"])) {
//             this.start();
//         } else {
//             addEvent(document, "DOMContentLoaded", this.start);
//         }
//         this.finished = [];
//     }

//     start() {
//         this.stopped = false;
//         this.boxes = [].slice.call(this.element.querySelectorAll(`.${this.config.boxClass}`));
//         this.all = this.boxes.slice(0);
//         if (this.boxes.length) {
//             if (this.disabled()) {
//                 this.resetStyle();
//             } else {
//                 for (let i = 0; i < this.boxes.length; i++) {
//                     const box = this.boxes[i];
//                     this.applyStyle(box, true);
//                 }
//             }
//         }
//         if (!this.disabled()) {
//             const scrollContainer = this.config.scrollContainer ? document.querySelector(this.config.scrollContainer) : window;
//             if (scrollContainer) {
//                 addEvent(scrollContainer, "scroll", this.scrollHandler);
//             }
//             addEvent(window, "resize", this.scrollHandler);
//             this.interval = setInterval(this.scrollCallback, 50);
//         }
//         if (this.config.live) {
//             const mut = new MutationObserver((records) => {
//                 for (let j = 0; j < records.length; j++) {
//                     const record = records[j];
//                     for (let k = 0; k < record.addedNodes.length; k++) {
//                         const node = record.addedNodes[k] as HTMLElement;
//                         this.doSync(node);
//                     }
//                 }
//                 return undefined;
//             });
//             mut.observe(document.body, {
//                 childList: true,
//                 subtree: true,
//             });
//         }
//     }

//     // unbind the scroll event
//     stop() {
//         this.stopped = true;
//         const scrollContainer = this.config.scrollContainer ? document.querySelector(this.config.scrollContainer) : window;
//         if (scrollContainer) {
//             removeEvent(scrollContainer, "scroll", this.scrollHandler);
//         }
//         removeEvent(window, "resize", this.scrollHandler);
//         if (this.interval != null) {
//             clearInterval(this.interval);
//         }
//     }

//     sync() {
//         if ((MutationObserver as any).notSupported) {
//             this.doSync(this.element);
//         }
//     }

//     doSync(element: HTMLElement) {
//         if (typeof element === "undefined" || element === null) {
//             element = this.element;
//         }
//         if (element.nodeType !== 1) {
//             return;
//         }
//         element = element.parentNode as HTMLElement || element;
//         const iterable = element.querySelectorAll(`.${this.config.boxClass}`);
//         for (let i = 0; i < iterable.length; i++) {
//             const box = iterable[i] as HTMLElement;
//             if (!isIn(box, this.all)) {
//                 this.boxes.push(box);
//                 this.all.push(box);
//                 if (this.stopped || this.disabled()) {
//                     this.resetStyle();
//                 } else {
//                     this.applyStyle(box, true);
//                 }
//                 this.scrolled = true;
//             }
//         }
//     }

//     // show box element
//     show(box: HTMLElement) {
//         this.applyStyle(box);
//         box.className = `${box.className} ${this.config.animateClass}`;
//         if (this.config.callback != null) {
//             this.config.callback(box);
//         }
//         emitEvent(box, this.wowEvent);

//         addEvent(box, "animationend", this.resetAnimation);
//         addEvent(box, "oanimationend", this.resetAnimation);
//         addEvent(box, "webkitAnimationEnd", this.resetAnimation);
//         addEvent(box, "MSAnimationEnd", this.resetAnimation);

//         return box;
//     }

//     applyStyle(box: HTMLElement, hidden?: boolean) {
//         const duration = box.getAttribute("data-wow-duration");
//         const delay = box.getAttribute("data-wow-delay");
//         const iteration = box.getAttribute("data-wow-iteration");

//         return this.animate(() =>
//             this.customStyle(box, hidden, duration, delay, iteration)
//         );
//     }

//     animate = (function animateFactory() {
//         if ("requestAnimationFrame" in window) {
//             return (callback: FrameRequestCallback) => window.requestAnimationFrame(callback);
//         }
//         return (callback: FrameRequestCallback) => callback();
//     })();

//     resetStyle() {
//         for (let i = 0; i < this.boxes.length; i++) {
//             const box = this.boxes[i];
//             box.style.visibility = "visible";
//         }
//         return undefined;
//     }

//     resetAnimation(event: Event) {
//         const animationEvent = event as AnimationEvent;
//         if (animationEvent.type.toLowerCase().indexOf("animationend") >= 0) {
//             const target = event.target as HTMLElement;
//             target.className = target.className
//                 .replace(this.config.animateClass!, "")
//                 .trim();
//         }
//     }

//     customStyle(box: HTMLElement, hidden?: boolean, duration?: string | null, delay?: string | null, iteration?: string | null) {
//         if (hidden) {
//             this.cacheAnimationName(box);
//         }
//         box.style.visibility = hidden ? "hidden" : "visible";

//         if (duration) {
//             this.vendorSet(box.style, { animationDuration: duration });
//         }
//         if (delay) {
//             this.vendorSet(box.style, { animationDelay: delay });
//         }
//         if (iteration) {
//             this.vendorSet(box.style, { animationIterationCount: iteration });
//         }
//         this.vendorSet(box.style, {
//             animationName: hidden ? "none" : this.cachedAnimationName(box),
//         });

//         return box;
//     }

//     vendors = ["moz", "webkit"];
//     vendorSet(elem: CSSStyleDeclaration, properties: { [key: string]: string | null }) {
//         for (const name in properties) {
//             if (properties.hasOwnProperty(name)) {
//                 const value = properties[name];
//                 elem[name] = value;
//                 for (let i = 0; i < this.vendors.length; i++) {
//                     const vendor = this.vendors[i];
//                     elem[`${vendor}${name.charAt(0).toUpperCase()}${name.substr(1)}`] = value;
//                 }
//             }
//         }
//     }
//     vendorCSS(elem: HTMLElement, property: string) {
//         const style = getComputedStyle(elem);
//         let result = style.getPropertyValue(property);
//         for (let i = 0; i < this.vendors.length; i++) {
//             const vendor = this.vendors[i];
//             result = result || style.getPropertyValue(`-${vendor}-${property}`);
//         }
//         return result;
//     }

//     animationName(box: HTMLElement) {
//         let aName;
//         try {
//             aName = this.vendorCSS(box, "animation-name").cssText;
//         } catch (error) {
//             // Opera, fall back to plain property value
//             aName = getComputedStyle(box).getPropertyValue("animation-name");
//         }

//         if (aName === "none") {
//             return ""; // SVG/Firefox, unable to get animation name?
//         }

//         return aName;
//     }

//     cacheAnimationName(box: HTMLElement) {
//         // https://bugzilla.mozilla.org/show_bug.cgi?id=921834
//         // box.dataset is not supported for SVG elements in Firefox
//         return this.animationNameCache.set(box, this.animationName(box));
//     }
//     cachedAnimationName(box: HTMLElement) {
//         return this.animationNameCache.get(box);
//     }

//     // fast window.scroll callback
//     scrollHandler() {
//         this.scrolled = true;
//     }

//     scrollCallback() {
//         if (this.scrolled) {
//             this.scrolled = false;
//             const results: HTMLElement[] = [];
//             for (let i = 0; i < this.boxes.length; i++) {
//                 const box = this.boxes[i];
//                 if (box) {
//                     if (this.isVisible(box)) {
//                         this.show(box);
//                         continue;
//                     }
//                     results.push(box);
//                 }
//             }
//             this.boxes = results;
//             if (!this.boxes.length && !this.config.live) {
//                 this.stop();
//             }
//         }
//     }

//     // Calculate element offset top
//     offsetTop(element: HTMLElement) {
//         // SVG elements don't have an offsetTop in Firefox.
//         // This will use their nearest parent that has an offsetTop.
//         // Also, using ('offsetTop' of element) causes an exception in Firefox.
//         while (element.offsetTop === undefined) {
//             element = element.parentNode as HTMLElement;
//         }
//         let top = element.offsetTop;
//         while (element.offsetParent) {
//             element = element.offsetParent as HTMLElement;
//             top += element.offsetTop;
//         }
//         return top;
//     }

//     // check if box is visible
//     isVisible(box: HTMLElement) {
//         const offset = parseInt(box.getAttribute("data-wow-offset") || `${this.config.offset}`, 10);
//         const viewTop =
//             (this.config.scrollContainer && this.config.scrollContainer.scrollTop) ||
//             window.pageYOffset;
//         const viewBottom =
//             viewTop + Math.min(this.element.clientHeight, getInnerHeight()) - offset;
//         const top = this.offsetTop(box);
//         const bottom = top + box.clientHeight;

//         return top <= viewBottom && bottom >= viewTop;
//     }

//     disabled() {
//         return !this.config.mobile && isMobile(navigator.userAgent);
//     }
// }