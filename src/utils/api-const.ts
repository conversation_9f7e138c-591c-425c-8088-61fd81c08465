export const API_ENDPOINTS = {
    // Auth
    login: (method: string) => `/auth/${method}`,

    // User
    addAddress: '/auth/add/address',
    getAddress: (page: number, size: number) => `/auth/list/address?page=${page}&per_page=${size}`,

    // Produts
    categories: "/products/list/categories?page=1&per_page=100",
    sliders: "/products/list/sliders?page=1&per_page=5",
    productsList: (params?: string) => `/products/list/product-variants?${params}`,
    filters: (categoryId: string) => `/products/get/filters?category_id=${categoryId}`,
    uniqueProductsList: (page: number, size: number) => `/products/list/uniq-product-variants?page=${page}&per_page=${size}`,
    countdownBannerList: (page: number, size: number) => `/products/list/countdown-banners?page=${page}&per_page=${size}`,
    searchProducts: (query: string, page: number, size: number) => `/products/search?search_query=${query}&page=${page}&per_page=${size}`,
    productDetails: (slug: string) => `/products/get/product-variant-slug/${slug}`,

    //Checkout
    getCartDetails: (coupon?: string) => `/orders/view/cart?coupon=${coupon ?? ""}&user_id=1`,
    addToCart: "/orders/add/cart",
}