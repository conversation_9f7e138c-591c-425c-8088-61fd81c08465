import { CartDetails, Item, OrderSummary } from "@/types/cart";
import { getLocalData, setLocalData } from "./file-utils";
import { ProductVariant } from "@/types/product/product";

export async function getCartItems(_?: string): Promise<CartDetails> {
    const items = await getCartItemsFromLocal();
    const orderSummary = getCartSummery(items);

    return {
        order_summary: orderSummary
    };
}

async function getCartItemsFromLocal(): Promise<Item[]> {
    const cartData = await getLocalData("cart");
    if (cartData) {
        try {
            const cartItems = JSON.parse(cartData);
            if (cartItems && Array.isArray(cartItems) && cartItems.length > 0) {
                return cartItems;
            }
        } catch (error) {
            return [];
        }
    }
    return [];
}

function getCartSummery(items: Item[]): OrderSummary {
    const orderSummary: OrderSummary = {
        coupon_discount: 0,
        discount: 0,
        items: items,
        shipping: 0,
        subtotal: 0,
        total: 0
    };

    orderSummary.subtotal = items.reduce((acc, item) => acc + ((item.mrp ?? item.selling_price) * item.quantity), 0);
    orderSummary.discount = items.reduce((acc, item) => acc + (((item.mrp ?? item.selling_price) - item.selling_price) * item.quantity), 0);

    orderSummary.total = orderSummary.subtotal - orderSummary.coupon_discount - orderSummary.discount + orderSummary.shipping;

    return orderSummary;
}

export async function addToCart(product: ProductVariant, qty: number): Promise<void> {
    let cartItems = await getCartItemsFromLocal();

    const existingItemIndex = cartItems.findIndex((item) => item.id === product.id);
    const newItem: Item = {
        id: product.id,
        mrp: product.mrp ?? 0,
        name: product.name,
        options: product.options,
        product_total: product.selling_price * qty,
        quantity: qty,
        selling_price: product.selling_price,
        slug: product.slug,
        images: product.images
    };

    if (existingItemIndex !== -1) {
        if (qty === 0) {
            // Remove item from cart if quantity is 0
            cartItems.splice(existingItemIndex, 1);
        } else {
            cartItems[existingItemIndex] = newItem;
        }
    } else {
        cartItems.push(newItem);
    }

    // Save updated cart items to local storage
    await setLocalData("cart", JSON.stringify(cartItems));

    // add delay to simulate network request
    await new Promise((resolve) => setTimeout(resolve, 200));
}

export async function clearCart(): Promise<void> {
    await setLocalData("cart", JSON.stringify([]));
}

export async function updateWishlist(product: ProductVariant, isAdded: boolean): Promise<void> {
    let wishlistItems = await getWishlistItemsFromLocal();

    const existingItemIndex = wishlistItems.findIndex((item) => item.id === product.id);

    if (isAdded) {
        if (existingItemIndex === -1) {
            wishlistItems.push(product);
        }
    } else {
        if (existingItemIndex !== -1) {
            wishlistItems.splice(existingItemIndex, 1);
        }
    }

    // Save updated wishlist items to local storage
    await setLocalData("wishlist", JSON.stringify(wishlistItems));

    // add delay to simulate network request
    await new Promise((resolve) => setTimeout(resolve, 200));
}

export async function getWishlistItems(): Promise<ProductVariant[]> {
    const wishlistItems = await getWishlistItemsFromLocal();
    return wishlistItems;
}

async function getWishlistItemsFromLocal(): Promise<ProductVariant[]> {
    const wishlistData = await getLocalData("wishlist");
    if (wishlistData) {
        try {
            const wishlistItems = JSON.parse(wishlistData);
            if (wishlistItems && Array.isArray(wishlistItems) && wishlistItems.length > 0) {
                return wishlistItems;
            }
        } catch (error) {
            return [];
        }
    }
    return [];
}