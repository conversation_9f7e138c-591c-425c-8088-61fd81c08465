export const socialLinks = [
    { href: import.meta.env.VITE_FOOTER_SOCIAL_LINKS_FACEBOOK, className: "social-facebook", iconClass: "icon-fb" },
    { href: import.meta.env.VITE_FOOTER_SOCIAL_LINKS_TWITTER, className: "social-twiter", iconClass: "icon-x" },
    { href: import.meta.env.VITE_FOOTER_SOCIAL_LINKS_INSTAGRAM, className: "social-instagram", iconClass: "icon-instagram" },
    { href: import.meta.env.VITE_FOOTER_SOCIAL_LINKS_TIKTOK, className: "social-tiktok", iconClass: "icon-tiktok" },
    { href: import.meta.env.VITE_FOOTER_SOCIAL_LINKS_AMAZON, className: "social-amazon", iconClass: "icon-amazon" },
    { href: import.meta.env.VITE_FOOTER_SOCIAL_LINKS_PINTEREST, className: "social-pinterest", iconClass: "icon-pinterest" },
];

export const links = {
    aboutUs: "/about-us",
    contactUs: "/contact-us",
    stories: "#",
    sizeGuide: "#",
    myAccount: "/my-account",
    shipping: "/shipping",
    returnRefund: "/return-refund",
    privacyPolicy: "/privacy-policy",
    termsConditions: "/term-of-use",
    ordersFAQs: "/FAQs",
    myWishlist: "/wish-list",
}

export const footerLinks = [
    {
        heading: "Information",
        items: [
            { label: "About Us", href: links.aboutUs, isLink: true },
            { label: "Contact us", href: links.contactUs, isLink: true },
            { label: "Return & Refund", href: links.returnRefund, isLink: true },
            { label: "Privacy Policy", href: links.privacyPolicy, isLink: true },
            { label: "Terms & Conditions", href: links.termsConditions, isLink: true },
        ],
    },
    {
        heading: "Account",
        items: [
            { label: "My Account", href: links.myAccount, isLink: true },
            { label: "My Wishlist", href: links.myWishlist, isLink: true },
            { label: "Shipping Info", href: links.shipping, isLink: true },
            { label: "Orders FAQs", href: links.ordersFAQs, isLink: true },
        ],
    }
];
