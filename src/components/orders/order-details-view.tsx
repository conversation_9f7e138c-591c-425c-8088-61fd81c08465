import { useGetOrderDetails } from "@/hooks/checkout-quires";
import { Loader } from "../common/query-wrapper";
import { Link } from "react-router-dom";
import { Timestamp } from "firebase/firestore";

export default function OrderDetailsView() {
    const orderId = window.location.pathname.split("/").pop();
    const { isLoading, data: order, error } = useGetOrderDetails(orderId ?? "");

    if (isLoading) return <Loader />
    if (error) {
        return (
            <div className="tf-alert-success">
                <div className="alert-content text-center p-4">
                    <h4 className="alert-title mb-3">Failed to load order!</h4>
                    <p className="alert-message mb-4">
                        {error.message}
                    </p>
                    <div className="text-center">
                        <button
                            className="tf-button d-block mx-auto"
                            onClick={() => window.location.href = "/products"}
                        >
                            Keep Shopping
                        </button>
                    </div>
                </div>
            </div>
        );
    }
    if (!order) return <div className="text-center py-5">Order not found</div>

    const isOngoing = ['Processing', 'Shipped'].includes(order.status);

    return (
        <div className="order-details">
            <div className="order-meta-info">
                <div className="d-flex justify-content-between align-items-center flex-wrap">
                    <div className="order-details-meta">
                        <span>Order placed {formatDate(order.created_at)}</span>
                        <span className="meta-divider"></span>
                        <span>Order #{order.order_number}</span>
                    </div>
                    {order.status === 'Completed' && order.invoice_url && (
                        <a href={order.invoice_url} className="btn btn-outline-primary btn-sm" target="_blank">
                            Download Invoice
                        </a>
                    )}
                </div>
                {isOngoing && (
                    <div className="delivery-status">
                        <div className="d-flex align-items-center gap-3 mb-2">
                            <div className={`order-status ${order.status.toLowerCase()}`}>
                                {order.status}
                            </div>
                            {order.expected_delivery_date && (
                                <div className="text-muted">
                                    Expected delivery by {formatDate(order.expected_delivery_date, true)}
                                </div>
                            )}
                        </div>
                        {order.tracking_details && (
                            <div className="order-timeline">
                                {order.tracking_details.tracking_history.map((event, index) => (
                                    <div key={index} className="timeline-item">
                                        <div className="timeline-dot"></div>
                                        <div>
                                            <div className="fw-bold">{event.status}</div>
                                            <div className="text-muted">{formatDate(event.timestamp)}</div>
                                            {event.description && <div>{event.description}</div>}
                                            {event.location && <div className="text-muted">{event.location}</div>}
                                        </div>
                                    </div>
                                ))}
                            </div>
                        )}
                    </div>
                )}
            </div>

            <div className="info-grid">
                <div className="info-section">
                    <h6>Shipping Address</h6>
                    <p className="mb-1">{order.shipping_address.name}</p>
                    <p className="mb-1">{order.shipping_address.address_line_1}</p>
                    {order.shipping_address.address_line_2 && (
                        <p className="mb-1">{order.shipping_address.address_line_2}</p>
                    )}
                    <p className="mb-1">
                        {order.shipping_address.city}, {order.shipping_address.state} {order.shipping_address.postal_code}
                    </p>
                    <p className="mb-0">Phone: {order.shipping_address.contact_number}</p>
                </div>

                <div className="info-section">
                    <h6>Payment Information</h6>
                    <p className="mb-1">Method: {order.payment_details.payment_method}</p>
                    <p className="mb-1">Status: {order.payment_details.payment_status}</p>
                    {order.payment_details.transaction_id && (
                        <p className="mb-1">Transaction ID: {order.payment_details.transaction_id}</p>
                    )}
                    {order.refund_status && (
                        <p className="refund-status">Refund {order.refund_status}</p>
                    )}
                </div>

                <div className="info-section">
                    <h6>Order Summary</h6>
                    <div className="d-flex justify-content-between mb-2">
                        <span>Subtotal</span>
                        <span>₹{order.order_summary.subtotal}</span>
                    </div>
                    <div className="d-flex justify-content-between mb-2">
                        <span>Shipping</span>
                        <span>₹{order.order_summary.shipping_fee}</span>
                    </div>
                    <div className="d-flex justify-content-between mb-2">
                        <span>Tax</span>
                        <span>₹{order.order_summary.tax}</span>
                    </div>
                    {order.order_summary.discount > 0 && (
                        <div className="d-flex justify-content-between mb-2 text-success">
                            <span>Discount</span>
                            <span>-₹{order.order_summary.discount}</span>
                        </div>
                    )}
                    {order.order_summary.coupon_discount > 0 && (
                        <div className="d-flex justify-content-between mb-2 text-success">
                            <span>Coupon Discount</span>
                            <span>-₹{order.order_summary.coupon_discount}</span>
                        </div>
                    )}
                    <hr />
                    <div className="d-flex justify-content-between fw-bold">
                        <span>Total</span>
                        <span>₹{order.order_summary.total}</span>
                    </div>
                </div>
            </div>

            <div className="order-items">
                {order.items.map((item) => (
                    <div key={item.id} className="order-product">
                        <div className="product-info">
                            <img src={item.image.image_url} alt={item.name} width={90} height={120} />
                            <div className="product-details">
                                <Link to={`/product-details/${item.slug}`} className="product-name">{item.name}</Link>
                                <span className="price">₹{item.selling_price.toFixed(2)}</span>
                            </div>
                        </div>
                        <div className="product-actions">
                            {order.status === 'Completed' && (
                                <button className="tf-button write-review">Write a review</button>
                            )}
                            <Link to={`/product-details/${item.slug}`} className="tf-button-alt">Buy it again</Link>
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
}

const formatDate = (dateString: string | Timestamp, dateOnly = false) => {
    if (dateString instanceof Timestamp) {
        dateString = dateString.toDate().toISOString();
    }
    return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        ...(dateOnly ? {} : {
            hour: '2-digit',
            minute: '2-digit'
        })
    });
};