import { Link } from "react-router-dom";
import { useGetInfiniteOrders } from "@/hooks/checkout-quires";
import { Loader } from "../common/query-wrapper";
import React from "react";
import { Timestamp } from "firebase/firestore";

export default function OrderListView() {
    const {
        data,
        error,
        fetchNextPage,
        hasNextPage,
        isFetching,
        isFetchingNextPage,
    } = useGetInfiniteOrders()

    if (isFetching && !isFetchingNextPage) return (<Loader />)

    if (error || !data) {
        return (
            <div className="tf-alert-success">
                <div className="alert-content text-center p-4">
                    <h4 className="alert-title mb-3">Failed to load orders!</h4>
                    <p className="alert-message mb-4">
                        {error?.message ?? "An error occurred while fetching orders."}
                    </p>
                    <div className="text-center">
                        <button
                            className="tf-button d-block mx-auto"
                            onClick={() => window.location.href = "/products"}
                        >
                            Keep Shopping
                        </button>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <section className="tf-section order-list mt-4">
            <div className="container">
                <div className="orders-container">
                    {data.pages?.map((pages, i) => (
                        <React.Fragment key={i}>
                            {pages.map((order, index) => (
                                <div key={index} className="order-card">
                                    <a href={`/order/${order.id}`} className="order-meta">
                                        <div className="order-info">
                                            <div className="order-date">
                                                <span>ORDER PLACED</span>
                                                <strong>{formatDate(order.created_at)}</strong>
                                            </div>
                                            <div className="order-total">
                                                <span>TOTAL</span>
                                                <strong>₹{order.order_summary.total.toFixed(2)}</strong>
                                            </div>
                                            <div className="ship-to">
                                                <span>SHIP TO</span>
                                                <strong>{order.shipping_address.name}</strong>
                                            </div>
                                        </div>
                                        <div className="order-number">
                                            <span>ORDER #{order.order_number}</span>
                                            <span className="view-details">View order details</span>
                                        </div>
                                        <div className="mobile-arrow">
                                            <i className="fas fa-chevron-right"></i>
                                        </div>
                                    </a>

                                    {order.items.map((item) => (
                                        <div key={item.id} className="order-product">
                                            <div className="product-info">
                                                <img src={item.image.image_url} alt={item.name} width={90} height={120} />
                                                <div className="product-details">
                                                    <Link to={`/product-details/${item.slug}`} className="product-name">{item.name}</Link>
                                                    <span className="price">₹{item.selling_price.toFixed(2)}</span>
                                                </div>
                                            </div>
                                            <div className="product-actions">
                                                {order.status === 'Completed' && (
                                                    <button className="tf-button write-review">Write a review</button>
                                                )}
                                                <Link to={`/product-details/${item.slug}`} className="tf-button-alt">Buy it again</Link>
                                            </div>
                                        </div>
                                    ))}

                                    <div className="order-delivery">
                                        <span className={`status-badge ${order.status.toLowerCase()}`}>
                                            {order.status}
                                        </span>
                                        {order.status === 'Completed' && (
                                            <span className="delivery-date">Delivered on 15 Jan 2024</span>
                                        )}
                                    </div>
                                </div>
                            ))}
                        </React.Fragment>
                    ))}
                    {!(data?.pages.some((p) => p.length > 0)) && (
                        <div className="empty-state text-center py-5">
                            <h4>No Orders Found</h4>
                            <p>You haven't placed any orders yet.</p>
                            <div className="text-center">
                                <button
                                    className="tf-button d-block mx-auto"
                                    onClick={() => window.location.href = "/products"}
                                >
                                    Keep Shopping
                                </button>
                            </div>
                        </div>
                    )}
                </div>
            </div>
            {/* <ul className="wg-pagination justify-content-center mt-2 mb-4">
                <Pagination totalPages={1} />
            </ul> */}
            {hasNextPage && <div
                className="wd-load d-flex justify-content-center mb-4"
                onClick={() => fetchNextPage()}

            >
                <button
                    className={`load-more-btn btn-out-line tf-loading ${isFetchingNextPage ? "loading" : ""
                        } `}
                >
                    <span className="text-btn">Load more</span>
                </button>
            </div>}
        </section>
    );
}

const formatDate = (dateString: string | Timestamp) => {
    if (dateString instanceof Timestamp) {
        dateString = dateString.toDate().toISOString();
    }
    return new Date(dateString).toLocaleDateString('en-US', {
        day: 'numeric',
        month: 'short',
        year: 'numeric'
    });
};
