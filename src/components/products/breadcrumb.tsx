import { useContextElement } from "@/layout/context"
import { Category, Collection } from "@/types/product/collection"
import React from "react"

interface BreadcrumbType {
    to: string,
    title: string,
    isActive: boolean,
}

export default function Breadcrumb() {
    const navMenu = useContextElement().navData

    if (!navMenu) return (<></>)

    const categories = navMenu
    const breadcrumbs = getBreadcrumbs(categories)
    const category = getData(categories)

    return (
        <div className="breadcrumbs-default">
            <div className="container">
                <div className="breadcrumbs-content">
                    <ul className="breadcrumbs d-flex align-items-center">
                        {breadcrumbs.map((b, index) => (
                            <React.Fragment key={index}>
                                <li>
                                    {b.isActive ? (
                                        <a className="link" href={b.to}>
                                            {b.title}
                                        </a>
                                    ) : (
                                        b.title
                                    )}
                                </li>
                                {(index !== breadcrumbs.length - 1) &&
                                    <li>
                                        <i className="icon-arrRight" />
                                    </li>
                                }
                            </React.Fragment>
                        ))}

                    </ul>
                    {category && <div className="content-bottom">
                        <h3>{category.name}</h3>
                        {category.desc && <p className="text-secondary">
                            {category.desc}
                        </p>}
                    </div>}
                </div>
            </div>
        </div>
    )
}

function getBreadcrumbs(category: Category[]) {
    const path = window.location.pathname
    const paths = path.split("/")
    const breadcrumbs: BreadcrumbType[] = []

    if (paths.some((p) => p == "sub")) {
        const c = category.find((c) => {
            const subCategory = c.sub_categories?.find((s) => s.slug == paths[paths.length - 1])
            if (subCategory) breadcrumbs.push({
                to: `/sub/${subCategory.slug}`,
                title: subCategory.name,
                isActive: false,
            })
            return subCategory
        })
        if (c) {
            breadcrumbs.push({
                to: `/${c.slug}`,
                title: c.name,
                isActive: true,
            })
        }
    } else {
        const c = category.find((c) => c.slug == paths[paths.length - 1])
        if (c) breadcrumbs.push({
            to: `/${c.slug}`,
            title: c.name,
            isActive: false,
        })
    }
    breadcrumbs.push({
        to: `/`,
        title: "Home",
        isActive: true,
    })
    return breadcrumbs.reverse()
}

function getData(category: Category[]): Collection | undefined {
    const path = window.location.pathname
    const paths = path.split("/")
    if (paths.some((p) => p == "sub")) {
        for (var c of category) {
            const sub = c.sub_categories?.find((s) => s.slug == paths[paths.length - 1])
            if (sub) return sub
        }
    } else {
        return category.find((c) => c.slug == paths[paths.length - 1])
    }
    return undefined
}