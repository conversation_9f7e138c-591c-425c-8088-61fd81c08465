import { Collection } from "@/types/product/collection";

export default function AllCategories({ categories }: { categories: (Collection | undefined)[] }) {
    return (<section className="flat-spacing">
        <div className="container">
            <div className="tf-grid-layout tf-col-2 lg-col-4">
                {categories && categories.map((item, index) => (
                    <div
                        key={index}
                        className="collection-position-2 radius-lg style-3 hover-img"
                    >
                        <a className="img-style">
                            {item && < img
                                className="lazyload"
                                data-src={item.image_url}
                                alt={`banner-${item.name.toLowerCase()}`}
                                src={item.image_url}
                                width={450}
                                height={600}
                            />}
                        </a>
                        <div className="content">
                            <a href={`/sub/${item?.slug}`} className="cls-btn">
                                {item && <h6 className="text">{item.name}</h6>}
                                <i className="icon icon-arrowUpRight" />
                            </a>
                        </div>
                    </div>
                ))}
            </div>
        </div>
    </section>)
}