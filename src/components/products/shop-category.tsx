import { Navigation, Pagination } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";
import { Image } from "../common/image";
import { useContextElement } from "@/layout/context";

export default function ShopCategories() {

    const navMenu = useContextElement().navData
    if (!navMenu) return (<></>)

    const path = window.location.pathname
    const paths = path.split("/")

    const subCategories = navMenu.find((c) => c.slug == paths[paths.length - 1])?.sub_categories

    if (!subCategories) return (<></>)

    return (
        <section className="flat-spacing pt-0">
            <div className="container">
                <Swiper
                    dir="ltr"
                    slidesPerView={5}
                    spaceBetween={20}
                    breakpoints={{
                        1200: { slidesPerView: 6, spaceBetween: 20 },
                        1000: { slidesPerView: 4, spaceBetween: 20 },
                        768: { slidesPerView: 3, spaceBetween: 20 },
                        480: { slidesPerView: 2, spaceBetween: 15 },
                        0: { slidesPerView: 2, spaceBetween: 15 },
                    }}
                    modules={[Pagination, Navigation]}
                    pagination={{
                        clickable: true,
                        el: ".spd54",
                    }}
                    navigation={{
                        prevEl: ".snbp12",
                        nextEl: ".snbn12",
                    }}
                >
                    {subCategories.map((collection, index) => (
                        <SwiperSlide key={index} >
                            <div className="collection-circle hover-img">
                                <a href={`/sub/${collection.slug}`} className="img-style" style={{ aspectRatio: 1 }}>
                                    <Image
                                        className="lazyload"
                                        data-src={collection.image_url}
                                        alt={collection.name}
                                        src={collection.image_url}
                                        width={363}
                                        height={363}
                                    />
                                </a>
                                <div className="collection-content text-center">
                                    <div>
                                        <a href={`/sub/${collection.slug}`} className="cls-title">
                                            <h6 className="text">{collection.name}</h6>
                                            <i className="icon icon-arrowUpRight" />
                                        </a>
                                    </div>
                                    {/* <div className="count text-secondary">{collection.count}</div> */}
                                </div>
                            </div>
                        </SwiperSlide>
                    ))}
                </Swiper>
                <div className="d-flex d-lg-none sw-pagination-collection sw-dots type-circle justify-content-center spd54" />
            </div>
        </section>
    );
}
