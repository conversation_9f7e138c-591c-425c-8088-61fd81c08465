import { useEffect, useState } from "react";
import Sorting from "./sorting";
import FilterMeta from "./filter-meta";
import GridView from "./grid-view";
import FilterModal from "./filter-model";
import { useSearchParams } from "react-router-dom";
import { useFilter, useProducts } from "@/hooks/product-quries";
import { Product1Loader } from "./product1";
import { Collection } from "@/types/product/collection";


export default function Product2({ parentClass = "flat-spacing", subCategories, categoryId }: { parentClass: string, subCategories?: Collection[], categoryId?: string }) {
    const [activeLayout, setActiveLayout] = useState(4);

    const [searchParams, setSearchParams] = useSearchParams();
    const currentPage = parseInt(searchParams.get("page") || "1", 10);

    let subCategoryId: string | undefined = undefined
    const path = window.location.pathname
    const paths = path.split("/")
    if (paths.some((p) => p == "sub")) {
        const slug = paths[paths.length - 1].split("-")
        subCategoryId = slug[slug.length - 1]
    }

    const initialFilters = {
        brands: searchParams.get("brands")?.split(",") || [],
        availability: searchParams.get("availability")?.split(",") || [],
        min_price: searchParams.get("min-price") || "",
        max_price: searchParams.get("max-price") || "",
        options: Object.fromEntries(
            [...searchParams.entries()].filter(([key]) => !["brands", "min-price", "max-price", "sort"].includes(key))
        ),
        sort: searchParams.get("sort") || ""
    }

    const [filters, setFilters] = useState(initialFilters);

    const allProps = {
        ...filters,
        setOption: (optionKey: string, value: any) => {
            setFilters((prev) => {
                const updatedOptions = { ...prev.options };

                if (!value || (Array.isArray(value) && value.length === 0)) {
                    delete updatedOptions[optionKey];
                } else {
                    updatedOptions[optionKey] = value;
                }

                return {
                    ...prev,
                    options: updatedOptions,
                };
            });
        },
        removeOption: (key: string) => {
            setFilters((prev) => {
                const updatedOptions = { ...prev.options };
                delete updatedOptions[key]
                return {
                    ...prev,
                    options: updatedOptions,
                };
            });
        },
        setPriceRange: (value: [number, number]) => {
            setFilters((prev) => {
                const updatedOptions = { ...prev.options };
                delete updatedOptions["min-price"]
                delete updatedOptions["max-price"]
                if (value[0] > 0) {
                    updatedOptions["min-price"] = value[0].toString()
                }
                if (value[1] > 0) {
                    updatedOptions["max-price"] = value[1].toString()
                }
                return {
                    ...prev,
                    options: updatedOptions
                }
            })
        },

        setBrands: (brand: string) => {
            setFilters((prev) => {
                const updatedBrands = prev.brands.includes(brand)
                    ? prev.brands.filter((b) => b !== brand)
                    : [...prev.brands, brand];

                return {
                    ...prev,
                    brands: updatedBrands,
                };
            });
        },
        setAvailability: (value: string) => {
            setFilters((prev) => {
                const updatedAvailability = prev.availability.includes(value)
                    ? prev.availability.filter((b) => b !== value)
                    : [...prev.availability, value];

                return {
                    ...prev,
                    availability: updatedAvailability,
                };
            });
        },
        setSortingOption: (value: any) => {
            setFilters((prev) => {
                return {
                    ...prev,
                    sort: value,
                }
            });
        },
        clearFilter: () => {
            setFilters({
                brands: [],
                max_price: "",
                min_price: "",
                options: {},
                availability: [],
                sort: "",
            });
        },
    }
    const { isLoading, data, isFetching } = useProducts(currentPage, categoryId, subCategoryId, filters, filters.sort)
    const { data: filter } = useFilter(categoryId ?? "")


    useEffect(() => {
        const handleResize = () => {
            if (window.innerWidth > 1200) {
                setActiveLayout(4);
            } else if (window.innerWidth < 1200 && window.innerWidth > 767) {
                setActiveLayout(3);
            } else if (window.innerWidth < 768) {
                setActiveLayout(2);
            }
        };
        handleResize();
        // Add the resize event listener
        window.addEventListener("resize", handleResize);

        // Clean up the event listener on unmount
        return () => {
            window.removeEventListener("resize", handleResize);
        };
    }, []);

    useEffect(() => {
        const params = new URLSearchParams();

        if (filters.brands.length > 0) {
            params.set("brands", filters.brands.join(","));
        }
        if (filters.availability.length > 0) {
            params.set("availability", filters.availability.join(","));
        }
        if (filters.max_price) {
            params.set("max-price", filters.max_price);
        }
        if (filters.min_price) {
            params.set("min-price", filters.min_price);
        }

        Object.entries(filters.options).forEach(([key, value]) => {
            if (value) params.set(key, value.toString());
        });

        setSearchParams(params);
    }, [filters, setSearchParams]);

    useEffect(() => {
        setSearchParams((prevParams) => {
            const newParams = new URLSearchParams(prevParams);
            if (filters.sort) {
                newParams.set("sort", filters.sort);
            } else {
                newParams.delete("sort");
            }
            return newParams;
        });
    }, [filters.sort, setSearchParams]);

    return (
        <>
            <section className={parentClass}>
                <div className="container mt-4">
                    <div className="tf-shop-control">
                        <div className="tf-control-filter">
                            {filter && <a
                                href="#filterShop"
                                data-bs-toggle="offcanvas"
                                aria-controls="filterShop"
                                className="tf-btn-filter"
                            >
                                <span className="icon icon-filter" />
                                <span className="text">Filters</span>
                            </a>}
                        </div>
                        <ul className="tf-control-layout">
                            {/* <LayoutHandler
                    setActiveLayout={setActiveLayout}
                    activeLayout={activeLayout}
                  /> */}
                        </ul>
                        <div className="tf-control-sorting">
                            <p className="d-none d-lg-block text-caption-1">Sort by:</p>
                            <Sorting allProps={allProps} />
                        </div>
                    </div>
                    <div className="wrapper-control-shop">
                        <FilterMeta isLoading={isLoading || isFetching} productLength={data?.total ?? 0} allProps={allProps} />

                        {/* {activeLayout == 1 ? (
                  <div className="tf-list-layout wrapper-shop" id="listLayout">
                    <Listview products={sorted} />
                  </div>
                ) : ( */}
                        <div
                            className={`tf-grid-layout wrapper-shop tf-col-${activeLayout}`}
                            id="gridLayout"
                        >
                            {isLoading || isFetching ? <Product1Loader /> : <GridView pagination data={data} />}
                        </div>
                        {/* )} */}
                    </div>
                </div>
            </section>

            {filter && <FilterModal filter={filter} allProps={allProps} subCategories={subCategories} />}
        </>
    )
}