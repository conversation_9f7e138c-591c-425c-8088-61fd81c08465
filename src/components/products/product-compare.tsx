import { useProductDetailsList } from "@/hooks/product-quries";
import { useContextElement } from "@/layout/context";
import { Loader } from "../common/query-wrapper";
import { compareSpec } from "@/utils/product-utils";

export default function ProductCompare() {
    const { compareItem, addToCart, isAddedToCartProducts } = useContextElement();

    const data = useProductDetailsList(compareItem.map((item) => item.slug));

    if (data.some((item) => item.isLoading)) {
        return (<Loader />);
    }

    const items = data.filter((item) => item.isSuccess).map((item) => item.data);

    return (
        <section className="flat-spacing">
            <div className="container">
                {!items.length ? (
                    <div>
                        No items to compare yet. Add products to your comparison list and
                        decide smarter!{" "}
                        <a className="btn-line" href="/products">
                            Explore Products
                        </a>
                    </div>
                ) : (
                    ""
                )}
                {items.length ? (
                    <div className="tf-compare-table">
                        <div className="tf-compare-row tf-compare-grid">
                            <div className="tf-compare-col d-md-block d-none" />
                            {items.map((elm, i) => (
                                <div key={i} className="tf-compare-col">
                                    <div className="tf-compare-item">
                                        <a
                                            className="tf-compare-image"
                                            href={`/product-details/${elm?.variant_details.slug}`}
                                        >
                                            <img
                                                className="lazyload"
                                                alt="img-compare"
                                                src={elm?.variant_details.images[0].image_url}
                                                width={elm?.variant_details.images[0].width}
                                                height={elm?.variant_details.images[0].height}
                                            />
                                        </a>
                                        <div className="tf-compare-content">
                                            <a
                                                className="link text-title text-line-clamp-1"
                                                href={`/product-details/${elm?.variant_details.slug}`}
                                            >
                                                {elm?.variant_details.name}
                                            </a>
                                            <p className="desc text-caption-1">
                                                {elm?.product.short_description}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>

                        <div className="tf-compare-row">
                            <div className="tf-compare-col tf-compare-field d-md-block d-none">
                                <h6>Rating</h6>
                            </div>
                            {items.map((elm, i) => (
                                <div
                                    key={i}
                                    className="tf-compare-col tf-compare-field tf-compare-rate"
                                >
                                    <div className="list-star">
                                        {Array.from({ length: elm?.product.rating ?? 0 }, (_, index) => (
                                            <span key={index} className="icon icon-star" />
                                        ))}
                                        {Array.from({ length: 5 - (elm?.product.rating ?? 0) }, (_, index) => (
                                            <span key={index} className="icon icon-star disabled" />
                                        ))}
                                    </div>
                                    <span>({elm?.product.number_of_rating})</span>
                                </div>
                            ))}
                        </div>
                        <div className="tf-compare-row">
                            <div className="tf-compare-col tf-compare-field d-md-block d-none">
                                <h6>Price</h6>
                            </div>

                            {compareItem.map((elm, i) => (
                                <div
                                    key={i}
                                    className="tf-compare-col tf-compare-field text-center"
                                >
                                    <span className="price">₹{elm.selling_price.toFixed(2)}</span>
                                </div>
                            ))}
                        </div>

                        {compareSpec(items).map((spec, i) => (
                            <div className="tf-compare-row" key={i}>
                                <div className="tf-compare-col tf-compare-field d-md-block d-none">
                                    <h6>{spec.key}</h6>
                                </div>
                                {spec.value.map((elm, i) => (
                                    <div
                                        key={i}
                                        className="tf-compare-col tf-compare-field text-center"
                                    >
                                        <span className="type">{elm}</span>
                                    </div>
                                ))}
                            </div>
                        ))}
                        {/* <div className="tf-compare-row">
                            <div className="tf-compare-col tf-compare-field d-md-block d-none">
                                <h6>Brand</h6>
                            </div>
                            {compareItem.map((elm, i) => (
                                <div
                                    key={i}
                                    className="tf-compare-col tf-compare-field text-center"
                                >
                                    <span className="brand">Gucci</span>
                                </div>
                            ))}
                        </div>
                        <div className="tf-compare-row">
                            <div className="tf-compare-col tf-compare-field d-md-block d-none">
                                <h6>size</h6>
                            </div>
                            {compareItem.map((elm, i) => (
                                <div
                                    key={i}
                                    className="tf-compare-col tf-compare-field text-center"
                                >
                                    <span className="size">X, XS, L, M, XL</span>
                                </div>
                            ))}
                        </div>
                        <div className="tf-compare-row">
                            <div className="tf-compare-col tf-compare-field d-md-block d-none">
                                <h6>Color</h6>
                            </div>
                            {compareItem.map((elm, i) => (
                                <div
                                    key={i}
                                    className="tf-compare-col tf-compare-field text-center"
                                >
                                    <div className="list-compare-color justify-content-center">
                                        <span className="item bg-pink" />
                                        <span className="item bg-yellow" />
                                        <span className="item bg-primary active" />
                                        <span className="item bg-success" />
                                        <span className="item bg-warning" />
                                    </div>
                                </div>
                            ))}
                        </div>
                        <div className="tf-compare-row">
                            <div className="tf-compare-col tf-compare-field d-md-block d-none">
                                <h6>Metarial</h6>
                            </div>
                            {compareItem.map((elm, i) => (
                                <div
                                    key={i}
                                    className="tf-compare-col tf-compare-field text-center"
                                >
                                    <span className="size">Cotton</span>
                                </div>
                            ))}
                        </div> */}
                        <div className="tf-compare-row">
                            <div className="tf-compare-col tf-compare-field d-md-block d-none">
                                <h6>Add To Cart</h6>
                            </div>
                            {compareItem.map((elm, i) => (
                                <div
                                    key={i}
                                    className="tf-compare-col tf-compare-field tf-compare-viewcart text-center"
                                >
                                    <a
                                        className="btn-view-cart"
                                        onClick={() => addToCart(elm.id, 1)}
                                    >
                                        {isAddedToCartProducts(elm.id)
                                            ? "Already Added"
                                            : "Add to Cart"}
                                    </a>
                                </div>
                            ))}
                        </div>
                    </div>
                ) : (
                    ""
                )}
            </div>
        </section>
    );
}