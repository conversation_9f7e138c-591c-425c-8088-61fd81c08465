import { FilterModel } from "@/types/home";
import { Collection } from "@/types/product/collection";
import { useState } from "react";
// import { productMain } from "@/data/products";

import RangeSlider from "react-range-slider-input";
export default function FilterModal({ filter, allProps, subCategories }: { filter: FilterModel, allProps: any, subCategories?: Collection[] }) {
    const availabilityOptions = ["Include Out of stock"]
    const minPrice = allProps.min_price && !isNaN(Number(allProps.min_price))
        ? Number(allProps.min_price)
        : 0;

    const maxPrice = allProps.max_price && !isNaN(Number(allProps.max_price))
        ? Number(allProps.max_price)
        : filter.max_price;

    const [value, setValue] = useState<[number, number]>([minPrice, maxPrice])

    return (
        <div className="offcanvas offcanvas-start canvas-filter" id="filterShop">
            <div className="canvas-wrapper">
                <div className="canvas-header">
                    <h5>Filters</h5>
                    <span
                        className="icon-close icon-close-popup"
                        data-bs-dismiss="offcanvas"
                        aria-label="Close"
                    />
                </div>
                <div className="canvas-body">

                    {subCategories && <div className="widget-facet facet-categories">
                        <h6 className="facet-title">Product Categories</h6>
                        <ul className="facet-content">
                            {subCategories?.map((category, index) => (
                                <li key={index}>
                                    <a href={`/sub/${category.slug}`} className={`categories-item`}>
                                        {category.name}
                                    </a>
                                </li>
                            ))}
                        </ul>
                    </div>}

                    <div className="widget-facet facet-price">
                        <h6 className="facet-title">Price</h6>

                        <RangeSlider
                            min={0}
                            max={filter.max_price}
                            value={value}
                            onRangeDragEnd={() => allProps.setPriceRange(value)}
                            onThumbDragEnd={() => allProps.setPriceRange(value)}
                            onInput={(value) => setValue(value)}
                        />
                        <div className="box-price-product mt-3">
                            <div className="box-price-item">
                                <span className="title-price">Min price</span>
                                <div
                                    className="price-val"
                                    id="price-min-value"
                                    data-currency="$"
                                >
                                    {value[0]}
                                </div>
                            </div>
                            <div className="box-price-item">
                                <span className="title-price">Max price</span>
                                <div
                                    className="price-val"
                                    id="price-max-value"
                                    data-currency="$"
                                >
                                    {value[1]}
                                </div>
                            </div>
                        </div>
                    </div>

                    {Object.keys(filter.options).map((key, index) => (
                        <div key={index} className="widget-facet facet-color">
                            <h6 className="facet-title">{key}</h6>
                            <div className="facet-color-box">
                                {filter.options[key].map((option, index) => (
                                    <div
                                        onClick={() => allProps.setOption(key, option)}
                                        key={index}
                                        className={`color-item color-check ${option == allProps.options[key] ? "active" : ""
                                            }`}
                                    >

                                        {option}
                                    </div>
                                ))}
                            </div>
                        </div>
                    ))}

                    <div className="widget-facet facet-fieldset">
                        <h6 className="facet-title">Availability</h6>
                        <div className="box-fieldset-item">
                            {availabilityOptions.map((option, index) => (
                                <fieldset
                                    key={index}
                                    className="fieldset-item"
                                    onClick={() => allProps.setAvailability(option)}
                                >
                                    <input
                                        type="radio"
                                        name="availability"
                                        className="tf-check"
                                        readOnly
                                        checked={allProps.availability.includes(option)}
                                    />
                                    <label>
                                        {option}
                                    </label>
                                </fieldset>
                            ))}
                        </div>
                    </div>

                    <div className="widget-facet facet-fieldset">
                        <h6 className="facet-title">Brands</h6>
                        <div className="box-fieldset-item">
                            {filter.brands.map((brand, index) => (
                                <fieldset
                                    key={index}
                                    className="fieldset-item"
                                    onClick={() => allProps.setBrands(brand.name + "-" + brand.id)}
                                >
                                    <input
                                        type="checkbox"
                                        name="brand"
                                        className="tf-check"
                                        readOnly
                                        checked={allProps.brands.includes(brand.name + "-" + brand.id)}
                                    />
                                    <label>
                                        {brand.name}

                                    </label>
                                </fieldset>
                            ))}
                        </div>
                    </div>

                </div>
                <div className="canvas-bottom">
                    <button
                        id="reset-filter"
                        onClick={allProps.clearFilter}
                        className="tf-btn btn-reset"
                    >
                        Reset Filters
                    </button>
                </div>
            </div>
        </div>
    );
}
