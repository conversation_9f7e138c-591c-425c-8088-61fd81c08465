import React from "react"

interface BreadcrumbProps {
    title: string,
    pageName?: string,
    background?: string,
    pages?: {
        name: string,
        link: string
    }[]
}

export default function Breadcrumb({ title, pageName, pages, background }: BreadcrumbProps) {
    const bgUrl = background ?? import.meta.env.VITE_BANNER_DEFAULT

    // "varients": {
    //     "women": "/images/section/fashion_women.jpg",
    //     "men": "/images/section/fashion_men.jpg",
    //     "child": "/images/section/fashion_child.jpg",
    // }
    return (
        <div
            className="page-title"
            style={{ backgroundImage: `url(${bgUrl})` }}
        >
            <div className="container">
                <h3 className="heading text-center">{title}</h3>
                <ul className="breadcrumbs d-flex align-items-center justify-content-center">
                    <li>
                        <a className="link" href={`/`}>
                            Home
                        </a>
                    </li>
                    {pages && pages.map((page, index) => (
                        <React.Fragment key={index}>
                            <li key={index}>
                                <i className="icon-arrRight" />
                            </li>
                            <li>
                                <a className="link" href={page.link}>
                                    {page.name}
                                </a>
                            </li>
                        </React.Fragment>
                    ))}
                    <li>
                        <i className="icon-arrRight" />
                    </li>
                    <li>{pageName ?? title}</li>
                </ul>
            </div>
        </div>
    )
}