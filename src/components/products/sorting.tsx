const filterOptions = [
    "Newest",
    "Price Low-High",
    "Price High-Low",
];
export default function Sorting({ allProps }: { allProps: any }) {
    const sort = getSortValue(allProps.sort)

    return (
        <div className="tf-dropdown-sort" data-bs-toggle="dropdown">
            <div className="btn-select">
                <span className="text-sort-value">{filterOptions[sort]}</span>
                <span className="icon icon-arrow-down" />
            </div>
            <div className="dropdown-menu">
                {filterOptions.map((option, i) => (
                    <div
                        onClick={() => allProps.setSortingOption(setSortValue(i))}
                        key={i}
                        className={`select-item ${sort === i ? "active" : ""
                            }`}
                    >
                        <span className="text-value-item">{option}</span>
                    </div>
                ))}
            </div>
        </div>
    );
}

function getSortValue(sort: string): number {
    switch (sort) {
        case "price_asc":
            return 1
        case "price_desc":
            return 2
        default:
            return 0
    }
}

function setSortValue(index: number): string {
    switch (index) {
        case 0: return "newest"
        case 1: return "price_asc"
        case 2: return "price_desc"
        default: return ""
    }
}
