import React from "react";
import { ProductVariant } from "@/types/product/product";


const ProductCard1 = React.lazy(() => import('../product-cards/product-card1'))
const Pagination = React.lazy(() => import('../common/pagination'))

export default function GridView({ pagination = true, data }: { pagination: boolean, data: Pagination<ProductVariant> | undefined }) {

    return (
        <>
            {data?.data.map((product, index) => (
                <ProductCard1 key={index} variant={product} gridClass="grid" />
            ))}
            {pagination ? (
                <ul className="wg-pagination justify-content-center">
                    <Pagination totalPages={data?.pages} />
                </ul>
            ) : (
                ""
            )}
        </>
    );
}
