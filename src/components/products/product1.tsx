import ProductCard1 from "@/components/product-cards/product-card1";
import { ProductVariant } from "@/types/product/product";
import { useEffect, useState } from "react";
import { Shimmer } from "react-shimmer";
import { Placeholder } from "../common/image";

export default function Products1({ parentClass = "flat-spacing-3", products }: { parentClass: string, products: [ProductVariant[], ProductVariant[], ProductVariant[]] }) {
    const titles = ["New Arrivals", "Best Seller", "On Sale"]

    const data = products

    const [activeItem, setActiveItem] = useState(titles[0]);
    const [selectedItems, setSelectedItems] = useState<ProductVariant[]>([]);

    useEffect(() => {
        document.getElementById("newArrivals")?.classList.remove("filtered")
        setTimeout(() => {
            {
                data && setSelectedItems(
                    data[titles.indexOf(activeItem)]
                )
            }
            document.getElementById("newArrivals")?.classList.add("filtered")
        }, 300);
    }, [activeItem]);

    if (data.length < 1 || data[0].length < 1) return (<></>)

    return (
        <section className={parentClass}>
            <div className="container">
                <div className="flat-animate-tab">
                    <ul className="tab-product justify-content-sm-center" role="tablist">
                        {titles.map((item) => (
                            <li key={item} className="nav-tab-item">
                                <a
                                    // href={`#`} // Generate href dynamically
                                    className={activeItem === item ? "active" : ""}
                                    onClick={(e) => {
                                        e.preventDefault();
                                        setActiveItem(item);
                                    }}
                                >
                                    {item}
                                </a>
                            </li>
                        ))}
                    </ul>
                    <div className="tab-content">
                        <div
                            className="tab-pane active show tabFilter filtered"
                            id="newArrivals"
                            role="tabpanel"
                        >
                            <div className="tf-grid-layout tf-col-2 lg-col-3 xl-col-4">
                                {selectedItems?.map((variant, i) => (
                                    <ProductCard1 key={i} variant={variant} />
                                ))}
                            </div>
                            <div className="sec-btn text-center">
                                <a href={`/products`} className="btn-line">
                                    View All Products
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    );
}

export function Product1Loader({ parentClass = "flat-spacing-3" }) {
    return (
        <section className={parentClass}>
            <div className="container">
                <div className="flat-animate-tab">
                    <ul className="tab-product justify-content-sm-center" role="tablist">
                        <li className="nav-tab-item">
                            <Shimmer height={48} width={200} />
                        </li>
                    </ul>
                    <div className="tab-content">
                        <div
                            className="tab-pane active show tabFilter filtered"
                            id="newArrivals"
                            role="tabpanel"
                        >
                            <div className="tf-grid-layout tf-col-2 lg-col-3 xl-col-4">
                                {[...Array(4)]?.map((_, i) => (
                                    <div key={i}
                                        className={`card-product fadeInUp`}
                                    >
                                        <div className="card-product-wrapper">

                                            <Placeholder
                                                className="lazyload img-product"
                                                width={600}
                                                height={800} />
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    );
}
