import React from "react";
import { Shimmer } from "react-shimmer";

export default function FilterMeta({ isLoading, allProps, productLength }: { isLoading: boolean, allProps: any, productLength: number }) {
    return (
        <div className="meta-filter-shop" style={{}}>
            {!isLoading && <div id="product-count-grid" className="count-text">
                <span className="count">{productLength}</span> Products Found
            </div>}
            {isLoading && <div id="product-count-grid" className="count-text">
                <Shimmer height={24} width={100} />
            </div>}

            <div id="applied-filters">
                {allProps.availability.length ? (
                    <React.Fragment>
                        {allProps.availability.map((label: any, i: number) => (
                            <span
                                key={i}
                                className="filter-tag"
                                onClick={() => allProps.setAvailability(label)}
                            >
                                {label}
                                <span className="remove-tag icon-close" />
                            </span>
                        ))}
                    </React.Fragment>
                ) : (
                    ""
                )}
                {Object.entries(allProps.options as Record<string, any>).map(([key, value], index) => (
                    <span key={index}
                        className="filter-tag"
                        onClick={() => allProps.removeOption(key)}
                    >
                        <div style={{ textTransform: "capitalize" }}>{value}</div>
                        <span className="remove-tag icon-close" />
                    </span>
                ))}
                {allProps.brands.length ? (
                    <React.Fragment>
                        {allProps.brands.map((brand: any, i: number) => (
                            <span
                                key={i}
                                className="filter-tag"
                                onClick={() => allProps.setBrands(brand)}
                            >
                                {brand.split("-")[0]}
                                <span className="remove-tag icon-close" />
                            </span>
                        ))}
                    </React.Fragment>
                ) : (
                    ""
                )}
            </div>
            {allProps.availability.length ||
                Object.entries(allProps.options as Record<string, any>).some(([_, v]) => v) ||
                allProps.brands.length ? (
                <button
                    id="remove-all"
                    className="remove-all-filters text-btn-uppercase"
                    onClick={allProps.clearFilter}
                >
                    REMOVE ALL <i className="icon icon-close" />
                </button>
            ) : (
                ""
            )}
        </div>
    );
}
