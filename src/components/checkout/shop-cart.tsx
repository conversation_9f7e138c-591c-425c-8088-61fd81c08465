import { Image } from "../common/image";
import { useContextElement } from "@/layout/context";

const discounts: any[] | undefined = undefined
//  [
//     {
//         discount: "10% OFF",
//         details: "For all orders from ₹200",
//         code: "Mo234231",
//     },
//     {
//         discount: "10% OFF",
//         details: "For all orders from ₹200",
//         code: "Mo234231",
//     },
//     {
//         discount: "10% OFF",
//         details: "For all orders from ₹200",
//         code: "Mo234231",
//     },
// ];

export default function ShopCart() {
    const { cartDetails, addToCart } = useContextElement()
    const cartItems = cartDetails?.order_summary.items

    return (
        <>
            <section className="flat-spacing">
                <div className="container">
                    <div className="row">
                        <div className="col-xl-8">

                            {cartItems?.length ? (
                                <form onSubmit={(e) => e.preventDefault()}>
                                    <table className="tf-table-page-cart">
                                        <thead>
                                            <tr>
                                                <th>Products</th>
                                                <th>Price</th>
                                                <th>Quantity</th>
                                                <th>Total Price</th>
                                                <th />
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {cartItems.map((elm, i) => (
                                                <tr key={i} className="tf-cart-item file-delete">
                                                    <td className="tf-cart-item_product">
                                                        <a
                                                            href={`/product-detail/${elm.id}`}
                                                            className="img-box"
                                                            style={{
                                                                aspectRatio: 3 / 4,
                                                                width: "90px",
                                                                overflow: "visible"
                                                            }}
                                                        >
                                                            <Image
                                                                alt="product"
                                                                src={elm.images[0].image_url}
                                                            />
                                                        </a>
                                                        <div className="cart-info">
                                                            <a
                                                                href={`/product-details/${elm.slug}`}
                                                                className="cart-title link"
                                                            >
                                                                {elm.name}
                                                            </a>
                                                            <div className="applied-filters">
                                                                {Object.entries(elm.options)
                                                                    .filter(([key]) => !["brand_id", "max_price"].includes(key))
                                                                    .map(([_, value], index) => (
                                                                        <div key={index} className="tf-options">
                                                                            <div style={{ textTransform: "capitalize" }}>{value}</div>
                                                                        </div>
                                                                    ))}
                                                            </div>

                                                        </div>
                                                    </td>
                                                    <td
                                                        data-cart-title="Price"
                                                        className="tf-cart-item_price text-center"
                                                    >
                                                        <div className="cart-price text-button price-on-sale">
                                                            ₹{elm.selling_price.toFixed(2)}
                                                        </div>
                                                    </td>
                                                    <td
                                                        data-cart-title="Quantity"
                                                        className="tf-cart-item_quantity"
                                                    >
                                                        <div className="wg-quantity mx-md-auto">
                                                            <span
                                                                className="btn-quantity btn-decrease"
                                                                onClick={() => {
                                                                    const qty = elm.quantity - 1
                                                                    addToCart(elm.id, qty)
                                                                }
                                                                }
                                                            >
                                                                -
                                                            </span>
                                                            <input
                                                                type="text"
                                                                className="quantity-product"
                                                                name="number"
                                                                value={elm.quantity}
                                                                readOnly
                                                            />
                                                            <span
                                                                className="btn-quantity btn-increase"
                                                                onClick={() => {
                                                                    const qty = elm.quantity + 1
                                                                    addToCart(elm.id, qty)
                                                                }
                                                                }
                                                            >
                                                                +
                                                            </span>
                                                        </div>
                                                    </td>
                                                    <td
                                                        data-cart-title="Total"
                                                        className="tf-cart-item_total text-center"
                                                    >
                                                        <div className="cart-total text-button total-price">
                                                            ₹{elm.product_total}
                                                        </div>
                                                    </td>
                                                    <td
                                                        data-cart-title="Remove"
                                                        className="remove-cart"
                                                        onClick={() => addToCart(elm.id, 0)}
                                                    >
                                                        <span className="remove icon icon-close" />
                                                    </td>
                                                </tr>
                                            ))}
                                        </tbody>
                                    </table>
                                    {/* <div className="ip-discount-code">
                                        <input type="text" value={cartDetails?.coupon?.name ?? ""}
                                            placeholder="Add voucher discount"
                                            readOnly={cartDetails?.coupon?.is_applied}
                                            onChange={(_) => { }}
                                        />
                                        {cartDetails?.coupon?.is_applied && <div className="tf-btn tf-clear">
                                            <span className="remove icon icon-close" />
                                        </div>}

                                        {!cartDetails?.coupon?.is_applied && <button className="tf-btn">
                                            <span className="text">Apply Code</span>
                                        </button>}
                                    </div> */}
                                    <div className="group-discount">
                                        {discounts && discounts.map((item, index) => (
                                            <div
                                                key={index}
                                                className={`box-discount ${cartDetails?.coupon?.id === index ? "active" : ""
                                                    }`}
                                            //   onClick={() => setActiveDiscountIndex(index)}
                                            >
                                                <div className="discount-top" style={{ display: "flow" }}>
                                                    <div className="discount-off">
                                                        <div className="sale-off text-btn-uppercase">{item.discount}</div>
                                                    </div>
                                                    <div className="discount-from">
                                                        <p className="text-caption-1">{item.details}</p>
                                                    </div>
                                                </div>
                                                <div className="discount-bot">
                                                    <span className="text-btn-uppercase">
                                                        {item.code}
                                                    </span>
                                                    <button className="tf-btn">
                                                        <span className="text">{cartDetails?.coupon?.id === index ? "Remove" : "Apply Code"}</span>
                                                    </button>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </form>
                            ) : (
                                <div>
                                    Your Cart is empty. Start adding your favorite products to
                                    save them for later!{" "}
                                    <a className="btn-line" href="/products">
                                        Explore Products
                                    </a>
                                </div>
                            )}
                        </div>
                        {(cartItems?.length ?? 0) > 0 && <div className="col-xl-4">
                            <div className="fl-sidebar-cart">
                                <div className="box-order bg-surface">
                                    <h5 className="title">Order Summary</h5>
                                    <div className="subtotal text-button d-flex justify-content-between align-items-center">
                                        <span>Subtotal</span>
                                        <span className="total">₹{cartDetails?.order_summary.subtotal.toFixed(2)}</span>
                                    </div>
                                    <div className="discount text-button d-flex justify-content-between align-items-center">
                                        <span>Discounts</span>
                                        <span className="total">₹{cartDetails?.order_summary.discount.toFixed(2) ?? 0}</span>
                                    </div>
                                    {(cartDetails?.order_summary.coupon_discount ?? 0) > 0 && <div className="discount text-button d-flex justify-content-between align-items-center">
                                        <span>Coupon</span>
                                        <span className="total">₹{cartDetails?.order_summary.coupon_discount.toFixed(2)}</span>
                                    </div>}

                                    <h5 className="total-order d-flex justify-content-between align-items-center">
                                        <span>Total</span>
                                        <span className="total">
                                            ₹
                                            {cartDetails?.order_summary.total.toFixed(2) ?? 0}
                                        </span>
                                    </h5>
                                    <div className="box-progress-checkout">
                                        {/* <fieldset className="check-agree">
                                            <input
                                                type="checkbox"
                                                id="check-agree"
                                                className="tf-check-rounded"
                                            />
                                            <label htmlFor="check-agree">
                                                I agree with the&nbsp;
                                                <a href={`/term-of-use`}>terms and conditions</a>
                                            </label>
                                        </fieldset> */}
                                        <a href={`/checkout`} className="tf-btn btn-reset">
                                            Checkout
                                        </a>
                                        <a
                                            href={`/products`}
                                            className="link text-btn-uppercase text-center"
                                        >
                                            Or Continue Shopping
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>}
                    </div>
                </div>
            </section>
        </>
    );
}
