import { useEffect, useState } from "react";
import GridView from "../products/grid-view";
import { useContextElement } from "@/layout/context";


export default function WishList({ parentClass = "flat-spacing" }: { parentClass: string }) {
    const [activeLayout, setActiveLayout] = useState(4);
    const { wishlist } = useContextElement()


    useEffect(() => {
        const handleResize = () => {
            if (window.innerWidth > 1200) {
                setActiveLayout(4);
            } else if (window.innerWidth < 1200 && window.innerWidth > 767) {
                setActiveLayout(3);
            } else if (window.innerWidth < 768) {
                setActiveLayout(2);
            }
        };
        handleResize();
        // Add the resize event listener
        window.addEventListener("resize", handleResize);

        // Clean up the event listener on unmount
        return () => {
            window.removeEventListener("resize", handleResize);
        };
    }, []);

    return (
        <>
            <section className={parentClass}>
                <div className="container mt-4">
                    <div className="wrapper-control-shop">

                        <div
                            className={`tf-grid-layout wrapper-shop tf-col-${activeLayout}`}
                            id="gridLayout"
                        >
                            {wishlist && <GridView pagination data={{
                                data: wishlist,
                                total: wishlist.length,
                                current_page: 1,
                                pages: 1,
                            }} />}
                        </div>
                    </div>
                </div>
            </section>

        </>
    )
}