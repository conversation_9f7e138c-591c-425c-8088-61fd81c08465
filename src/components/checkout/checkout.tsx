import { useContextElement } from "@/layout/context"
import React, { useEffect, useState } from "react";
import { useGetAddress } from "@/hooks/auth-quries";
import { UserAddress } from "@/types/user";
import { Loader } from "../common/query-wrapper";
import { useCheckoutDetails, usePlaceOrder } from "@/hooks/checkout-quires";

const SuccessAlert = React.lazy(() => import('../common/success-alert').then(module => ({ default: module.SuccessAlert })));
const FailedAlert = React.lazy(() => import('../common/failed-alert').then(module => ({ default: module.FailedAlert })));
const Swiper = React.lazy(() => import('swiper/react').then(module => ({ default: module.Swiper })));
const SwiperSlide = React.lazy(() => import('swiper/react').then(module => ({ default: module.SwiperSlide })));


const discounts: any[] | undefined = undefined
//  [
//     {
//         discount: "10% OFF",
//         details: "For all orders from ₹200",
//         code: "Mo234231",
//     },
//     {
//         discount: "10% OFF",
//         details: "For all orders from ₹200",
//         code: "Mo234231",
//     },
//     {
//         discount: "10% OFF",
//         details: "For all orders from ₹200",
//         code: "Mo234231",
//     },
// ];

export default function CheckoutView() {
    const productId = new URLSearchParams(window.location.search).get("id");
    const { data: cartDetails, isLoading: loading } = useCheckoutDetails(productId ? productId : undefined);

    const { setIsLoading, showAlert, closeAlert } = useContextElement();
    const { isLoading, data: addresses } = useGetAddress();
    const [showAddressList, setShowAddressList] = useState(false);
    const [selectedAddress, setSelectedAddress] = useState<UserAddress>();
    const [tempSelectedId, setTempSelectedId] = useState<number | string>(-1);

    const { mutate: placeOrder } = usePlaceOrder((isLoading: boolean) => {
        setIsLoading(isLoading);
    }, (data: [string, string]) => {
        showAlert(
            <SuccessAlert
                orderNumber={data[0]}
                onViewOrder={() => {
                    closeAlert();
                    window.location.href = `/order/${data[1]}`;
                }}
            />,
            "lg",
            false,
        )
    }, (_: any) => {
        showAlert(
            <FailedAlert
                errorMessage="Something went wrong. Please try again."
                onClose={closeAlert}
                onRetry={() => {
                    closeAlert();
                    handlePlaceOrder();
                }}
            />,
            "lg",
            true,
        )
    });

    const handlePlaceOrder = async () => {
        if (selectedAddress) {
            placeOrder({
                address: selectedAddress,
                coupon: cartDetails?.coupon?.name,
                productId: productId ? productId : undefined
            });
        } else {
            alert("Please select an address");
        }
    };

    useEffect(() => {
        if (addresses && addresses.length > 0) {
            const defaultAddress = addresses.find(address => address.is_default) || addresses[0];
            setSelectedAddress(defaultAddress);
            setTempSelectedId(defaultAddress.id);
        }
    }, [addresses]);

    const handleAddressSelect = () => {
        const address = addresses?.find(a => a.id === tempSelectedId);
        if (address) {
            setSelectedAddress(address);
            setShowAddressList(false);
        }
    };

    const formatAddress = (address: UserAddress) => {
        const addressParts = [
            address.address_line_1,
            address.address_line_2,
            address.landmark
        ].filter(part => part && part.trim().length > 0);

        return (
            <div className="address-details">
                <div className="">
                    <strong>{address.name}</strong>
                </div>
                <div className="address-lines">
                    <div>{addressParts.join(', ')}</div>
                    <div>{`${address.city}, ${address.state} - ${address.pin_code}`}</div>
                </div>
                <span>Mob: {address.contact_number}</span>
                {address.address_type && (
                    <div className="address-type">
                        <span className="badge">{address.address_type}</span>
                    </div>
                )}
                {address.comments && (
                    <div className="delivery-instructions text-secondary">
                        <small>Note: {address.comments}</small>
                    </div>
                )}
            </div>
        );
    };

    if ((!cartDetails || cartDetails.order_summary.items.length === 0)) {
        return <section className="flat-spacing">
            <div className="container">
                <div className="row">
                    <div className="col-xl-8">
                        <div >
                            Your Cart is empty. Start adding your favorite products to
                            save them for later!{" "}
                            <a className="btn-line" href="/products">
                                Explore Products
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    }

    if (isLoading || loading) return <Loader />;

    return (
        <section>
            <div className="container">
                <div className="row">
                    <div className="col-xl-6">
                        <div className="flat-spacing pt-4 tf-page-checkout">
                            <div className="wrap">
                                <h6 className="title">Delivery Address</h6>
                                {!addresses || addresses.length === 0 ? (
                                    <div className="empty-address-container p-4">
                                        <p className="mb-3">No addresses found</p>
                                        <a className="text-btn"
                                            data-bs-toggle="modal"
                                            data-bs-target="#addressModal"
                                            style={{ fontSize: '0.85rem' }}>
                                            + Add new address
                                        </a>
                                    </div>
                                ) : !showAddressList ? (
                                    <div className="address-container">
                                        {selectedAddress && formatAddress(selectedAddress)}
                                        <a className="text-btn change-btn"
                                            onClick={() => {
                                                setShowAddressList(true);
                                                setTempSelectedId(selectedAddress?.id || -1);
                                            }}>
                                            Change
                                        </a>
                                    </div>
                                ) : (
                                    <div className="address-list-container border rounded p-3">
                                        {addresses?.map((address) => (
                                            <div key={address.id}
                                                className="address-item mb-3">
                                                <label className="address-radio-container d-flex p-3">
                                                    <input
                                                        type="radio"
                                                        name="address"
                                                        checked={tempSelectedId === address.id}
                                                        onChange={() => setTempSelectedId(address.id)}
                                                        className="tf-check-rounded mt-2"
                                                    />
                                                    <div className="ms-3 flex-grow-1">
                                                        {formatAddress(address)}
                                                    </div>
                                                </label>
                                            </div>
                                        ))}
                                        <div className="address-actions d-flex justify-content-between align-items-center pt-3">
                                            <button
                                                className="tf-btn-sm"
                                                onClick={handleAddressSelect}
                                                disabled={tempSelectedId === -1}
                                                style={{ fontSize: '0.85rem', padding: '6px 12px', height: '32px' }}
                                            >
                                                Use this address
                                            </button>
                                            <a className="text-btn"
                                                data-bs-toggle="modal"
                                                data-bs-target="#addressModal"
                                                style={{ fontSize: '0.85rem' }}>
                                                + Add new address
                                            </a>
                                        </div>
                                    </div>
                                )}
                            </div>
                            <div className="wrap">
                                <h5 className="title">Choose payment Option:</h5>
                                <form
                                    className="form-payment"
                                    onSubmit={(e) => e.preventDefault()}
                                >
                                    <div className="payment-box" id="payment-box">
                                        <div className="payment-item payment-choose-card active">
                                            <label
                                                htmlFor="delivery-method"
                                                className="payment-header"
                                                data-bs-toggle="collapse"
                                                data-bs-target="#delivery-payment"
                                                aria-controls="delivery-payment"
                                            >
                                                <input
                                                    type="radio"
                                                    name="payment-method"
                                                    className="tf-check-rounded"
                                                    id="delivery-method"
                                                    defaultChecked
                                                    readOnly
                                                />
                                                <span className="text-title">Cash on delivery</span>
                                            </label>
                                            <div
                                                id="credit-card-payment"
                                                className=" show"
                                                data-bs-parent="#payment-box"
                                            >
                                                <div className="payment-body">
                                                    <p className="text-secondary">
                                                        Make your payment directly in cash upon delivery. Please ensure you have the exact amount ready, as change may not always be available.
                                                    </p>
                                                </div>
                                            </div>
                                            <div
                                                id="delivery-payment"
                                                className="collapse"
                                                data-bs-parent="#payment-box"
                                            />
                                        </div>
                                        <div className="payment-item">
                                            <label
                                                htmlFor="credit-card-method"
                                                className="payment-header collapsed"
                                                data-bs-toggle="collapse"
                                                data-bs-target="#credit-card-payment"
                                                aria-controls="credit-card-payment"
                                                style={{
                                                    color: "#bbb",
                                                    cursor: "not-allowed",
                                                }}
                                            >
                                                <input
                                                    type="radio"
                                                    name="payment-method"
                                                    className="tf-check-rounded"
                                                    id="credit-card-method"
                                                    disabled
                                                    readOnly
                                                    style={{
                                                        cursor: "not-allowed"
                                                    }}
                                                />
                                                <span className="text-title">Credit Card</span>
                                            </label>

                                        </div>

                                        <div className="payment-item">
                                            <label
                                                htmlFor="apple-method"
                                                className="payment-header collapsed"
                                                data-bs-toggle="collapse"
                                                data-bs-target="#apple-payment"
                                                aria-controls="apple-payment"
                                                style={{
                                                    color: "#bbb",
                                                    cursor: "not-allowed",
                                                }}
                                            >
                                                <input
                                                    type="radio"
                                                    name="payment-method"
                                                    className="tf-check-rounded"
                                                    id="apple-method"
                                                    disabled
                                                    readOnly
                                                    style={{
                                                        cursor: "not-allowed"
                                                    }}
                                                />
                                                <span className="text-title apple-pay-title">
                                                    Netbanking
                                                </span>
                                            </label>
                                            <div
                                                id="apple-payment"
                                                className="collapse"
                                                data-bs-parent="#payment-box"
                                            />
                                        </div>
                                        <div className="payment-item paypal-item">
                                            <label
                                                htmlFor="paypal-method"
                                                className="payment-header collapsed"
                                                data-bs-toggle="collapse"
                                                data-bs-target="#paypal-method-payment"
                                                aria-controls="paypal-method-payment"
                                                style={{
                                                    color: "#bbb",
                                                    cursor: "not-allowed",
                                                }}
                                            >
                                                <input
                                                    type="radio"
                                                    name="payment-method"
                                                    className="tf-check-rounded"
                                                    id="paypal-method"
                                                    disabled
                                                    readOnly
                                                    style={{
                                                        cursor: "not-allowed"
                                                    }}
                                                />
                                                <span className="paypal-title">
                                                    UPI
                                                </span>
                                            </label>
                                            <div
                                                id="paypal-method-payment"
                                                className="collapse"
                                                data-bs-parent="#payment-box"
                                            />
                                        </div>
                                    </div>
                                    <button className="tf-btn btn-reset" onClick={handlePlaceOrder}>Place Order</button>
                                </form>
                            </div>
                        </div>
                    </div>
                    <div className="col-xl-1">
                        <div className="line-separation" />
                    </div>
                    <div className="col-xl-5">
                        <div className="flat-spacing flat-sidebar-checkout">
                            <div className="sidebar-checkout-content">
                                <h5 className="title">Shopping Cart</h5>
                                <div className="list-product">
                                    {cartDetails?.order_summary.items.map((elm, i) => (
                                        <div key={i} className="item-product">
                                            <a
                                                href={`/product-detail/${elm.id}`}
                                                className="img-product"
                                                style={{
                                                    aspectRatio: 3 / 4,
                                                    width: "90px",
                                                    overflow: "visible"
                                                }}
                                            >
                                                <img
                                                    alt="img-product"
                                                    src={elm.images[0].image_url}
                                                    width={90}
                                                // height={800}
                                                />
                                            </a>
                                            <div className="content-box">
                                                <div className="info">
                                                    <a
                                                        href={`/product-details/${elm.slug}`}
                                                        className="name-product link text-title"
                                                    >
                                                        {elm.name}
                                                    </a>
                                                    <div className="variant text-caption-1 text-secondary" style={{ display: "flex" }}>
                                                        {Object.entries(elm.options)
                                                            .filter(([key]) => !["brand_id", "max_price"].includes(key))
                                                            .map(([_, value], index) => (
                                                                <div key={index} className="tf-options" style={{ paddingRight: "8px" }}>
                                                                    <div style={{ textTransform: "capitalize" }}>{value}</div>
                                                                </div>
                                                            ))}
                                                    </div>
                                                </div>
                                                <div className="total-price text-button">
                                                    <span className="count">{elm.quantity}</span>X
                                                    <span className="price">₹{elm.selling_price.toFixed(2)}</span>
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                                <div className="sec-discount">
                                    <Swiper
                                        dir="ltr"
                                        className="swiper tf-sw-categories"
                                        slidesPerView={2.25} // data-preview="2.25"
                                        breakpoints={{
                                            1024: {
                                                slidesPerView: 2.25, // data-tablet={3}
                                            },
                                            768: {
                                                slidesPerView: 3, // data-tablet={3}
                                            },
                                            640: {
                                                slidesPerView: 2.5, // data-mobile-sm="2.5"
                                            },
                                            0: {
                                                slidesPerView: 1.2, // data-mobile="1.2"
                                            },
                                        }}
                                        spaceBetween={20}
                                    >
                                        {discounts && discounts.map((item, index) => (
                                            <SwiperSlide key={index}>
                                                <div
                                                    className={`box-discount ${cartDetails?.coupon?.id === index ? "active" : ""
                                                        }`}
                                                // onClick={() => setActiveDiscountIndex(index)}
                                                >
                                                    <div className="discount-top">
                                                        <div className="discount-off">
                                                            <div className="text-caption-1">Discount</div>
                                                            <span className="sale-off text-btn-uppercase">
                                                                {item.discount}
                                                            </span>
                                                        </div>
                                                        <div className="discount-from">
                                                            <p className="text-caption-1">{item.details}</p>
                                                        </div>
                                                    </div>
                                                    <div className="discount-bot">
                                                        <span className="text-btn-uppercase">
                                                            {item.code}
                                                        </span>
                                                        <button className="tf-btn">
                                                            <span className="text">{cartDetails?.coupon?.id === index ? "Remove" : "Apply Code"}</span>
                                                        </button>
                                                    </div>
                                                </div>{" "}
                                            </SwiperSlide>
                                        ))}
                                    </Swiper>
                                    {/* <div className="ip-discount-code">
                                        <input type="text" value={cartDetails?.coupon?.name ?? ""}
                                            placeholder="Add voucher discount"
                                            readOnly={cartDetails?.coupon?.is_applied}
                                            onChange={(_) => { }}
                                        />
                                        {cartDetails?.coupon?.is_applied && <div className="tf-btn tf-clear">
                                            <span className="remove icon icon-close" />
                                        </div>}

                                        {!cartDetails?.coupon?.is_applied && <button className="tf-btn">
                                            <span className="text">Apply Code</span>
                                        </button>}
                                    </div>
                                    <p>
                                        {cartDetails?.coupon?.is_applied ? cartDetails.coupon.message : ""}
                                    </p> */}
                                </div>
                                <div className="sec-total-price">
                                    <div className="top">
                                        <div className="item d-flex align-items-center justify-content-between text-button">
                                            <span>Subtotal</span>
                                            <span>₹{cartDetails?.order_summary.subtotal.toFixed(2)}</span>
                                        </div>
                                        <div className="item d-flex align-items-center justify-content-between text-button">
                                            <span>Discounts</span>
                                            <span>₹{cartDetails?.order_summary.discount.toFixed(2)}</span>
                                        </div>
                                        {(cartDetails?.order_summary.coupon_discount ?? 0) > 0 && <div className="item d-flex align-items-center justify-content-between text-button">
                                            <span>Coupon</span>
                                            <span>-₹
                                                {cartDetails?.order_summary.coupon_discount.toFixed(2) ?? 0}</span>
                                        </div>}
                                    </div>
                                    <div className="bottom">
                                        <h5 className="d-flex justify-content-between">
                                            <span>Total</span>
                                            <span className="total-price-checkout">
                                                ₹{cartDetails?.order_summary.total.toFixed(2)}
                                            </span>
                                        </h5>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    );
}