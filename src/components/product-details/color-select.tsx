import React, { useState } from "react";
import type { ProductVariant } from "../../types/product/product";

interface Props {
  variant: ProductVariant | undefined;
  colorOptions?: { id: string; value: string; image: string | undefined }[];
}

export default function ColorSelect({ variant, colorOptions, }: Props) {
  const uniqueColorOptions = colorOptions?.filter(
    (option, index, self) =>
      index === self.findIndex((t) => t.value === option.value)
  ) ?? [];
  const [showAllColors, setShowAllColors] = useState(false);


  return (
    <div className="variant-picker-item">
      <div className="variant-picker-label mb_12">
        Colors:
        <span
          className="text-title variant-picker-label-value value-currentColor"
          style={{ textTransform: "capitalize" }}
        >
          {variant?.options?.color ?? ""}
        </span>
      </div>
      <div className="variant-picker-values">
        {uniqueColorOptions?.slice(0, 5).map(({ id, value, image }) => (
          colorFragment({ id, value, image, variant })
        ))}
        {!showAllColors && (uniqueColorOptions?.length ?? 0) > 5 && (
          <a><i onClick={() => setShowAllColors(!showAllColors)}>
            Show More
          </i></a>
        )}
        {showAllColors && uniqueColorOptions?.slice(5).map(({ id, value, image }) => (
          colorFragment({ id, value, image, variant })

        ))}
        {showAllColors && (uniqueColorOptions?.length ?? 0) > 5 && (
          <a><i onClick={() => setShowAllColors(!showAllColors)}>
            Show Less
          </i></a>
        )}
      </div>
    </div>
  );
}

function colorFragment({ id, value, image, variant }: { id: string; value: string; image: string | undefined; variant: ProductVariant | undefined }) {
  // const [loading, setLoading] = useState(true);
  return (
    <a key={id} href={id.toString()}>
      <React.Fragment key={id}>
        {/* <input id={id} type="radio" name={value} readOnly /> */}
        <label
          className={`style-image hover-tooltip tooltip-bot color-btn  ${variant?.options?.color == value ? "active" : ""
            } `}
          htmlFor={id}
        // onClick={() => handleSelectColor(option.color)}
        >
          <img
            className="ls-is-cached lazyloaded"
            data-src={image}
            alt={image ?? ""}
            src={image ?? ""}
            width={600}
            height={800}
          />
          <span className="tooltip">{value}</span>
        </label>
      </React.Fragment>
    </a>
  );
}
