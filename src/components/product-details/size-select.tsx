import { ProductVariant } from "../../types/product/product";
// import { useState } from "react";

interface Props {
  title: string;
  variant: ProductVariant | undefined;
  options: { id: number; key: string; value: string; stock: number }[];
}

export default function SizeSelect({ title, variant, options }: Props) {
  // const [selectedSize, setSelectedSize] = useState("L"); // Default value is "L"


  // const handleChange = (value: string) => {
  //   setSelectedSize(value);
  // };

  return (
    <div className="variant-picker-item">
      <div className="d-flex justify-content-between mb_12">
        <div className="variant-picker-label">
          {title.charAt(0).toUpperCase() + title.slice(1) + `:`}
          <span className="text-title variant-picker-label-value">
            {variant?.options[title]}
          </span>
        </div>
        {/* Size Guide */}
        {/* <a
          href="#size-guide"
          data-bs-toggle="modal"
          className="size-guide text-title link"
        >
          Size Guide
        </a> */}
      </div>
      <div className="variant-picker-values gap12">
        {options?.map((option: any) => (
          <a href={option.slug?.toString()} key={option.id?.toString()}>
            <div key={option.id?.toString()}>
              <input
                type="radio"
                id={option.key + "_" + option.id?.toString()}
                checked={variant?.options[title] === option.value}
                disabled={option.stock < 1}
                readOnly
              />
              <label
                className={`style-text-1 size-btn ${option.stock < 1 ? "type-disable" : ""
                  }`}
                htmlFor={option.id?.toString()}
                data-value={option.value}
              >
                <span className="text-title">{option.value}</span>
              </label>
            </div>
          </a>
        ))}
      </div>
    </div>
  );
}
