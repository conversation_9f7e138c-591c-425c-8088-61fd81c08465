"use client";
// import { slides } from "@/data/singleProductSliders";
import Drift from "drift-zoom";
import PhotoSwipeLightbox from "photoswipe/lightbox";
import { useEffect, useRef, useState } from "react";
import { Thumbs } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";
// import Image from "next/image";
import { Swiper as SwiperType } from 'swiper/types';
import { ProductImage, ProductVideo } from "../../../types/product/product";
// import VideoPlayer from "@/components/common/video";


interface Props {
  slideImages?: ProductImage[];
  sliderVideos?: ProductVideo[] | string[] | undefined;
  thumbSlidePerView?: number;
  thumbSlidePerViewOnMobile?: number;
}

export default function Slider1({ slideImages = [] = [], thumbSlidePerView = 6, thumbSlidePerViewOnMobile = 6 }: Props) {
  const items = [...slideImages];

  useEffect(() => {
    // Function to initialize Drift
    const imageZoom = () => {
      const driftAll = document.querySelectorAll(".tf-image-zoom");
      const pane = document.querySelector(".tf-zoom-main");

      driftAll.forEach((el) => {
        new Drift(el as HTMLElement, {
          zoomFactor: 2,
          paneContainer: pane as HTMLElement,
          inlinePane: 0,
          handleTouch: false,
          hoverBoundingBox: true,
          containInline: true,
        });
      });
    };
    imageZoom();
    const zoomElements = document.querySelectorAll(".tf-image-zoom");

    const handleMouseOver = (event: any) => {
      const parent = event.target.closest(".section-image-zoom");
      if (parent) {
        parent.classList.add("zoom-active");
      }
    };

    const handleMouseLeave = (event: any) => {
      const parent = event.target.closest(".section-image-zoom");
      if (parent) {
        parent.classList.remove("zoom-active");
      }
    };

    zoomElements.forEach((element) => {
      element.addEventListener("mouseover", handleMouseOver);
      element.addEventListener("mouseleave", handleMouseLeave);
    });

    // Cleanup event listeners on component unmount
    return () => {
      zoomElements.forEach((element) => {
        element.removeEventListener("mouseover", handleMouseOver);
        element.removeEventListener("mouseleave", handleMouseLeave);
      });
    };
  }, []); // Empty dependency array to run only once on mount

  const lightboxRef = useRef<PhotoSwipeLightbox | null>(null);
  useEffect(() => {
    // Initialize PhotoSwipeLightbox
    const lightbox = new PhotoSwipeLightbox({
      gallery: "#gallery-swiper-started",
      children: ".item",
      pswpModule: () => import("photoswipe"),
    });

    lightbox.init();

    // Store the lightbox instance in the ref for later use
    lightboxRef.current = lightbox;

    // Cleanup: destroy the lightbox when the component unmounts
    return () => {
      lightbox.destroy();
    };
  }, []);

  const [thumbsSwiper, setThumbsSwiper] = useState<SwiperType | null>(null);
  //   const [activeIndex, setActiveIndex] = useState(0);



  const swiperRef = useRef<SwiperType | null>(null);
  //   useEffect(() => {
  //     if (!(items[activeIndex].color == activeColor)) {
  //       const slideIndex =
  //         items.filter((elm) => elm.color == activeColor)[0]?.id - 1;
  //       swiperRef.current.slideTo(slideIndex);
  //     }
  //   }, [activeColor]);
  //   useEffect(() => {
  //     setTimeout(() => {
  //       if (swiperRef.current) {
  //         swiperRef.current.slideTo(1);
  //         swiperRef.current.slideTo(
  //           items.filter((elm) => elm.color == activeColor)[0]?.id - 1
  //         );
  //       }
  //     });
  //   }, []);

  return (
    <div className="thumbs-slider">
      <Swiper
        className="swiper tf-product-media-thumbs other-image-zoom"
        dir="ltr"
        direction="vertical"
        spaceBetween={10}
        slidesPerView={thumbSlidePerView}
        onSwiper={setThumbsSwiper}
        modules={[Thumbs]}
        initialSlide={1}
        breakpoints={{
          0: {
            direction: "horizontal",
            slidesPerView: thumbSlidePerViewOnMobile,
          },
          820: {
            direction: "horizontal",
            slidesPerView:
              thumbSlidePerViewOnMobile < 4
                ? thumbSlidePerViewOnMobile + 1
                : thumbSlidePerViewOnMobile,
          },
          920: {
            direction: "horizontal",
            slidesPerView:
              thumbSlidePerViewOnMobile < 4
                ? thumbSlidePerViewOnMobile + 2
                : thumbSlidePerViewOnMobile,
          },
          1020: {
            direction: "horizontal",
            slidesPerView:
              thumbSlidePerViewOnMobile < 4
                ? thumbSlidePerViewOnMobile + 2.5
                : thumbSlidePerViewOnMobile,
          },
          1200: {
            direction: "vertical",
            slidesPerView: thumbSlidePerView,
          },
        }}
      >
        {items.map((slide, index) => (
          <SwiperSlide
            className="swiper-slide stagger-item"
            // data-color={slide.color}
            key={index}
          >
            <div className="item">
              <img
                className="lazyload"
                data-src={slide.image_url}
                alt={slide.image_url}
                src={slide.image_url}
                width={slide.width}
                height={slide.height}
              />
            </div>
          </SwiperSlide>
        ))}
        {/* {sliderVideos.map((slide, index) => (
          <SwiperSlide
            className="swiper-slide stagger-item"
            // data-color={slide.color}
            key={index}
          >
            <div className="item">
              {(typeof slide === 'string') && <Image
                className="lazyload"
                data-src={slide + "/default.jpg"}
                alt={slide ?? ""}
                src={slide ?? ""}
                width={340}
                height={380}
                placeholder="empty" />
              }
              {(slide && typeof slide !== 'string') && <Image
                className="lazyload"
                data-src={slide.thumbnail}
                alt={slide.thumbnail ?? ""}
                src={slide.thumbnail ?? ""}
                width={slide.width}
                height={slide.height}
                placeholder="empty"
              />}
            </div>
          </SwiperSlide>
        ))} */}
      </Swiper>
      <Swiper
        dir="ltr"
        className="swiper tf-product-media-main"
        id="gallery-swiper-started"
        spaceBetween={10}
        slidesPerView={1}
        thumbs={{ swiper: thumbsSwiper }}
        modules={[Thumbs]}
        onSwiper={(swiper) => (swiperRef.current = swiper)}
        onSlideChange={(swiper) => {
          if (items[swiper.activeIndex]) {
            // setActiveIndex(swiper.activeIndex);
            // setActiveColor(items[swiper.activeIndex]?.color.toLowerCase());
          }
        }}
      >
        {items.map((slide, index) => (
          <SwiperSlide key={index} className="swiper-slide" data-color="gray">
            <a
              href={slide.image_url}
              target="_blank"
              className="item"
              data-pswp-width={slide.width}
              data-pswp-height={slide.height}
            //   onClick={() => openLightbox(index)}
            >

              <img
                className="tf-image-zoom lazyload"
                data-zoom={slide.image_url}
                data-src={slide.image_url}
                alt=""
                src={slide.image_url}
                width={slide.width}
                height={slide.height}
              />
            </a>
          </SwiperSlide>
        ))}
        {/* {sliderVideos.map((slide, index) => (
          <SwiperSlide key={index} className="swiper-slide" data-color="gray">
            <VideoPlayer video={slide} />
          </SwiperSlide>
        ))} */}
      </Swiper>
    </div>
  );
}
