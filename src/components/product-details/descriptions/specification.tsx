import { Product, ProductVariant } from "../../../types/product/product";

export default function Specification({ varient }: { product: Product, varient: ProductVariant }) {
    return (
        <table className="tf-pr-attrs">
            <tbody>
                {varient?.spec && Object.entries(varient.spec).map((v) => (
                    <tr key={v[0]} className="tf-attr-pa-color">
                        <th className="tf-attr-label">{(v[0] as any).replaceAll("_", " ").capitalizeFirstLetter()}</th>
                        <td className="tf-attr-value">{v[1]}</td>
                    </tr>
                ))}
            </tbody>
        </table>
    );
}