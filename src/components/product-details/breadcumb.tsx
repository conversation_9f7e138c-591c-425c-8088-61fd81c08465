import { Product } from '../../types/product/product';
import { useContextElement } from '@/layout/context';
import { useEffect, useState } from 'react';

export default function Breadcumb({ product }: { product: Product }) {

  const { navData } = useContextElement()

  const [categorySlug, setCategorySlug] = useState("")
  const [subCategorySlug, setSubCategorySlug] = useState("")

  useEffect(() => {
    const category = navData?.find((n) => n.id == product.category_id)
    setCategorySlug(category?.slug ?? "")
    const subcategory = category?.sub_categories?.find((s) => s.id == product.sub_category_id)
    setSubCategorySlug(subcategory?.slug ?? "")


  }, [navData])

  return (
    <div className="tf-breadcrumb">
      <div className="container">
        <div className="tf-breadcrumb-wrap">
          <div className="tf-breadcrumb-list">
            <a href={`/`} className="text text-caption-1">
              Home
            </a>

            <i className="icon icon-arrRight" />
            <a href={`/${categorySlug}`} className="text text-caption-1">{product.category}</a>
            <i className="icon icon-arrRight" />
            <a href={`/sub/${subCategorySlug}`} className="text text-caption-1">{product.sub_category}</a>
            <i className="icon icon-arrRight" />
            <span className="text text-caption-1">{product.name}</span>
          </div>
          <div className="tf-breadcrumb-prev-next">
            {/* <Link
              href={`/${pathname.split("/")[1]}/${
                product.id <= 1 ? 1 : product.id - 1
              }`}
              className="tf-breadcrumb-prev"
            >
              <i className="icon icon-arrLeft" />
            </Link>
            <a href="#" className="tf-breadcrumb-back">
              <i className="icon icon-squares-four" />
            </a> */}
            {/* <Link
              href={`/${pathname.split("/")[1]}/${
                product.id >= 6 ? 1 : product.id + 1
              }`}
              className="tf-breadcrumb-next"
            >
              <i className="icon icon-arrRight" />
            </Link> */}
          </div>
        </div>
      </div>
    </div>
  );
}
