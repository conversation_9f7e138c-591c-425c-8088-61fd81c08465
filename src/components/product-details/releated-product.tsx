import { Swiper, SwiperSlide } from "swiper/react";
import { Pagination } from "swiper/modules";
import ProductCard1 from "../product-cards/product-card1";
import { ProductVariant } from "../../types/product/product";

export default function RelatedProducts({ products }: { products: ProductVariant[] }) {

    return (
        <section className="flat-spacing">
            <div className="container flat-animate-tab">
                <ul
                    className="tab-product justify-content-sm-center fadeInUp"
                    data-wow-delay="0s"
                    role="tablist"
                >
                    <li className="nav-tab-item" role="presentation">
                        <a href="#ralatedProducts" className="active" data-bs-toggle="tab">
                            Related Products
                        </a>
                    </li>
                    {/* <li className="nav-tab-item" role="presentation">
                        <a href="#recentlyViewed" data-bs-toggle="tab">
                            Recently Viewed
                        </a>
                    </li> */}
                </ul>
                <div className="tab-content">
                    <div
                        className="tab-pane active show"
                        id="ralatedProducts"
                        role="tabpanel"
                    >
                        <Swiper
                            className="swiper tf-sw-latest"
                            dir="ltr"
                            spaceBetween={15}
                            breakpoints={{
                                0: { slidesPerView: 2, spaceBetween: 15 },

                                768: { slidesPerView: 3, spaceBetween: 30 },
                                1200: { slidesPerView: 4, spaceBetween: 30 },
                            }}
                            modules={[Pagination]}
                            pagination={{
                                clickable: true,
                                el: ".spd4",
                            }}
                        >
                            {products.map((variants, i) => (
                                <SwiperSlide key={i} className="swiper-slide">
                                    <ProductCard1 variant={variants} />
                                </SwiperSlide>
                            ))}

                            <div className="sw-pagination-latest spd4  sw-dots type-circle justify-content-center" />
                        </Swiper>
                    </div>
                    <div className="tab-pane" id="recentlyViewed" role="tabpanel">
                        <Swiper
                            className="swiper tf-sw-latest"
                            dir="ltr"
                            spaceBetween={15}
                            breakpoints={{
                                0: { slidesPerView: 2, spaceBetween: 15 },

                                768: { slidesPerView: 3, spaceBetween: 30 },
                                1200: { slidesPerView: 4, spaceBetween: 30 },
                            }}
                            modules={[Pagination]}
                            pagination={{
                                clickable: true,
                                el: ".spd5",
                            }}
                        >
                            {products.slice(4).map((variant, i) => (
                                <SwiperSlide key={i} className="swiper-slide">
                                    <ProductCard1 variant={variant} />
                                </SwiperSlide>
                            ))}

                            <div className="sw-pagination-latest spd5  sw-dots type-circle justify-content-center" />
                        </Swiper>
                    </div>
                </div>
            </div>
        </section>
    );
}

const shimmer = `relative overflow-hidden before:absolute before:inset-0 before:-translate-x-full before:animate-[shimmer_1.5s_infinite] before:bg-gradient-to-r before:from-transparent before:via-white/10 before:to-transparent`;


function ProductSkeleton() {
    return (
        <div className="col-span-4 space-y-4 lg:col-span-1">
            <div className={`relative h-[167px] rounded-xl bg-gray-900 ${shimmer}`} />

            <div className="h-4 w-full rounded-lg bg-gray-900" />
            <div className="h-6 w-1/3 rounded-lg bg-gray-900" />
            <div className="h-4 w-full rounded-lg bg-gray-900" />
            <div className="h-4 w-4/6 rounded-lg bg-gray-900" />
        </div>
    );
}

export function RecommendedProductsSkeleton() {
    return (
        <section className="flat-spacing">
            <div className="container flat-animate-tab">
                <div className="space-y-6 pb-[5px]">
                    <div className="space-y-2">
                        <div className={`h-6 w-1/3 rounded-lg bg-gray-900 ${shimmer}`} />
                        <div className={`h-4 w-1/2 rounded-lg bg-gray-900 ${shimmer}`} />
                    </div>

                    <div className="grid grid-cols-4 gap-6">
                        <ProductSkeleton />
                        <ProductSkeleton />
                        <ProductSkeleton />
                        <ProductSkeleton />
                    </div>
                </div>
            </div>
        </section>
    );
}
