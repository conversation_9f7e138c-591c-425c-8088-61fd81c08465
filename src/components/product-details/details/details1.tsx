import React, { useState } from "react";
import { library } from '@fortawesome/fontawesome-svg-core'
import { faStar as faStarRegular } from "@fortawesome/free-regular-svg-icons";
import Slider1 from "../sliders/slider1";
import ColorSelect from "../color-select";
import SizeSelect from "../size-select";
import QuantitySelect from "../quantity-select";
import { Product, ProductVariant } from "../../../types/product/product";
import { useContextElement } from "../../../layout/context";
import { filterProductsByColor } from "../../../utils/product-utils";

library.add(faStarRegular);
// import ProductStikyBottom from "../ProductStikyBottom";

export default function Details1({ product, activeVarient }: { product: Product, activeVarient: ProductVariant }) {
  const [quantity, setQuantity] = useState(1);
  const { isAddedtoWishlist, updateWishlist } = useContextElement();


  const {
    addToCart,
    isAddedToCartProducts,
    cartDetails,
    // addToWishlist,
    // isAddedtoWishlist,
    isAddedtoCompareItem,
    addToCompareItem,
    // cartProducts,
    // updateQuantity,
  } = useContextElement();

  return (
    <section className="flat-spacing">
      <div className="tf-main-product section-image-zoom">
        <div className="container">
          <div className="row">
            {/* Product default */}
            <div className="col-md-6">
              <div className="tf-product-media-wrap sticky-top">
                <Slider1
                  slideImages={activeVarient?.images}
                  sliderVideos={activeVarient?.product_video_url}
                />
              </div>
            </div>
            {/* /Product default */}
            {/* tf-product-info-list */}
            <div className="col-md-6">
              <div className="tf-product-info-wrap position-relative mw-100p-hidden ">
                <div className="tf-zoom-main" />
                <div className="tf-product-info-list other-image-zoom">
                  <div className="tf-product-info-heading">
                    <div className="tf-product-info-name">
                      <div className="text text-btn-uppercase">{product.category}</div>
                      <h3 className="name">{product.name}</h3>
                      <div className="sub">
                        <div className="tf-product-info-rate">
                          <div className="list-star">
                            {Array.from({ length: product.rating ?? 0 }, (_, index) => (
                              <span key={index} className="icon icon-star" />
                            ))}
                            {Array.from({ length: 5 - (product.rating ?? 0) }, (_, index) => (
                              <span key={index} className="icon icon-star disabled" />
                            ))}

                          </div>

                          <div className="text text-caption-1">
                            {"(" + product.number_of_rating + " reviews)"}
                          </div>
                        </div>
                        {/* recent purchase count missing */}
                        <div className="tf-product-info-sold">
                          <i className="icon icon-lightning" />
                          <div className="text text-caption-1">
                            {Math.floor(Math.random() * (activeVarient.stock || 50)) + 1}&nbsp;sold in last&nbsp;{Math.floor(Math.random() * 48) + 1}&nbsp;hours
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="tf-product-info-desc">
                      <div className="tf-product-info-price">
                        <h5 className="price-on-sale font-2">
                          {" "}
                          ₹{activeVarient?.selling_price.toFixed(2)}
                        </h5>
                        {activeVarient?.mrp && activeVarient.mrp > activeVarient?.selling_price ? (
                          <>
                            <div className="compare-at-price font-2">
                              {" "}
                              ₹{activeVarient.mrp.toFixed(2)}
                            </div>
                            <div className="badges-on-sale text-btn-uppercase">-{((activeVarient.mrp - activeVarient.selling_price) / activeVarient.mrp * 100).toFixed(0)}%</div>
                          </>
                        ) : (
                          ""
                        )}
                      </div>
                      <p>{product.short_description}</p>
                      {/* <div className="tf-product-info-liveview">
                        <i className="icon icon-eye" />
                        <p className="text-caption-1">
                          <span className="liveview-count">28</span> people are
                          viewing this right now
                        </p>
                      </div> */}
                    </div>
                  </div>
                  <div className="tf-product-info-choose-option">
                    {product.option_keys.includes("color") && <ColorSelect
                      variant={activeVarient}
                      colorOptions={product.variants_minimal.map((variant) => ({ id: variant.slug, value: variant.options.color, image: variant.images.length > 0 ? variant.images[0].image_url : "" }))}
                    />}
                    {filterProductsByColor(product.variants_minimal, activeVarient).map((options, index) => (
                      options.length > 0 && <SizeSelect key={index} title={options[0].key} variant={activeVarient} options={options} />
                    ))}
                    <div className="tf-product-info-quantity">
                      <div className="title mb_12">Quantity:</div>
                      <QuantitySelect
                        quantity={
                          isAddedToCartProducts(activeVarient.id)
                            ? cartDetails?.order_summary.items.filter(
                              (elm) => elm.id == activeVarient.id
                            )[0].quantity
                            :
                            quantity
                        }
                        setQuantity={(qty: number) => {
                          if (isAddedToCartProducts(activeVarient.id)) {
                            addToCart(activeVarient.id, qty);
                          } else {
                            setQuantity(qty);
                          }
                        }}
                      />
                    </div>
                    <div>
                      <div className="tf-product-info-by-btn mb_10">
                        {isAddedToCartProducts(activeVarient.id) && <a href="/cart"
                          className="btn-style-2 flex-grow-1 text-btn-uppercase fw-6 btn-add-to-cart"
                        >
                          <span>
                            Go to cart
                          </span>
                        </a>}
                        {!isAddedToCartProducts(activeVarient.id) && <a
                          onClick={() => addToCart(activeVarient.id, quantity)}
                          className="btn-style-2 flex-grow-1 text-btn-uppercase fw-6 btn-add-to-cart"
                        >
                          <span>
                            Add to cart
                          </span>

                        </a>}
                        <a
                          href="#compare"
                          data-bs-toggle="offcanvas"
                          aria-controls="compare"
                          onClick={() => addToCompareItem(activeVarient)}
                          className="box-icon hover-tooltip compare btn-icon-action"
                        >
                          <span className="icon icon-gitDiff" />
                          <span className="tooltip text-caption-2">
                            {isAddedtoCompareItem(activeVarient.id)
                              ? "Already compared"
                              : "Compare"}
                          </span>
                        </a>
                        <a
                          onClick={() => updateWishlist(activeVarient)}
                          className="box-icon hover-tooltip text-caption-2 wishlist btn-icon-action"
                        >
                          <span className="icon icon-heart" />
                          <span className="tooltip text-caption-2">
                            {isAddedtoWishlist(activeVarient.id)
                              ? "Already Wishlished"
                              : "Wishlist"}
                          </span>
                        </a>
                      </div>
                      <a href={`/checkout?id=${activeVarient.slug}`} className="btn-style-3 text-btn-uppercase">
                        Buy it now
                      </a>
                    </div>

                    <div className="tf-product-info-help">
                      <div className="tf-product-info-extra-link">
                        {/* Return policy missing */}
                        {/* <a
                          href="#delivery_return"
                          data-bs-toggle="modal"
                          className="tf-product-extra-icon"
                        >
                          <div className="icon">
                            <i className="icon-shipping" />
                          </div>
                          <p className="text-caption-1">
                            Delivery &amp; Return
                          </p>
                        </a> */}
                        {/* Ask a question missing */}
                        {/* <a
                          href="#ask_question"
                          data-bs-toggle="modal"
                          className="tf-product-extra-icon"
                        >
                          <div className="icon">
                            <i className="icon-question" />
                          </div>
                          <p className="text-caption-1">Ask A Question</p>
                        </a> */}
                        {/* Share missing */}
                        {/* <a
                          href="#share_social"
                          data-bs-toggle="modal"
                          className="tf-product-extra-icon"
                        >
                          <div className="icon">
                            <i className="icon-share" />
                          </div>
                          <p className="text-caption-1">Share</p>
                        </a> */}
                      </div>
                      <div className="tf-product-info-time">
                        <div className="icon">
                          <i className="icon-timer" />
                        </div>
                        {/* Estimated Delivery missing */}
                        <p className="text-caption-1">
                          Estimated Delivery:&nbsp;&nbsp;<span>12-26 days</span>
                          (International), <span>3-6 days</span> (United States)
                        </p>
                      </div>
                      <div className="tf-product-info-return">
                        <div className="icon">
                          <i className="icon-arrowClockwise" />
                        </div>
                        {/* Return Policy missing */}
                        {activeVarient?.policy?.return && <p className="text-caption-1">
                          Return within <span>{activeVarient.policy?.return}</span> of purchase. Duties
                          &amp; taxes are non-refundable.
                        </p>}
                        {!activeVarient.policy?.return && <p className="text-caption-1">
                          No refund policies available.
                        </p>}
                      </div>
                      <div className="dropdown dropdown-store-location">
                        <div
                          className="dropdown-title dropdown-backdrop"
                          data-bs-toggle="dropdown"
                          aria-haspopup="true"
                        >
                          <div className="tf-product-info-view link">
                            <div className="icon">
                              <i className="icon-map-pin" />
                            </div>
                            <span>View Store Information</span>
                          </div>
                        </div>
                        <div className="dropdown-menu dropdown-menu-end">
                          <div className="dropdown-content">
                            <div className="dropdown-content-heading">
                              <h5>Store Location</h5>
                              <i className="icon icon-close" />
                            </div>
                            <div className="line-bt" />
                            <div>
                              <h6>{product.seller}</h6>
                            </div>
                            <div>
                              {product.seller_address}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <ul className="tf-product-info-sku">

                      <li>
                        <p className="text-caption-1">Vendor:</p>
                        <p className="text-caption-1 text-1">{product.seller}</p>
                      </li>
                      <li>
                        <p className="text-caption-1">Available:</p>
                        <p className="text-caption-1 text-1">{activeVarient.stock > 0 ? "In stock" : "Out of stock"}</p>
                      </li>
                      <li>
                        <p className="text-caption-1">Tags:</p>
                        <p className="text-caption-1">
                          {activeVarient.tags?.map((tag, index) => (
                            <React.Fragment key={index}>
                              <a href={"#/tags/" + tag} className="text-1 link">
                                {tag}
                              </a>
                              {index < (activeVarient?.tags?.length ?? 0) - 1 && ", "}
                            </React.Fragment>
                          ))}
                        </p>
                      </li>
                    </ul>
                    {import.meta.env.VITE_PAYMENT_ENABLE_ONLINE_PAYMENT && <div className="tf-product-info-guranteed">
                      <div className="text-title">Guranteed safe checkout:</div>
                      <div className="tf-payment">
                        <a href="#">
                          <img
                            alt=""
                            src="/images/payment/img-1.png"
                            width={100}
                            height={64}
                          />
                        </a>
                        <a href="#">
                          <img
                            alt=""
                            src="/images/payment/img-2.png"
                            width={100}
                            height={64}
                          />
                        </a>
                        <a href="#">
                          <img
                            alt=""
                            src="/images/payment/img-3.png"
                            width={100}
                            height={64}
                          />
                        </a>
                        <a href="#">
                          <img
                            alt=""
                            src="/images/payment/img-4.png"
                            width={98}
                            height={64}
                          />
                        </a>
                        <a href="#">
                          <img
                            alt=""
                            src="/images/payment/img-5.png"
                            width={102}
                            height={64}
                          />
                        </a>
                        <a href="#">
                          <img
                            alt=""
                            src="/images/payment/img-6.png"
                            width={98}
                            height={64}
                          />
                        </a>
                      </div>
                    </div>}
                  </div>
                </div>
              </div>
            </div>
            {/* /tf-product-info-list */}
          </div>
        </div>
      </div>
      {/* <ProductStikyBottom /> */}
    </section >
  );
}
