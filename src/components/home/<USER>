import CountdownTimer from "@/components/common/countdown";
import { BannerCountdown } from "@/types/product/banner-collection";
import { Link } from "react-router-dom";
import { Image } from "../common/image";

export default function BannerCountdownView({ banner }: { banner: BannerCountdown }) {
    return (
        <section className="bg-surface flat-spacing flat-countdown-banner">
            <div className="container">
                <div className="row align-items-center">
                    <div className="col-lg-5">
                        <div className="banner-left">
                            <div className="box-title">
                                <h3 className="wow fadeInUp">{banner.title}</h3>
                                <p className="text-secondary wow fadeInUp">
                                    {banner.sub_title}
                                </p>
                            </div>
                            <div className="btn-banner wow fadeInUp">
                                <Link to={banner.cta_url} className="tf-btn btn-fill">
                                    <span className="text">{banner.action_text}</span>
                                    <i className="icon icon-arrowUpRight" />
                                </Link>
                            </div>
                        </div>
                    </div>
                    <div className="col-lg-2">
                        <div className="banner-img">
                            <Image
                                className="lazyload"
                                blurhash={banner.blur_hash}
                                data-src={banner.img_src}
                                loading="lazy"
                                alt={banner.title}
                                src={banner.img_src}
                                width={banner.width}
                                height={banner.height}
                                decoding="async"
                            />
                        </div>
                    </div>
                    <div className="col-lg-5">
                        <div className="banner-right">
                            <div className="tf-countdown-lg">
                                <div
                                    className="js-countdown"
                                    data-timer={1007500}
                                    data-labels="Days,Hours,Mins,Secs"
                                >
                                    <CountdownTimer style={2} targetDate={banner.ends_on} />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    );
}
