import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay, EffectFade, Pagination } from "swiper/modules";
import { Slide } from "@/types/product/slide";
import { Image, Placeholder } from "../common/image";

export default function Hero({ slides }: { slides: Slide[] }) {

    return (
        <section className="tf-slideshow slider-default slider-effect-fade">
            <Swiper
                effect="fade"
                spaceBetween={0}
                centeredSlides={false}
                slidesPerView={1}
                loop={true}
                modules={[EffectFade, Autoplay, Pagination]}
                autoplay={{ delay: 3000 }}
                dir="ltr"
                pagination={{
                    clickable: true,
                    el: ".spd55",
                }}
            >
                {slides?.map((slide, index) => (
                    <SwiperSlide key={index}>
                        <div className="wrap-slider">
                            <Image
                                blurhash={slide.blurhash}
                                alt={slide.heading}
                                src={slide.img_src}
                                width={slide.width}
                                height={slide.height}
                            // priority
                            />
                            <div className="box-content">
                                <div className="content-slider">
                                    <div className="box-title-slider">
                                        <p className="fade-item fade-item-1 subheading text-btn-uppercase text-white">
                                            {slide.subheading}
                                        </p>
                                        <div className="fade-item fade-item-2 heading text-white title-display">
                                            {slide.heading.split("\n").map((line, idx) => (
                                                <span key={idx}>
                                                    {line}
                                                    <br />
                                                </span>
                                            ))}
                                        </div>
                                    </div>
                                    <div className="fade-item fade-item-3 box-btn-slider">
                                        <a
                                            href={`${slide.redirect_url}`}
                                            className="tf-btn btn-fill btn-white"
                                        >
                                            <span className="text">{slide.btn_text}</span>
                                            <i className="icon icon-arrowUpRight" />
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </SwiperSlide>
                ))}
            </Swiper>
            <div className="wrap-pagination">
                <div className="container">
                    <div className="sw-dots sw-pagination-slider type-circle white-circle justify-content-center spd55" />
                </div>
            </div>
        </section>
    );
}

export function HeroLoading() {
    return (<Placeholder width={1920} height={803} />)
}
