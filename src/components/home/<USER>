// import { HomeFakeRepositoryImpl } from "@/repositories/home/<USER>";
import { BannerConfig } from "@/types/home";
// import { BannerCollection } from "@/types/product/banner-collection";
// import wrapPromise from "@/utils/wrap-promise";
// import { Link } from "react-router-dom";
import { Placeholder } from "../common/image";
import { Shimmer } from "react-shimmer";

// let collectionData: { read: () => BannerCollection[] | undefined }

export default function BannerCollections({ config }: { config: BannerConfig }) {

    console.log(config);


    // if (!collectionData) {
    //     collectionData = wrapPromise((new HomeFakeRepositoryImpl().fetchHomeData(config.endPoint)))
    // }

    // const data:BannerCollection[] | undefined = undefined

    return (
        <section className="flat-spacing pt-0">
            <div className="container">
                <div className="tf-grid-layout md-col-2">
                    {/* {data?.map((banner, index) => (
                        <div key={index} className={`${banner.defaultStyle ? "collection-default" : "collection-position"} hover-img`}>
                            <a className="img-style">
                                <Image
                                    className="lazyload"
                                    blurhash={banner.blurHash}
                                    data-src={banner.imgSrc}
                                    alt={banner.title}
                                    src={banner.imgSrc}
                                    width={banner.width}
                                    height={banner.height}
                                />
                            </a>
                            <div className="content">
                                <h3 className={`title ${banner.defaultStyle ? "" : "wow fadeInUp"}`}>
                                    <Link to={banner.titleRedirectUrl} className={`link ${banner.defaultStyle ? "" : "text-white wow fadeInUp"}`}>
                                        {banner.title}
                                    </Link>
                                </h3>
                                <p className={`desc ${banner.defaultStyle ? "" : "text-white"} wow fadeInUp`}>
                                    {banner.description}
                                </p>
                                <div className="wow fadeInUp">
                                    <Link
                                        to={banner.actionUrl}
                                        className={`btn-line ${banner.defaultStyle ? "" : "style-white"}`}
                                    >
                                        {banner.action}
                                    </Link>
                                </div>
                            </div>
                        </div>
                    ))} */}
                </div>
            </div>
        </section>
    );
}

export function BannerCollectionLoader() {
    return (
        <section className="flat-spacing pt-0">
            <div className="container">
                <div className="tf-grid-layout md-col-2">
                    <div className="collection-default hover-img">
                        <a className="img-style">
                            <Placeholder
                                width={945}
                                height={709}
                            />
                        </a>
                        <div className="content">
                            <h3 className="title wow fadeInUp">
                                <Shimmer height={48} width={300} />
                            </h3>
                            <div className="desc wow fadeInUp">
                                <Shimmer height={26} width={600} />
                            </div>
                            <div className="wow fadeInUp">
                                <Shimmer height={30} width={100} />
                            </div>
                        </div>
                    </div>
                    <div className="collection-position hover-img">
                        <a className="img-style">
                            <Placeholder
                                width={945}
                                height={945}
                            />
                        </a>
                    </div>
                </div>
            </div>
        </section>
    )
}
