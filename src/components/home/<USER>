import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation, Pagination } from "swiper/modules";
import { Link } from "react-router-dom";
import { Collection } from "@/types/product/collection";
import { Image, Placeholder } from "../common/image";
import { Shimmer } from "react-shimmer";

export default function Collections({ uri, collections }: { uri?: string, collections: Collection[] }) {
    return (
        <section className="flat-spacing-2 pb_0">
            <div className="container">
                <div className="heading-section-2 wow fadeInUp">
                    <h3>Categories you might like</h3>
                    <a href="/categories" className="btn-line">
                        View All Collection
                    </a>
                </div>
                <div
                    className="flat-collection-circle wow fadeInUp"
                    data-wow-delay="0.1s"
                >
                    <Swiper
                        dir="ltr"
                        slidesPerView={5}
                        spaceBetween={20}
                        breakpoints={{
                            1200: { slidesPerView: 5, spaceBetween: 20 },
                            1000: { slidesPerView: 4, spaceBetween: 20 },
                            768: { slidesPerView: 3, spaceBetween: 20 },
                            480: { slidesPerView: 2, spaceBetween: 15 },
                            0: { slidesPerView: 2, spaceBetween: 15 },
                        }}
                        modules={[Pagination, Navigation]}
                        pagination={{
                            clickable: true,
                            el: ".spd54",
                        }}
                        navigation={{
                            prevEl: ".snbp12",
                            nextEl: ".snbn12",
                        }}
                    >
                        {collections?.map((collection, index) => (
                            <SwiperSlide key={index}>
                                <div className="collection-circle hover-img" >
                                    <a href={`${uri ?? ""}/${collection.slug}`} className="img-style" style={{ aspectRatio: 1 }}>

                                        <Image
                                            className="lazyload"
                                            blurhash={collection.blurHash}
                                            data-src={collection.image_url}
                                            alt={collection.name}
                                            src={collection.image_url}
                                            height={363}
                                            width={363}
                                        />
                                    </a>
                                    <div className="collection-content text-center">
                                        <div>
                                            <Link to={`${uri ?? ""}/${collection.slug}`} className="cls-title">
                                                <h6 className="text">{collection.name}</h6>
                                                <i className="icon icon-arrowUpRight" />
                                            </Link>
                                        </div>
                                        {/* <div className="count text-secondary">
                      {collection.count}
                    </div> */}
                                    </div>
                                </div>
                            </SwiperSlide>
                        ))}
                    </Swiper>
                    <div className="d-flex d-lg-none sw-pagination-collection sw-dots type-circle justify-content-center spd54" />
                    <div className="nav-prev-collection d-none d-lg-flex nav-sw style-line nav-sw-left snbp12">
                        <i className="icon icon-arrLeft" />
                    </div>
                    <div className="nav-next-collection d-none d-lg-flex nav-sw style-line nav-sw-right snbn12">
                        <i className="icon icon-arrRight" />
                    </div>
                </div>
            </div>
        </section>
    );
}

export function CollectionsLoading() {
    return (
        <section className="flat-spacing-2 pb_0">
            <div className="container">
                <div className="heading-section-2 wow fadeInUp">
                    <h3><Shimmer height={48} width={500} /></h3>
                    <div >
                        <Shimmer height={30} width={150} />
                    </div>
                </div>
                <div
                    className="flat-collection-circle wow fadeInUp"
                    data-wow-delay="0.1s"
                >
                    <Swiper
                        dir="ltr"
                        slidesPerView={5}
                        spaceBetween={20}
                        breakpoints={{
                            1200: { slidesPerView: 5, spaceBetween: 20 },
                            1000: { slidesPerView: 4, spaceBetween: 20 },
                            768: { slidesPerView: 3, spaceBetween: 20 },
                            480: { slidesPerView: 2, spaceBetween: 15 },
                            0: { slidesPerView: 2, spaceBetween: 15 },
                        }}
                        modules={[Pagination, Navigation]}
                        pagination={{
                            clickable: true,
                            el: ".spd54",
                        }}
                        navigation={{
                            prevEl: ".snbp12",
                            nextEl: ".snbn12",
                        }}
                    >
                        {[...Array(5)]?.map((_, index) => (
                            <SwiperSlide key={index}>
                                <div className="collection-circle hover-img">
                                    <div className="img-style">
                                        <Placeholder
                                            width={363}
                                            height={363} />
                                    </div>
                                    <div className="collection-content text-center">
                                        <div>
                                            <Link to={`/shop-collection`} className="cls-title">
                                                <h6 className="text"><Shimmer height={20} width={150} /></h6>
                                                <i className="icon icon-arrowUpRight" />
                                            </Link>
                                        </div>
                                    </div>
                                </div>
                            </SwiperSlide>
                        ))}
                    </Swiper>
                </div>
            </div>
        </section>
    );
}
