import { DotLottieReact } from '@lottiefiles/dotlottie-react';

interface SuccessAlertProps {
    orderNumber: string;
    onViewOrder: () => void;
}

export const SuccessAlert = ({ orderNumber, onViewOrder }: SuccessAlertProps) => {
    return (
        <div className="tf-alert-success">
            <div className="alert-content text-center p-4">
                <div className="success-icon mb-3">
                    <div style={{ height: '150px', margin: '0 auto' }}>
                        <DotLottieReact
                            key={orderNumber}
                            src="/images/lottie/success.lottie"
                            loop={false}
                            autoplay={true}
                            style={{ width: '100%', height: 'auto' }}
                        />
                    </div>
                </div>
                <h4 className="alert-title mb-3">Order Placed Successfully!</h4>
                <p className="alert-message mb-4">
                    Your order #{orderNumber} has been successfully placed. Thank you for shopping with us!
                </p>
                <div className="alert-actions">
                    <button
                        className="tf-btn-remove"
                        data-bs-dismiss="modal"
                        style={{ padding: '8px 20px' }}
                        onClick={onViewOrder}
                    >
                        View Order Details
                    </button>
                    <a
                        href={`/products`}
                        className="link text-btn-uppercase text-center"
                        style={{ marginLeft: '10px' }}
                    >
                        Continue Shopping
                    </a>
                </div>
            </div>
        </div>
    );
};