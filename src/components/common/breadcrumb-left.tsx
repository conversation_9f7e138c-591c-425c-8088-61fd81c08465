import React from "react"

interface BreadcrumbProps {
    title: string,
    desc?: string,
    pages?: {
        name: string,
        link: string
    }[]
}

export default function Breadcrumb({ title, desc, pages }: BreadcrumbProps) {

    return (
        <div className="breadcrumbs-default pb-0">
            <div className="container">
                <div className="breadcrumbs-content">
                    <ul className="breadcrumbs d-flex align-items-center">
                        <li>
                            <a className="link" href={`/`}>
                                Home
                            </a>
                        </li>
                        {pages && pages.map((page, index) => (
                            <React.Fragment key={index}>
                                <li>
                                    <i className="icon-arrRight" />
                                </li>
                                <li>
                                    <a className="link" href={page.link}>
                                        {page.name}
                                    </a>
                                </li>
                            </React.Fragment>
                        ))}

                        <li>
                            <i className="icon-arrRight" />
                        </li>
                        <li>{title}</li>

                    </ul>
                    <div className="content-bottom">
                        <h5>{title}</h5>
                        {desc && <p className="text-secondary">
                            {desc}
                        </p>}
                    </div>
                </div>
            </div>
        </div>
    )
}