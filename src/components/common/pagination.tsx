"use client";
import { useSearchParams } from "react-router-dom";

export default function Pagination({ totalPages = 3 }) {
    const [searchParams, setSearchParams] = useSearchParams();
    const currentPage = parseInt(searchParams.get("page") || "1", 10);

    const handlePageClick = (page: number) => {
        if (page >= 1 && page <= totalPages) {
            setSearchParams({ page: page.toString() })
            // setCurrentPage(page);
        }
    };

    const renderPageNumbers = () => {
        if (totalPages <= 7) {
            return Array.from({ length: totalPages }, (_, index) => {
                const page = index + 1;
                return renderPageItem(page);
            });
        }

        const pageNumbers = [];

        // Always show first page
        pageNumbers.push(1);

        if (currentPage <= 3) {
            // Show first few pages
            for (let i = 2; i <= Math.min(4, totalPages - 1); i++) {
                pageNumbers.push(i);
            }
            pageNumbers.push('...');
        } else if (currentPage >= totalPages - 2) {
            // Show last few pages
            pageNumbers.push('...');
            for (let i = totalPages - 3; i < totalPages; i++) {
                pageNumbers.push(i);
            }
        } else {
            // Show current page and neighbors
            pageNumbers.push('...');
            pageNumbers.push(currentPage - 1);
            pageNumbers.push(currentPage);
            pageNumbers.push(currentPage + 1);
            pageNumbers.push('...');
        }

        // Always show last page
        pageNumbers.push(totalPages);

        return pageNumbers.map((item, index) => {
            if (item === '...') {
                return (
                    <li key={`ellipsis-${index}`} className="pagination-ellipsis">
                        <div className="pagination-item text-button">...</div>
                    </li>
                );
            }
            return renderPageItem(item as number);
        });
    };

    const renderPageItem = (page: number) => (
        <li
            key={page}
            className={page === currentPage ? "active" : ""}
            onClick={() => handlePageClick(page)}
        >
            <div className="pagination-item text-button">{page}</div>
        </li>
    );

    return (
        <>
            <li onClick={() => handlePageClick(currentPage - 1)}>
                <a
                    className={`pagination-item text-button ${currentPage === 1 ? "disabled" : ""
                        }`}
                >
                    <i className="icon-arrLeft" />
                </a>
            </li>
            {renderPageNumbers()}
            <li onClick={() => handlePageClick(currentPage + 1)}>
                <a
                    className={`pagination-item text-button ${currentPage === totalPages ? "disabled" : ""
                        }`}
                >
                    <i className="icon-arrRight" />
                </a>
            </li>
        </>
    );
}
