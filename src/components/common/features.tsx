import { PolicyItem } from "@/types/home";
import { Pagination } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";

export default function Features({ parentClass = "flat-spacing", config }: { parentClass: string, config: PolicyItem[] }) {
    return (
        <section className={parentClass}>
            <div className="container">
                <Swiper
                    dir="ltr"
                    className="swiper tf-sw-iconbox"
                    spaceBetween={15}
                    breakpoints={{
                        1200: { slidesPerView: 4 },
                        768: { slidesPerView: 3 },
                        576: { slidesPerView: 2 },
                        0: { slidesPerView: 1 },
                    }}
                    modules={[Pagination]}
                    pagination={{
                        clickable: true,
                        el: ".spd2",
                    }}
                >
                    {config.map((item) => (
                        <SwiperSlide key={item.id}>
                            <div className="tf-icon-box">
                                <div className="icon-box">
                                    <span className={`icon ${item.icon}`} />
                                </div>
                                <div className="content text-center">
                                    <h6>{item.title}</h6>
                                    <p className="text-secondary">{item.description}</p>
                                </div>
                            </div>
                        </SwiperSlide>
                    ))}
                    <div className="sw-pagination-iconbox spd2 sw-dots type-circle justify-content-center" />
                </Swiper>
            </div>
        </section>
    );
}
