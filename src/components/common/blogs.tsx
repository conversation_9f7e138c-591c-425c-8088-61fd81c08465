import { Swiper, SwiperSlide } from "swiper/react";
import { Pagination } from "swiper/modules";
// import { Link } from "react-router-dom";
// import { BlogThumbnail } from "@/types/blog/blog-thumbnail";
// import wrapPromise from "@/utils/wrap-promise";
// import { HomeRepositoryImpl } from "@/repositories/home/<USER>";
import { BlogConfig } from "@/types/home";
import { Placeholder } from "./image";
import { Shimmer } from "react-shimmer";

// let blogData: { read: () => BlogThumbnail[] | undefined }

export default function Blogs({ parentClass = "flat-spacing pt-0", config }: { config: BlogConfig, parentClass: string }) {

    // if (!blogData) {
    //     blogData = wrapPromise((new HomeRepositoryImpl()).fetchHomeData(config.endPoint))
    // }

    // const data:BlogThumbnail[] | undefined = undefined
    //blogData.read()

    return (
        <section className={parentClass}>
            <div className="container">
                <div className="heading-section text-center">
                    <h3 className="heading wow fadeInUp">{config.title}</h3>
                    <p className="subheading text-secondary wow fadeInUp">
                        {config.subTitle}
                    </p>
                </div>
                <Swiper
                    breakpoints={{
                        0: {
                            slidesPerView: 1,
                            spaceBetween: 15,
                            pagination: { clickable: true },
                        },
                        768: {
                            slidesPerView: 2,
                            spaceBetween: 30,
                            pagination: { clickable: true },
                        },
                        1024: {
                            slidesPerView: 3,
                            spaceBetween: 30,
                            pagination: { clickable: true },
                        },
                    }}
                    dir="ltr"
                    className="swiper tf-sw-recent"
                    modules={[Pagination]}
                    pagination={{
                        clickable: true,
                        el: ".spd1",
                    }}
                >
                    {/* {data?.map((post, index) => (
                        <SwiperSlide key={index} className="swiper-slide">
                            <div
                                className="wg-blog style-1 hover-image wow fadeInUp"
                                data-wow-delay={post.delay}
                            >
                                <div className="image">
                                    <Image
                                        className="lazyload"
                                        blurhash={post.blurHash}
                                        data-src={post.imgSrc}
                                        alt={post.title}
                                        src={post.imgSrc}
                                        width={post.imgWidth}
                                        height={post.imgHeight}
                                    />
                                </div>
                                <div className="content">
                                    <p className="text-btn-uppercase text-secondary-2">
                                        {post.date}
                                    </p>
                                    <div>
                                        <h6 className="title fw-5">
                                            <Link className="link" to={`/blog-detail/${post.id}`}>
                                                {post.title}
                                            </Link>
                                        </h6>
                                        <div className="body-text">{post.description}</div>
                                    </div>
                                </div>
                            </div>
                        </SwiperSlide>
                    ))} */}

                    <div className="sw-pagination-recent spd1 sw-dots type-circle justify-content-center" />
                </Swiper>
            </div>
        </section>
    );
}

export function BlogsLoader({ parentClass = "flat-spacing pt-0" }) {
    return (
        <section className={parentClass}>
            <div className="container">
                <div className="heading-section text-center">
                    <h3 className="tab-product justify-content-sm-center">
                        <Shimmer height={48} width={300} />
                    </h3>
                    <div className="tab-product justify-content-sm-center">
                        <Shimmer height={26} width={800} />
                    </div>
                </div>
                <Swiper
                    breakpoints={{
                        0: {
                            slidesPerView: 1,
                            spaceBetween: 15,
                            pagination: { clickable: true },
                        },
                        768: {
                            slidesPerView: 2,
                            spaceBetween: 30,
                            pagination: { clickable: true },
                        },
                        1024: {
                            slidesPerView: 3,
                            spaceBetween: 30,
                            pagination: { clickable: true },
                        },
                    }}
                    dir="ltr"
                    className="swiper tf-sw-recent"
                    modules={[Pagination]}
                    pagination={{
                        clickable: true,
                        el: ".spd1",
                    }}
                >
                    {[...Array(3)]?.map((_, index) => (
                        <SwiperSlide key={index} className="swiper-slide">
                            <div
                                className="wg-blog style-1 hover-image wow fadeInUp"
                                data-wow-delay={0}
                            >
                                <div className="image">
                                    <Placeholder
                                        width={615}
                                        height={461}
                                    />
                                </div>
                                <div className="content">
                                    <div className="text-btn-uppercase text-secondary-2">
                                        <Shimmer height={20} width={150} />
                                    </div>
                                    <div>
                                        <h6 className="title fw-5">
                                            <div style={{ marginBottom: 2 }}>
                                                <Shimmer height={27} width={400} />
                                            </div>
                                            <Shimmer height={27} width={200} />

                                        </h6>
                                        <div className="body-text"><Shimmer height={52} width={410} /></div>
                                    </div>
                                </div>
                            </div>
                        </SwiperSlide>
                    ))}

                </Swiper>
            </div>
        </section>
    )
}
