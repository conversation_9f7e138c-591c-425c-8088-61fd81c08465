import { UseQueryResult } from "@tanstack/react-query";
import { ReactNode } from "react";

interface QueryWrapperProps<T> {
    query: UseQueryResult<T>;
    loader?: ReactNode;
    error?: ReactNode;
    children: (data: T) => ReactNode;
}

export default function QueryWrapper<T>({
    query,
    loader = <Loader />,
    error,
    children,
}: QueryWrapperProps<T>) {

    if (query.isLoading) return loader;
    if (query.isError) return error;
    if (!query.data) return null;

    return <>{children(query.data)}</>
}

export function Loader() {
    return (
        <div className="loader-overlay">
            <div className="spinner-border text-primary" style={{ width: '3rem', height: '3rem' }} role="status">
                <span className="visually-hidden">Loading...</span>
            </div>
        </div>
    )
}