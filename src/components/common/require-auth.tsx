import { useContextElement } from "@/layout/context";
import { Navigate, useLocation } from "react-router-dom";
import { Loader } from "./query-wrapper";

export function RequireAuth({ children }: { children: JSX.Element }) {
    const { isAuthenticated, authLoading } = useContextElement();
    const location = useLocation();

    if (authLoading) return <Loader />

    if (!isAuthenticated) {
        return <Navigate to="/login" state={{ from: location }} replace />;
    }

    return children;
}