
// import { decode } from 'blurhash';
import { ImgHTMLAttributes, useEffect, useState } from "react";
import { Shimmer } from 'react-shimmer';

type ImageProps = ImgHTMLAttributes<HTMLImageElement>

export interface Props extends ImageProps {
    blurhash?: string | undefined,
}

export interface AsyncImageProps extends Props {
    inView: boolean
}

export function Image({ ...props }: Props) {
    return (
        // <InView triggerOnce>
        //     {({ ref, inView }) => (
        //         <div ref={ref}>
        <AsyncImage inView={true} blurhash="LEHV6nWB2yk8pyo0adR*.7kCMdnj" {...props} />
        //         </div>
        //     )}
        // </InView>
    )
}

// function getImage(canvas: HTMLCanvasElement, blurhash?: string | undefined): Promise<string> {
//     return new Promise<string>(resolve => {
//         const ctx = canvas.getContext("2d");

//         const imageData = ctx!.createImageData(canvas.width, canvas.height);
//         const pixels = decode(blurhash ?? "", canvas.width, canvas.height)
//         imageData.data.set(pixels);
//         ctx!.putImageData(imageData, 0, 0);
//         resolve(canvas.toDataURL("image/png"))
//     })
// }

function AsyncImage({ ...props }: AsyncImageProps) {
    // const [isLoaded, setIsLoaded] = useState(false);
    const [isError, setIsError] = useState(false)

    // const [imageSrc, setImageSrc] = useState<string | undefined>(undefined);


    useEffect(() => {
        const canvas = document.createElement("canvas");
        canvas.width = props.width as number | undefined || 128; // Set a reasonable default size
        canvas.height = props.height as number | undefined || 128;
        const ctx = canvas.getContext("2d");

        if (ctx) {
            const loadImage = async () => {
                // const src = await getImage(canvas, props.blurhash ?? "");
                // setImageSrc(src);
            };

            // if (props.inView) {
            loadImage();
            // }

        }
    }, [props.blurhash, props.width, props.height]);

    return (
        <>
            {isError && <Placeholder
                className={props.className}
                alt={props.alt}
                crossOrigin={props.crossOrigin}
                loading={props.loading}
                referrerPolicy={props.referrerPolicy}
                sizes={props.sizes}
                srcSet={props.srcSet}
                useMap={props.useMap}
                decoding={props.decoding}
                width={props.width}
                height={props.height}
            />}
            {props.inView && !isError && <img
                className={props.className}
                // src={isLoaded ? props.src : imageSrc}
                src={props.src}
                alt={props.alt}
                crossOrigin={props.crossOrigin}
                loading={props.loading}
                referrerPolicy={props.referrerPolicy}
                sizes={props.sizes}
                srcSet={props.srcSet}
                useMap={props.useMap}
                decoding={props.decoding}
                width={props.width}
                height={props.height}
                // onLoad={() => setIsLoaded(true)}
                onError={() => setIsError(true)}
            />}
        </>
    )
}

export function Placeholder({ ...props }: Props) {
    // const [imageSrc, setImageSrc] = useState<string | undefined>(undefined);
    // useEffect(() => {
    //     const canvas = document.createElement("canvas");
    //     canvas.width = props.width as number | undefined || 128; // Set a reasonable default size
    //     canvas.height = props.height as number | undefined || 128;
    //     const ctx = canvas.getContext("2d");

    //     if (ctx) {
    //         const loadImage = async () => {
    //             const src = await getImage(canvas, props.blurhash ?? "L1Q]{A-;fQ-;~qfkfQfkfQfQfQfQ");
    //             setImageSrc(src);
    //         };

    //         // if (props.inView) {
    //         loadImage();
    //         // }

    //     }
    // }, [props.blurhash, props.width, props.height]);
    const height = props.height as number | undefined || 128;
    const width = props.width as number | undefined || 128;
    return (
        <Shimmer height={height} width={width} />
    )
    // return (
    //     <img
    //         className={props.className}
    //         src={imageSrc}
    //         alt={props.alt}
    //         crossOrigin={props.crossOrigin}
    //         loading={props.loading}
    //         referrerPolicy={props.referrerPolicy}
    //         sizes={props.sizes}
    //         srcSet={props.srcSet}
    //         useMap={props.useMap}
    //         decoding={props.decoding}
    //         width={props.width}
    //         height={props.height}
    //     />
    // )
}