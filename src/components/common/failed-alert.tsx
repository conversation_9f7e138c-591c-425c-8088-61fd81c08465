import { DotLottieReact } from '@lottiefiles/dotlottie-react';

interface FailedAlertProps {
    errorMessage: string;
    onClose?: () => void;
    onRetry?: () => void;
}

export const FailedAlert = ({ errorMessage, onClose, onRetry }: FailedAlertProps) => {
    return (
        <div className="tf-alert-success">
            <div className="alert-content text-center p-4">
                <div className="success-icon mb-3">
                    <div style={{ height: '150px', margin: '0 auto' }}>
                        <DotLottieReact
                            key={Math.random()}
                            src="/images/lottie/failed.lottie"
                            loop={false}
                            autoplay={true}
                            style={{ width: '100%', height: 'auto' }}
                        />
                    </div>
                </div>
                <h4 className="alert-title mb-3">Failed to place order!</h4>
                <p className="alert-message mb-4">
                    {errorMessage}
                </p>
                <div className="alert-actions">
                    {onRetry && (
                        <button
                            className="tf-btn-remove"
                            data-bs-dismiss="modal"
                            style={{ padding: '8px 40px' }}
                            onClick={onRetry}
                        >
                            Retry
                        </button>
                    )}

                    <button
                        className="tf-btn-remove"
                        data-bs-dismiss="modal"
                        style={{ padding: '8px 40px' }}
                        onClick={onClose}
                    >
                        Close
                    </button>

                </div>
            </div>
        </div>
    );
};