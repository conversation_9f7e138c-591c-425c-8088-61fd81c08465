import { Swiper, SwiperSlide } from "swiper/react";
import { Pagination } from "swiper/modules";
import { Link } from "react-router-dom";
import { ProductVariant } from "@/types/product/product";
import { Image, Placeholder } from "./image";
import { Shimmer } from "react-shimmer";

export default function ShopGram({ parentClass = "", products }: { parentClass: string, products: ProductVariant[] }) {
    if (products.length < 1) return null

    return (
        <section className={parentClass}>
            <div className="container">
                <div className="heading-section text-center">
                    <h3 className="heading wow fadeInUp">Shop Instagram</h3>
                    <p className="subheading text-secondary wow fadeInUp">
                        Elevate your wardrobe with fresh finds today!
                    </p>
                </div>
                <Swiper
                    dir="ltr"
                    className="swiper tf-sw-shop-gallery"
                    spaceBetween={10}
                    breakpoints={{
                        1200: { slidesPerView: 5 },
                        768: { slidesPerView: 3 },
                        0: { slidesPerView: 2 },
                    }}
                    modules={[Pagination]}
                    pagination={{
                        clickable: true,
                        el: ".spb222",
                    }}
                >
                    {products?.slice(0, 5).map((item, i) => (
                        <SwiperSlide key={i} style={{ aspectRatio: 3 / 4 }}>
                            <div
                                className="gallery-item hover-overlay hover-img wow fadeInUp"
                                data-wow-delay="0.1s"
                                style={{ height: "100%" }}
                            // {item.delay}
                            >
                                <div className="img-style" style={{ height: "100%" }}>
                                    <Image
                                        className="lazyload img-hover"
                                        data-src={item.images[0].image_url}
                                        alt={item.name}
                                        src={item.images[0].image_url}
                                        width={item.images[0].width}
                                        height={item.images[0].height}
                                    />
                                </div>
                                <Link
                                    to={`/product-details/${item.slug}`}
                                    className="box-icon hover-tooltip"
                                >
                                    <span className="icon icon-eye" />
                                    <span className="tooltip">View Product</span>
                                </Link>
                            </div>
                        </SwiperSlide>
                    ))}
                    <div className="sw-pagination-gallery sw-dots type-circle justify-content-center spb222"></div>
                </Swiper>
            </div>
        </section>
    );
}

export function ShopGramLoader({ parentClass = "" }) {
    return (
        <section className={parentClass}>
            <div className="container">
                <div className="heading-section text-center">
                    <h3 className="tab-product justify-content-sm-center">
                        <Shimmer height={48} width={500} />
                    </h3>
                    <div className="tab-product justify-content-sm-center">
                        <Shimmer height={26} width={600} />
                    </div>
                </div>
                <Swiper
                    dir="ltr"
                    className="swiper tf-sw-shop-gallery"
                    spaceBetween={10}
                    breakpoints={{
                        1200: { slidesPerView: 5 },
                        768: { slidesPerView: 3 },
                        0: { slidesPerView: 2 },
                    }}
                    modules={[Pagination]}
                    pagination={{
                        clickable: true,
                        el: ".spb222",
                    }}
                >
                    {[...Array(5)].map((_, i) => (
                        <SwiperSlide key={i}>
                            <div
                                className="gallery-item hover-overlay hover-img wow fadeInUp"
                                data-wow-delay="0.1s"
                            // {item.delay}
                            >
                                <div className="img-style">
                                    <Placeholder
                                        className="lazyload img-hover"
                                        width={600}
                                        height={800}
                                    />
                                </div>
                            </div>
                        </SwiperSlide>
                    ))}

                </Swiper>
            </div>
        </section>
    )
}
