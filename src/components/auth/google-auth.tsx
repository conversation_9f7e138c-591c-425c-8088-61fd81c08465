// import { AuthRepository } from "@/lib/repositories/auth/auth_respository";
import { GoogleLogin } from "@react-oauth/google";

export default function GoogleAuth() {
    return <GoogleLogin type="icon" shape="circle" ux_mode="popup" onSuccess={credRes => {
        console.log("loading");
        console.log(credRes.clientId);

        // (new AuthRepository()).googleSignIn(credRes.credential).then(() => {
        //     console.log("sucess");
        // })

    }} />
}