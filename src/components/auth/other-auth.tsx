import { useLogin } from "@/hooks/auth-quries";
import { useContextElement } from "@/layout/context";
import { JSX, lazy, Suspense } from "react";
import { useLocation, useNavigate } from "react-router-dom";

export default function OtherAuth() {
    const authMethods: JSX.Element[] = [];
    if (import.meta.env.VITE_BASIC_HOST === "FB") {
        const { setIsLoading, login } = useContextElement();
        const navigate = useNavigate();
        const location = useLocation();
        const from = location.state?.from?.pathname || '/';

        const { mutate: handleLoginEvent } = useLogin(
            (data) => {
                setIsLoading(false)
                login(data.access_token, data.data.user);
                navigate(from, { replace: true });
            },
            (error) => {
                console.log("Error");
                console.log(error);
                setIsLoading(false)
            }
        )

        const handleLogin = () => {
            setIsLoading(true)
            handleLoginEvent({
                method: "google", body: {}
            })
        };
        authMethods.push(
            <a
                onClick={handleLogin}
                className="social-google">
                <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" width="24px" height="24px" viewBox="0 0 30 30" style={{ fill: "currentColor" }}>
                    <path d="M 15.003906 3 C 8.3749062 3 3 8.373 3 15 C 3 21.627 8.3749062 27 15.003906 27 C 25.013906 27 27.269078 17.707 26.330078 13 L 25 13 L 22.732422 13 L 15 13 L 15 17 L 22.738281 17 C 21.848702 20.448251 18.725955 23 15 23 C 10.582 23 7 19.418 7 15 C 7 10.582 10.582 7 15 7 C 17.009 7 18.839141 7.74575 20.244141 8.96875 L 23.085938 6.1289062 C 20.951937 4.1849063 18.116906 3 15.003906 3 z"></path>
                </svg>
            </a>
        )
    } else if (import.meta.env.VITE_API_GOOGLE_CLIENT_ID) {
        const GoogleAuth = lazy(() => import("./google-auth"));
        authMethods.push(
            <Suspense fallback={<div />}>
                <GoogleAuth />
            </Suspense>
        );
    }

    if (authMethods.length < 1) return (<div />)

    return (
        <div className="justify-content-center" style={{ textAlign: "center" }}>
            <div className="p-3">OR LOGIN WITH</div>
            <ul className={`tf-social-icon "style-white" justify-content-center`} >

                {authMethods.map((link, index) => (
                    <li key={index}>{link}
                        {/* <a href={link.href} className={link.className}>
                                            <i className={`icon ${link.iconClass}`} />
                                        </a> */}
                    </li>
                ))}
            </ul>

        </div>
    )
}