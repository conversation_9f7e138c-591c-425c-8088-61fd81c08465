import { useRegister } from "@/hooks/auth-quries";
import { useContextElement } from "@/layout/context";
import { useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";

export default function RegisterComponent() {
    const { setIsLoading, login } = useContextElement();
    const navigate = useNavigate();
    const location = useLocation();
    const from = location.state?.from?.pathname || '/';

    const [passwordType, setPasswordType] = useState("password");
    const [confirmPasswordType, setConfirmPasswordType] = useState("password");
    const [error, setError] = useState<string | null>(null);

    const togglePassword = () => {
        setPasswordType((prevType) =>
            prevType === "password" ? "text" : "password"
        );
    };

    const toggleConfirmPassword = () => {
        setConfirmPasswordType((prevType) =>
            prevType === "password" ? "text" : "password"
        );
    };

    const { mutate: handleRegisterEvent } = useRegister(
        (data) => {
            setIsLoading(false)
            login(data.access_token, data.data.user);
            navigate(from, { replace: true });
        },
        (error) => {
            console.log("Error");
            console.log(error);
            setIsLoading(false)
        }
    )

    const handleRegistration = (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault()
        const formData = new FormData(e.target as HTMLFormElement);
        const name = formData.get("name");
        const email = formData.get("email");
        const password = formData.get("password");
        const confirmPassword = formData.get("confirmPassword");
        const agreeCheckbox = formData.get("agree_checkbox");
        if (password !== confirmPassword) {
            setError("Passwords do not match");
            return;
        }
        if (typeof password === "string" && password.length < 6) {
            setError("Password must be at least 6 characters");
            return;
        }
        if (!agreeCheckbox) {
            setError("You must agree to the terms of use");
            return;
        }
        console.log("Registering user with email:", email);
        console.log("Registering user with password:", password);

        setError(null);
        setIsLoading(true)
        handleRegisterEvent({
            "name": name,
            "email": email,
            "password": password
        })
    }
    return (
        <section className="flat-spacing">
            <div className="container">
                <div className="login-wrap">
                    <div className="left">
                        <div className="heading">
                            <h4>Register</h4>
                        </div>
                        <form
                            onSubmit={handleRegistration}
                            className="form-login form-has-password"
                        >
                            <div className="wrap">
                                <fieldset className="">
                                    <input
                                        className=""
                                        type="text"
                                        placeholder="Name*"
                                        name="name"
                                        tabIndex={2}
                                        defaultValue=""
                                        aria-required="true"
                                        required
                                    />
                                </fieldset>
                                <fieldset className="">
                                    <input
                                        className=""
                                        type="email"
                                        placeholder="Email address*"
                                        name="email"
                                        tabIndex={2}
                                        defaultValue=""
                                        aria-required="true"
                                        required
                                    />
                                </fieldset>
                                <fieldset className="position-relative password-item">
                                    <input
                                        className="input-password"
                                        type={passwordType}
                                        placeholder="Password*"
                                        name="password"
                                        tabIndex={2}
                                        defaultValue=""
                                        aria-required="true"
                                        required
                                    />
                                    <span
                                        className={`toggle-password ${!(passwordType === "text") ? "unshow" : ""
                                            }`}
                                        onClick={togglePassword}
                                    >
                                        <i
                                            className={`icon-eye-${!(passwordType === "text") ? "hide" : "show"
                                                }-line`}
                                        />
                                    </span>
                                </fieldset>

                                <fieldset className="position-relative password-item">
                                    <input
                                        className="input-password"
                                        type={confirmPasswordType}
                                        placeholder="Confirm Password*"
                                        name="confirmPassword"
                                        tabIndex={2}
                                        defaultValue=""
                                        aria-required="true"
                                        required
                                    />
                                    <span
                                        className={`toggle-password ${!(confirmPasswordType === "text") ? "unshow" : ""
                                            }`}
                                        onClick={toggleConfirmPassword}
                                    >
                                        <i
                                            className={`icon-eye-${!(confirmPasswordType === "text") ? "hide" : "show"
                                                }-line`}
                                        />
                                    </span>
                                </fieldset>
                                <div className="d-flex align-items-center">
                                    <div className="tf-cart-checkbox">
                                        <div className="tf-checkbox-wrapp">
                                            <input
                                                defaultChecked
                                                className=""
                                                type="checkbox"
                                                id="login-form_agree"
                                                name="agree_checkbox"
                                            />
                                            <div>
                                                <i className="icon-check" />
                                            </div>
                                        </div>
                                        <label
                                            className="text-secondary-2"
                                            htmlFor="login-form_agree"
                                        >
                                            I agree to the&nbsp;
                                        </label>
                                    </div>
                                    <a href={`/term-of-use`} title="Terms of Service">
                                        Terms of User
                                    </a>
                                </div>
                            </div>
                            {error && (
                                <div className="text-danger m-2">
                                    {error}
                                </div>
                            )}
                            <div className="button-submit">
                                <button className="tf-btn btn-fill" type="submit">
                                    <span className="text text-button">Register</span>
                                </button>
                            </div>
                        </form>
                    </div>
                    <div className="right">
                        <h4 className="mb_8">Already have an account?</h4>
                        <p className="text-secondary">
                            Welcome back. Sign in to access your personalized experience,
                            saved preferences, and more. We're thrilled to have you with us
                            again!
                        </p>
                        <a href={`/login`} className="tf-btn btn-fill">
                            <span className="text text-button">Login</span>
                        </a>
                    </div>
                </div>
            </div>
        </section>
    );
}