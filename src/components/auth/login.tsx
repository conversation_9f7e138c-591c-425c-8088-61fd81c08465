import { useState } from "react";
import { Link, useLocation, useNavigate } from 'react-router-dom';
import OtherAuth from "./other-auth";
import { useContextElement } from "@/layout/context";
import { useLogin } from "@/hooks/auth-quries";

export default function LoginComponent() {
    const { setIsLoading, login } = useContextElement();
    const navigate = useNavigate();
    const location = useLocation();
    const from = location.state?.from?.pathname || '/';

    const [passwordType, setPasswordType] = useState("password");

    const togglePassword = () => {
        setPasswordType((prevType) =>
            prevType === "password" ? "text" : "password"
        );
    };

    const { mutate: handleLoginEvent } = useLogin(
        (data) => {
            setIsLoading(false)
            login(data.access_token, data.data.user);
            navigate(from, { replace: true });
        },
        (error) => {
            console.log("Error");
            console.log(error.response);
            setIsLoading(false)
        }
    )

    const handleLogin = (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        setIsLoading(true)
        const formData = new FormData(e.target as HTMLFormElement);
        const email = formData.get("email");
        const password = formData.get("password");
        handleLoginEvent({
            method: "email", body: {
                "email": email,
                "password": password
            }
        })
    };

    return (
        <section className="flat-spacing">
            <div className="container">
                <div className="login-wrap">
                    <div className="left">
                        <div className="heading">
                            <h4>Login</h4>
                        </div>
                        <form
                            onSubmit={handleLogin}
                            className="form-login form-has-password"
                        >
                            <div className="wrap">
                                <fieldset className="">
                                    <input
                                        className=""
                                        type="email"
                                        placeholder="Email address*"
                                        name="email"
                                        tabIndex={2}
                                        defaultValue=""
                                        aria-required="true"
                                        required
                                    />
                                </fieldset>
                                <fieldset className="position-relative password-item">
                                    <input
                                        className="input-password"
                                        type={passwordType}
                                        placeholder="Password*"
                                        name="password"
                                        tabIndex={2}
                                        defaultValue=""
                                        aria-required="true"
                                        required
                                    />
                                    <span
                                        className={`toggle-password ${!(passwordType === "text") ? "unshow" : ""
                                            }`}
                                        onClick={togglePassword}
                                    >
                                        <i
                                            className={`icon-eye-${!(passwordType === "text") ? "hide" : "show"
                                                }-line`}
                                        />
                                    </span>
                                </fieldset>
                                <div className="d-flex align-items-center justify-content-between">
                                    <div className="tf-cart-checkbox">
                                        <div className="tf-checkbox-wrapp">
                                            <input
                                                defaultChecked
                                                className=""
                                                type="checkbox"
                                                id="login-form_agree"
                                                name="agree_checkbox"
                                            />
                                            <div>
                                                <i className="icon-check" />
                                            </div>
                                        </div>
                                        <label htmlFor="login-form_agree"> Remember me </label>
                                    </div>
                                    <Link
                                        to={`/forget-password`}
                                        className="font-2 text-button forget-password link"
                                    >
                                        Forgot Your Password?
                                    </Link>
                                </div>
                            </div>
                            <div className="button-submit">
                                <button className="tf-btn btn-fill" type="submit">
                                    <span className="text text-button">Login</span>
                                </button>
                            </div>
                        </form>
                    </div>
                    <div className="right">
                        <h4 className="mb_8">New Customer</h4>
                        <p className="text-secondary">
                            Be part of our growing family of new customers! Join us today and
                            unlock a world of exclusive benefits, offers, and personalized
                            experiences.
                        </p>
                        <a href={`/register`} className="tf-btn btn-fill">
                            <span className="text text-button">Register</span>
                        </a>
                    </div>
                </div>

                <OtherAuth />

            </div>
        </section>
    );
}
