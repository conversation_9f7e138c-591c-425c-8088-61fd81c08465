import { ProductVariant } from "@/types/product/product";
import { Pagination } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";
import ProductCard4 from "../product-cards/product-card4";

export default function Products4({ products }: { products: ProductVariant[] }) {
    return (
        <section className="flat-spacing-4 pt-0">
            <div className="container">
                <div className="heading-section-2 wow fadeInUp">
                    <h4>Most-viewed Products</h4>
                    <a href={`/products`} className="line-under">
                        See All Products
                    </a>
                </div>
                <Swiper
                    dir="ltr"
                    className="swiper tf-sw-recent"
                    spaceBetween={30}
                    breakpoints={{
                        1200: {
                            slidesPerView: 6,
                        },
                        768: {
                            slidesPerView: 4,
                        },
                        480: {
                            slidesPerView: 3,
                        },
                        0: {
                            slidesPerView: 2,
                        },
                    }}
                    modules={[Pagination]}
                    pagination={{
                        clickable: true,
                        el: ".spd28",
                    }}
                >
                    {products.map((product, i) => (
                        <SwiperSlide key={i} className="swiper-slide">
                            <ProductCard4 product={product} />
                        </SwiperSlide>
                    ))}

                    <div className="sw-pagination-recent sw-dots type-circle justify-content-center spd28" />
                </Swiper>
            </div>
        </section>
    );
}