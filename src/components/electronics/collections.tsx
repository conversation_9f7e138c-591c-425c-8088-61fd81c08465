import { Pagination } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";

const collectionItems6 = [
    {
        id: 1,
        imgSrc: "https://modavenextjs.vercel.app/images/collections/cls-electronic-1.jpg",
        title: "New Apple Watch",
        description: "Stay connected and stylish with the latest Apple Watch.",
        wowDelay: "0s",
    },
    {
        id: 2,
        imgSrc: "https://modavenextjs.vercel.app/images/collections/cls-electronic-2.jpg",
        title: "Discover Galaxy",
        description: "Experience the cutting-edge Samsung S24",
        wowDelay: "0.1s",
    },
    {
        id: 3,
        imgSrc: "https://modavenextjs.vercel.app/images/collections/cls-electronic-3.jpg",
        title: "Smart Speaker",
        description: "Google home smart speaker with google assistant",
        wowDelay: "0.2s",
    },
];

export default function Collections() {
    return (
        <section className="flat-spacing pt-0">
            <div className="container">
                <Swiper
                    spaceBetween={30}
                    slidesPerView={3}
                    breakpoints={{
                        1024: {
                            slidesPerView: 3,
                        },
                        768: {
                            slidesPerView: 2,
                        },
                        576: {
                            slidesPerView: 1.7,
                        },
                        0: {
                            slidesPerView: 1,
                        },
                    }}
                    className="swiper tf-sw-collection"
                    modules={[Pagination]}
                    pagination={{
                        clickable: true,
                        el: ".spd23",
                    }}
                >
                    {collectionItems6.map((item, index) => (
                        <SwiperSlide key={index}>
                            <div
                                className="collection-position-2 style-5 style-7 hover-img wow fadeInUp"
                                data-wow-delay={item.wowDelay}
                            >
                                <a className="img-style">
                                    <img
                                        className="lazyload"
                                        data-src={item.imgSrc}
                                        alt={`banner-cls-${item.id}`}
                                        src={item.imgSrc}
                                        width={616}
                                        height={410}
                                    />
                                </a>
                                <div className="content text-start">
                                    <h5 className="title mb_8">
                                        <a href={`/shop-default-grid`} className="link">
                                            {item.title}
                                        </a>
                                    </h5>
                                    <p className="mb_16">{item.description}</p>
                                    <div>
                                        <a href={`/shop-default-grid`} className="btn-line">
                                            Shop Now
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </SwiperSlide>
                    ))}
                </Swiper>
                <div className="sw-pagination-collection sw-dots type-circle justify-content-center spd23" />
            </div>
        </section>
    );
}