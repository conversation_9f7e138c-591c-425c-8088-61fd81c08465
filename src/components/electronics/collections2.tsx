import { Category } from "@/types/product/collection";
import { Pagination } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";

export default function Collections2({ slides }: { slides: Category[] | undefined }) {
    return (
        <section className="flat-spacing-4 pt-0">
            <div className="container">
                <div className="heading-section-2 wow fadeInUp">
                    <h4>Our Featured Offers</h4>
                    <a href={`/categories`} className="line-under">
                        See All Offers
                    </a>
                </div>
                <Swiper
                    dir="ltr"
                    className="swiper tf-sw-products1"
                    data-preview={4}
                    data-tablet={4}
                    data-mobile={2}
                    spaceBetween={30}
                    breakpoints={{
                        768: {
                            slidesPerView: 4,
                        },

                        0: {
                            slidesPerView: 2,
                        },
                    }}
                    modules={[Pagination]}
                    pagination={{
                        clickable: true,
                        el: ".spd24",
                    }}
                >
                    {slides && slides.map((slide, index) => (
                        <SwiperSlide key={index} className="swiper-slide">
                            <div
                                className="collection-circle style-1 hover-img wow fadeInUp"
                                data-wow-delay={0}
                            >
                                <a href={`/${slide.slug}`} className="img-style">
                                    <img
                                        className="lazyload"
                                        data-src={slide.image_url}
                                        alt={slide.name}
                                        src={slide.image_url}
                                        width={361}
                                        height={360}
                                        style={{
                                            aspectRatio: "1/1",
                                            objectFit: "cover",
                                        }}
                                    />
                                </a>
                                <div className="collection-content text-center">
                                    <h5 className="heading">{slide.name}</h5>
                                    <div>
                                        <a href={`/${slide.slug}`} className="btn-line">
                                            Shop Now
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </SwiperSlide>
                    ))}

                    <div className="sw-pagination-products1 sw-dots type-circle justify-content-center spd24" />
                </Swiper>
            </div>
        </section>
    );
}