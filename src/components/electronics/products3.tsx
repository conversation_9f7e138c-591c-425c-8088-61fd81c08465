import { useContextElement } from "@/layout/context";
import { ProductVariant } from "@/types/product/product";

export default function Products3({ products }: { products: ProductVariant[] }) {
    const { addToCart } = useContextElement();
    const productsGroup: [ProductVariant[], ProductVariant[], ProductVariant[]] = [[], [], []];
    products.forEach((item, index) => {
        productsGroup[index % 3].push(item)
    })

    return (
        <section className="flat-spacing-4">
            <div className="container">
                <div className="grid-card-product tf-grid-layout lg-col-3 md-col-2">
                    {productsGroup.map((productList, index) => (
                        <div className="column-card-product" key={index}>
                            <h5 className="heading wow fadeInUp" style={{ visibility: index === 0 ? "visible" : "hidden" }}>
                                Top products
                            </h5>

                            <div className="list-card-product" >
                                {productList.map((product, index) => {
                                    const rating = Math.floor(Math.random() * 4) + 1;
                                    return (
                                        (
                                            <div
                                                key={index}
                                                className="card-product list-st-2 wow fadeInUp"
                                            >
                                                <div className="card-product-wrapper">
                                                    <a
                                                        href={`/product-details/${product.slug}`}
                                                        className="product-img"
                                                    >
                                                        <img
                                                            className="lazyload img-product"
                                                            data-src={product.images[0].image_url}
                                                            alt="image-product"
                                                            src={product.images[0].image_url}
                                                            width={product.images[0].width}
                                                            height={product.images[0].height}
                                                            style={{
                                                                aspectRatio: "3/4",
                                                            }}
                                                        />
                                                        <img
                                                            className="lazyload img-hover"
                                                            data-src={product.images[1].image_url}
                                                            alt="image-product"
                                                            src={product.images[1].image_url}
                                                            width={product.images[1].width}
                                                            height={product.images[1].height}
                                                            style={{
                                                                aspectRatio: "3/4",
                                                            }}
                                                        />
                                                    </a>
                                                    <div className="on-sale-wrap">
                                                        <span className="on-sale-item">
                                                            -{(((product.mrp ?? 0) - product.selling_price) / (product.mrp ?? product.selling_price) * 100).toFixed(0)}%
                                                        </span>
                                                    </div>
                                                </div>
                                                <div className="card-product-info">
                                                    <a
                                                        href={`/product-details/${product.slug}`}
                                                        className="title link"
                                                    >
                                                        {product.name}
                                                    </a>
                                                    <div className="bottom">
                                                        <div className="inner-left">
                                                            <div className="box-rating">
                                                                <ul className="list-star">
                                                                    {[...Array(rating)].map((_, starIndex) => (
                                                                        <li key={starIndex} className="icon icon-star" />
                                                                    ))}
                                                                    {[...Array(5 - rating)].map((_, starIndex) => (
                                                                        <li key={starIndex} className="icon icon-star diabled" />
                                                                    ))}
                                                                </ul>
                                                                <span className="text-caption-1 text-secondary">
                                                                    ({Math.floor(Math.random() * 100) + 1})
                                                                </span>
                                                            </div>
                                                            <span className="price py-4">
                                                                ${product.selling_price.toFixed(2)}
                                                            </span>
                                                        </div>
                                                        <a
                                                            onClick={() => addToCart(product.id, 1)}
                                                            className="box-icon"
                                                        >
                                                            <svg
                                                                width={25}
                                                                height={24}
                                                                viewBox="0 0 25 24"
                                                                fill="none"
                                                                xmlns="http://www.w3.org/2000/svg"
                                                            >
                                                                <path
                                                                    d="M16.2187 10.3327V5.99935C16.2187 4.85008 15.7622 3.74788 14.9495 2.93522C14.1369 2.12256 13.0347 1.66602 11.8854 1.66602C10.7361 1.66602 9.63394 2.12256 8.82129 2.93522C8.00863 3.74788 7.55208 4.85008 7.55208 5.99935V10.3327M4.30208 8.16602H19.4687L20.5521 21.166H3.21875L4.30208 8.16602Z"
                                                                    stroke="#181818"
                                                                    strokeWidth="1.6"
                                                                    strokeLinecap="round"
                                                                    strokeLinejoin="round"
                                                                />
                                                            </svg>
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        )
                                    )
                                }
                                )}
                            </div>


                        </div>
                    ))}
                </div>
            </div>
        </section>
    );
}