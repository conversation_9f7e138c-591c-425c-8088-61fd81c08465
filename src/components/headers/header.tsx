import { lazy, Suspense } from 'react';

const Header1 = lazy(() => import('./header1'));
const Header11 = lazy(() => import('./header11'));

export default function Header({ fullWidth = false }) {
    const type = import.meta.env.VITE_BASIC_TYPE || 'fashion';

    return (
        <Suspense fallback={<div></div>}>
            {type === 'fashion' ? (
                <Header1 fullWidth={fullWidth} />
            ) : (
                <Header11 />
            )}
        </Suspense>
    );
}
