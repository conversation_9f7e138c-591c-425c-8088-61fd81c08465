import { useContextElement } from "@/layout/context";
import { Category, Collection } from "@/types/product/collection";

export default function Nav() {
    const data = useContextElement().navData as Category[] | undefined
    if (!data) return null

    return (
        <>
            {data && data.slice(0, 6).map((menu, i) => (
                <li key={i} className={`menu-item position-relative ${getHeaderStyle(menu)}`}>
                    <a href={`/${menu.slug}`} className="item-link">
                        {menu.name}
                    </a>

                    <div className="sub-menu submenu-default">
                        <ul className="menu-list">
                            {menu.sub_categories?.map((subMenu, index) => (
                                <li
                                    key={index}
                                    className={`menu-item-li ${getHeaderStyle(menu, subMenu)} `}
                                >
                                    <a href={`/sub/${subMenu.slug}`} className="menu-link-text">
                                        {subMenu.name}
                                    </a>
                                </li>
                            ))}
                        </ul>
                    </div>
                </li>
            ))}
        </>
    );
}

function getHeaderStyle(menu: Category, subMenu?: Collection | undefined): string {
    const path = window.location.pathname
    if (subMenu) {
        return path.includes(subMenu.slug) ? "active" : ""
    } else {
        const paths = path.split("/")
        if (!paths.some((p) => p == "sub") && path.includes(menu.slug)) {
            return "active"
        } else {
            return menu.sub_categories?.some((sub) => sub.slug === paths[paths.length - 1]) ? "active" : ""
        }
    }
}
