import { useContextElement } from "@/layout/context";

export default function CategoryList() {
    const { navData } = useContextElement()

    return (
        <div className="list-categories-inner">
            <ul>
                {navData && navData.map((menu, i) => (
                    <li className="sub-categories2" key={i}>
                        <a href={`/${menu.slug}`} className="categories-item">
                            <img src={menu.image_url} alt={menu.name} height={24} width={24} />
                            <span className="inner-left">
                                <span className="text">{menu.name}</span>
                            </span>
                            {menu.sub_categories && <i className="icon icon-arrRight" />}
                        </a>
                        {menu.sub_categories && (
                            <ul className="list-categories-inner">
                                {menu.sub_categories.map((subMenu, index) => (
                                    <li key={index}>
                                        <a href={`/sub/${subMenu.slug}`} className="categories-item">
                                            <img src={subMenu.image_url} alt={subMenu.name} height={24} width={24} />
                                            <span className="inner-left">
                                                <span className="text">{subMenu.name}</span>
                                            </span>
                                        </a>
                                    </li>
                                ))}
                            </ul>
                        )}
                    </li>
                )
                )}
            </ul>
        </div>
    );
}