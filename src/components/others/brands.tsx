import { Swiper, SwiperSlide } from "swiper/react";

export const brands = [
    {
        id: 1,
        imgSrc: "/images/brand/vanfaba.png",
        alt: "brand",
        width: 174,
        height: 40,
    },
    {
        id: 2,
        imgSrc: "/images/brand/anvouge.png",
        alt: "brand",
        width: 137,
        height: 30,
    },
    {
        id: 3,
        imgSrc: "/images/brand/carolin.png",
        alt: "brand",
        width: 129,
        height: 40,
    },
    {
        id: 4,
        imgSrc: "/images/brand/shangxi.png",
        alt: "brand",
        width: 146,
        height: 40,
    },
    {
        id: 5,
        imgSrc: "/images/brand/ecomife.png",
        alt: "brand",
        width: 118,
        height: 26,
    },
    {
        id: 6,
        imgSrc: "/images/brand/cheryl.png",
        alt: "brand",
        width: 110,
        height: 40,
    },
    {
        id: 7,
        imgSrc: "/images/brand/sopify.png",
        alt: "brand",
        width: 87,
        height: 35,
    },
    {
        id: 8,
        imgSrc: "/images/brand/pennyw.png",
        alt: "brand",
        width: 127,
        height: 40,
    },
    {
        id: 9,
        imgSrc: "/images/brand/panadoxn.png",
        alt: "brand",
        width: 157,
        height: 40,
    },
    {
        id: 10,
        imgSrc: "/images/brand/shangxi.png",
        alt: "brand",
        width: 146,
        height: 40,
    },
];

export default function Brands({ parentClass = "flat-spacing-5 line-top" }) {
    return (
        <section className={parentClass}>
            <Swiper
                dir="ltr"
                className="swiper tf-sw-partner sw-auto"
                spaceBetween={50} // Equivalent to data-space={50}
                loop={true} // Equivalent to data-loop="true"
                autoplay={{ delay: 0 }} // Equivalent to data-auto-play="true" with a delay of 0
                breakpoints={{
                    1024: {
                        slidesPerView: "auto", // Equivalent to data-preview="auto"
                        spaceBetween: 74, // Equivalent to data-space-lg={74}
                    },
                    768: {
                        slidesPerView: "auto", // Equivalent to data-tablet="auto"
                        spaceBetween: 50, // Equivalent to data-space-md={50}
                    },
                    0: {
                        slidesPerView: 2, // Equivalent to data-mobile={2}
                        spaceBetween: 50, // Equivalent to data-space={50}
                    },
                }}
            >
                {brands.map((brand) => (
                    <SwiperSlide key={brand.id}>
                        <a href="#" className="brand-item">
                            <img
                                alt={brand.alt}
                                src={brand.imgSrc}
                                width={brand.width}
                                height={brand.height}
                            />
                        </a>
                    </SwiperSlide>
                ))}
            </Swiper>
        </section>
    );
}