

export default function StoreLocations3() {
  return (
    <section className="flat-spacing">
      <div className="container">
        <div className="row">
          <div className="col-12">
            <div className="contact-us-map">
              <div className="wrap-map">
                <div
                  id="map-contact"
                  className="map-contact"
                  data-map-zoom={16}
                  data-map-scroll="true"
                >
                  <iframe
                    src={import.meta.env.VITE_BASIC_LOCATION}
                    width={600}
                    height={450}
                    style={{ border: 0, width: "100%", height: "100%" }}
                    allowFullScreen={true}
                    loading="lazy"
                  />
                </div>
              </div>
              <div className="right">
                <h4>Information</h4>
                <div className="mb_20">
                  <div className="text-title mb_8">Phone:</div>
                  <p className="text-secondary"><a href={`tel:${import.meta.env.VITE_BASIC_MOBILE}`}><p>{import.meta.env.VITE_BASIC_MOBILE}</p></a></p>
                </div>
                <div className="mb_20">
                  <div className="text-title mb_8">Email:</div>
                  <p className="text-secondary"><a href={`mailto:${import.meta.env.VITE_BASIC_EMAIL}`}><p>{import.meta.env.VITE_BASIC_EMAIL}</p></a></p>
                </div>
                <div className="mb_20">
                  <div className="text-title mb_8">Address:</div>
                  <p className="text-secondary">
                    {import.meta.env.VITE_BASIC_ADDRESS}
                  </p>
                </div>
                {/* <div>
                  <div className="text-title mb_8">Open Time:</div>
                  <p className="mb_4 open-time">
                    <span className="text-secondary">Mon - Sat:</span> 7:30am -
                    8:00pm PST
                  </p>
                  <p className="open-time">
                    <span className="text-secondary">Sunday:</span> 9:00am -
                    5:00pm PST
                  </p>
                </div> */}
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}