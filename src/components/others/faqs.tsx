import data from "@/data/faq.json";

export default function Faqs() {
    return (
        <section className="flat-spacing">
            <div className="container">
                <div className="page-faqs-wrap">
                    <div className="list-faqs">
                        {data.map((item, index) => (
                            <div key={index}>
                                <h5 className="faqs-title">{item.title}</h5>
                                <ul
                                    className="accordion-product-wrap style-faqs"
                                    id={`accordion-faq-${index}`}
                                >
                                    {item.faq.map((faq, i) => (
                                        <li key={i} className="accordion-product-item">
                                            <a
                                                href={`#accordion-${index}-${i}`}
                                                className="accordion-title collapsed current"
                                                data-bs-toggle="collapse"
                                                aria-expanded="true"
                                                aria-controls={`accordion-${index}-${i}`}
                                            >
                                                <h6>{faq.question}</h6>
                                                <span className="btn-open-sub" />
                                            </a>
                                            <div
                                                id={`accordion-${index}-${i}`}
                                                className="collapse"
                                                data-bs-parent={`#accordion-faq-${index}`}
                                            >
                                                <div className="accordion-faqs-content">
                                                    <p className="text-secondary">{faq.answer}</p>
                                                </div>
                                            </div>
                                        </li>
                                    ))}
                                </ul>
                            </div>
                        ))}
                    </div>
                </div>
            </div>
        </section>
    );
}