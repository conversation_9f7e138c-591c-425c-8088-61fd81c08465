import { useEffect, useState } from "react";

interface InfoContentModel {
    title: string;
    scrollId: string;
    content: string;
}

export default function InfoContent({ title, data }: { title: string, data: InfoContentModel[] }) {
    const sectionIds = data.map((elm) => elm.scrollId);
    const [activeSection, setActiveSection] = useState(sectionIds[0]);

    useEffect(() => {
        // Create an IntersectionObserver to track visibility of sections
        const observer = new IntersectionObserver(
            (entries) => {
                entries.forEach((entry) => {
                    if (entry.isIntersecting) {
                        // Update active section when the section is visible in the viewport
                        setActiveSection(entry.target.id);
                    }
                });
            },
            {
                rootMargin: "-50% 0px", // Trigger when section is 50% visible
            }
        );

        // Observe each section
        sectionIds.forEach((id) => {
            const element = document.getElementById(id);
            if (element) {
                observer.observe(element);
            }
        });

        return () => {
            // Cleanup the observer when the component unmounts
            observer.disconnect();
        };
    }, [sectionIds]);

    const handleClick = (id: string) => {
        const element = document.getElementById(id);
        if (element) {
            element.scrollIntoView({ behavior: "smooth", block: "center" });
        }
    };

    return (
        <section className="flat-spacing">
            <div className="container">
                <div className="terms-of-use-wrap">
                    <div className="left sticky-top">
                        {data.map(({ title, scrollId }, index) => (
                            <h6
                                key={index}
                                onClick={() => handleClick(scrollId)}
                                className={`btn-scroll-target ${activeSection == scrollId ? "active" : ""
                                    }`}
                            >
                                {index + 1}. {title}
                            </h6>
                        ))}
                    </div>
                    <div className="right">
                        <h4 className="heading">{title}</h4>
                        {data.map((elm, index) => (
                            <div className="terms-of-use-item item-scroll-target" id={elm.scrollId} key={index}>
                                <h5 className="terms-of-use-title">{`${index + 1}. ${elm.title}`}</h5>
                                <div className="terms-of-use-content">
                                    <div
                                        dangerouslySetInnerHTML={{
                                            __html: elm.content.replace(
                                                /<a\s+/g,
                                                '<a style="margin-top: 10px; font-size: 14px; color: #0066cc; font-weight: 500; text-decoration: underline;" '
                                            )
                                        }}
                                    />
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </div>
        </section>
    );
}