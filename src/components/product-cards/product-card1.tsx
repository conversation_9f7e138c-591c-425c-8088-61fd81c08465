"use client";
import { useEffect, useState } from "react";
import { useContextElement } from "../../layout/context";
import { ProductVariant } from "../../types/product/product";

export default function ProductCard1({ variant, gridClass = "" }: { variant: ProductVariant, gridClass?: string }) {
    const [currentImage, setCurrentImage] = useState("");

    const {
        addToCart,
        isAddedToCartProducts,
        setQuickViewItem,
        updateWishlist,
        isAddedtoWishlist,
        addToCompareItem,
        isAddedtoCompareItem,
    } = useContextElement();

    useEffect(() => {
        // console.log(variant.images);

        if (variant.images && variant.images.length > 0) {
            setCurrentImage(variant.images[0].image_url);
        }
    }, [variant]);

    return (
        <div
            className={`card-product fadeInUp ${gridClass} ${variant.isOnSale ? "on-sale" : ""
                } ${true ? "card-product-size" : ""}`}
        >
            <div className="card-product-wrapper">
                <a href={`/product-details/${variant.slug}`} className="product-img">
                    <img
                        className="lazyload img-product"
                        src={currentImage}
                        alt={variant.name}
                        width={600}
                        height={800}
                    />

                    <img
                        className="lazyload img-hover"
                        src={currentImage}
                        alt={variant.name}
                        width={600}
                        height={800}
                    />
                </a>
                {variant?.hotSale && (
                    <div className="marquee-product bg-main">
                        <div className="marquee-wrapper">
                            <div className="initial-child-container">
                                <div className="marquee-child-item">
                                    <p className="font-2 text-btn-uppercase fw-6 text-white">
                                        Hot Sale 25% OFF
                                    </p>
                                </div>
                                <div className="marquee-child-item">
                                    <span className="icon icon-lightning text-critical" />
                                </div>
                                <div className="marquee-child-item">
                                    <p className="font-2 text-btn-uppercase fw-6 text-white">
                                        Hot Sale 25% OFF
                                    </p>
                                </div>
                                <div className="marquee-child-item">
                                    <span className="icon icon-lightning text-critical" />
                                </div>
                                <div className="marquee-child-item">
                                    <p className="font-2 text-btn-uppercase fw-6 text-white">
                                        Hot Sale 25% OFF
                                    </p>
                                </div>
                                <div className="marquee-child-item">
                                    <span className="icon icon-lightning text-critical" />
                                </div>
                                <div className="marquee-child-item">
                                    <p className="font-2 text-btn-uppercase fw-6 text-white">
                                        Hot Sale 25% OFF
                                    </p>
                                </div>
                                <div className="marquee-child-item">
                                    <span className="icon icon-lightning text-critical" />
                                </div>
                                <div className="marquee-child-item">
                                    <p className="font-2 text-btn-uppercase fw-6 text-white">
                                        Hot Sale 25% OFF
                                    </p>
                                </div>
                                <div className="marquee-child-item">
                                    <span className="icon icon-lightning text-critical" />
                                </div>
                            </div>
                        </div>
                        <div className="marquee-wrapper">
                            <div className="initial-child-container">
                                <div className="marquee-child-item">
                                    <p className="font-2 text-btn-uppercase fw-6 text-white">
                                        Hot Sale 25% OFF
                                    </p>
                                </div>
                                <div className="marquee-child-item">
                                    <span className="icon icon-lightning text-critical" />
                                </div>
                                <div className="marquee-child-item">
                                    <p className="font-2 text-btn-uppercase fw-6 text-white">
                                        Hot Sale 25% OFF
                                    </p>
                                </div>
                                <div className="marquee-child-item">
                                    <span className="icon icon-lightning text-critical" />
                                </div>
                                <div className="marquee-child-item">
                                    <p className="font-2 text-btn-uppercase fw-6 text-white">
                                        Hot Sale 25% OFF
                                    </p>
                                </div>
                                <div className="marquee-child-item">
                                    <span className="icon icon-lightning text-critical" />
                                </div>
                                <div className="marquee-child-item">
                                    <p className="font-2 text-btn-uppercase fw-6 text-white">
                                        Hot Sale 25% OFF
                                    </p>
                                </div>
                                <div className="marquee-child-item">
                                    <span className="icon icon-lightning text-critical" />
                                </div>
                                <div className="marquee-child-item">
                                    <p className="font-2 text-btn-uppercase fw-6 text-white">
                                        Hot Sale 25% OFF
                                    </p>
                                </div>
                                <div className="marquee-child-item">
                                    <span className="icon icon-lightning text-critical" />
                                </div>
                            </div>
                        </div>
                    </div>
                )}
                {/* {product.isOnSale && (
          <div className="on-sale-wrap">
            <span className="on-sale-item">-{product.salePercentage}</span>
          </div>
        )} */}
                {/* {product.sizes && (
          <div className="variant-wrap size-list">
            <ul className="variant-box">
              {product.sizes.map((size) => (
                <li key={size} className="size-item">
                  {size}
                </li>
              ))}
            </ul>
          </div>
        )} */}
                {/* {product.countdown && (
          <div className="variant-wrap countdown-wrap">
            <div className="variant-box">
              <div
                className="js-countdown"
                data-timer={product.countdown}
                data-labels="D :,H :,M :,S"
              >
                <CountdownTimer />
              </div>
            </div>
          </div>
        )} */}
                {variant.mrp ? (
                    <div className="on-sale-wrap">
                        <span className="on-sale-item">-{((variant.mrp - variant.selling_price) / variant.mrp * 100).toFixed(0)}%</span>
                    </div>
                ) : (
                    ""
                )}
                <div className="list-product-btn">
                    <a
                        onClick={() => updateWishlist(variant)}
                        className="box-icon wishlist btn-icon-action"
                    >
                        <span className="icon icon-heart" />
                        <span className="tooltip">
                            {isAddedtoWishlist(variant.id)
                                ? "Already Wishlished"
                                : "Wishlist"}
                        </span>
                    </a>
                    <a
                        href="#compare"
                        data-bs-toggle="offcanvas"
                        aria-controls="compare"
                        onClick={() => addToCompareItem(variant)}
                        className="box-icon compare btn-icon-action"
                    >
                        <span className="icon icon-gitDiff" />
                        <span className="tooltip">
                            {isAddedtoCompareItem(variant.id)
                                ? "Already compared"
                                : "Compare"}
                        </span>
                    </a>
                    <a
                        href="#quickView"
                        onClick={() => setQuickViewItem(variant)}
                        data-bs-toggle="modal"
                        className="box-icon quickview tf-btn-loading"
                    >
                        <span className="icon icon-eye" />
                        <span className="tooltip">Quick View</span>
                    </a>
                </div>
                <div className="list-btn-main">
                    <a
                        className="btn-main-product"
                        onClick={() => addToCart(variant.id, 1)}
                    >
                        {isAddedToCartProducts(variant.id)
                            ? "Already Added"
                            : "ADD TO CART"}
                    </a>
                </div>
            </div>
            <div className="card-product-info">
                <a href={`/product-details/${variant.slug}`} className="title link">
                    {variant.name}
                </a>
                <span className="price">
                    {variant.mrp && (
                        <span className="old-price">₹{variant.mrp?.toFixed(2)}</span>
                    )}{" "}
                    ₹{variant.selling_price?.toFixed(2)}
                </span>
                {/* {product.colors && (
          <ul className="list-color-product">
            {product.colors.map((color, index) => (
              <li
                key={index}
                className={`list-color-item color-swatch ${
                  currentImage == color.imgSrc ? "active" : ""
                } ${color.bgColor == "bg-white" ? "line" : ""}`}
                onMouseOver={() => setCurrentImage(color.imgSrc)}
              >
                <span className={`swatch-value ${color.bgColor}`} />
                <Image
                  className="lazyload"
                  src={color.imgSrc}
                  alt="color variant"
                  width={600}
                  height={800}
                />
              </li>
            ))}
          </ul>
        )} */}
            </div>
        </div>
    );
}
