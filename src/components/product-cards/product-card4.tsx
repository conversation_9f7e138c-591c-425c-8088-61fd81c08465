import { useContextElement } from "@/layout/context";
import { ProductVariant } from "@/types/product/product";
import { useEffect, useState } from "react";

export default function ProductCard4({ product }: { product: ProductVariant }) {
    const [currentImage, setCurrentImage] = useState("");
    const rating = Math.floor(Math.random() * 4) + 1;

    const {
        updateWishlist,
        isAddedtoWishlist,
        addToCompareItem,
        isAddedtoCompareItem,
        setQuickViewItem,
        addToCart,
        isAddedToCartProducts,
    } = useContextElement();

    useEffect(() => {
        setCurrentImage(product.images[0].image_url);
    }, [product]);

    return (
        <div
            className={`card-product wow fadeInUp ${product.isOnSale ? "on-sale" : ""
                } `
            }
        >
            <div className="card-product-wrapper">
                <a href={`/product-details/${product.slug}`} className="product-img">
                    <img
                        className="lazyload img-product"
                        src={currentImage}
                        alt={product.name}
                        width={600}
                        height={800}
                    />
                    <img
                        className="lazyload img-hover"
                        src={product.images[1].image_url}
                        alt={product.name}
                        width={600}
                        height={800}
                    />
                </a>
                {product.hotSale && (
                    <div className="marquee-product bg-main">
                        <div className="marquee-wrapper">
                            <div className="initial-child-container">
                                <div className="marquee-child-item">
                                    <p className="font-2 text-btn-uppercase fw-6 text-white">
                                        Hot Sale 25% OFF
                                    </p>
                                </div>
                                <div className="marquee-child-item">
                                    <span className="icon icon-lightning text-critical" />
                                </div>
                                <div className="marquee-child-item">
                                    <p className="font-2 text-btn-uppercase fw-6 text-white">
                                        Hot Sale 25% OFF
                                    </p>
                                </div>
                                <div className="marquee-child-item">
                                    <span className="icon icon-lightning text-critical" />
                                </div>
                                <div className="marquee-child-item">
                                    <p className="font-2 text-btn-uppercase fw-6 text-white">
                                        Hot Sale 25% OFF
                                    </p>
                                </div>
                                <div className="marquee-child-item">
                                    <span className="icon icon-lightning text-critical" />
                                </div>
                                <div className="marquee-child-item">
                                    <p className="font-2 text-btn-uppercase fw-6 text-white">
                                        Hot Sale 25% OFF
                                    </p>
                                </div>
                                <div className="marquee-child-item">
                                    <span className="icon icon-lightning text-critical" />
                                </div>
                                <div className="marquee-child-item">
                                    <p className="font-2 text-btn-uppercase fw-6 text-white">
                                        Hot Sale 25% OFF
                                    </p>
                                </div>
                                <div className="marquee-child-item">
                                    <span className="icon icon-lightning text-critical" />
                                </div>
                            </div>
                        </div>
                        <div className="marquee-wrapper">
                            <div className="initial-child-container">
                                <div className="marquee-child-item">
                                    <p className="font-2 text-btn-uppercase fw-6 text-white">
                                        Hot Sale 25% OFF
                                    </p>
                                </div>
                                <div className="marquee-child-item">
                                    <span className="icon icon-lightning text-critical" />
                                </div>
                                <div className="marquee-child-item">
                                    <p className="font-2 text-btn-uppercase fw-6 text-white">
                                        Hot Sale 25% OFF
                                    </p>
                                </div>
                                <div className="marquee-child-item">
                                    <span className="icon icon-lightning text-critical" />
                                </div>
                                <div className="marquee-child-item">
                                    <p className="font-2 text-btn-uppercase fw-6 text-white">
                                        Hot Sale 25% OFF
                                    </p>
                                </div>
                                <div className="marquee-child-item">
                                    <span className="icon icon-lightning text-critical" />
                                </div>
                                <div className="marquee-child-item">
                                    <p className="font-2 text-btn-uppercase fw-6 text-white">
                                        Hot Sale 25% OFF
                                    </p>
                                </div>
                                <div className="marquee-child-item">
                                    <span className="icon icon-lightning text-critical" />
                                </div>
                                <div className="marquee-child-item">
                                    <p className="font-2 text-btn-uppercase fw-6 text-white">
                                        Hot Sale 25% OFF
                                    </p>
                                </div>
                                <div className="marquee-child-item">
                                    <span className="icon icon-lightning text-critical" />
                                </div>
                            </div>
                        </div>
                    </div>
                )}
                {/* {product.isOnSale && (
            <div className="on-sale-wrap">
              <span className="on-sale-item">-{product.salePercentage}</span>
            </div>
          )} */}
                {/* {product.sizes && (
            <div className="variant-wrap size-list">
              <ul className="variant-box">
                {product.sizes.map((size) => (
                  <li key={size} className="size-item">
                    {size}
                  </li>
                ))}
              </ul>
            </div>
          )} */}
                {/* {product.countdown && (
            <div className="variant-wrap countdown-wrap">
              <div className="variant-box">
                <div
                  className="js-countdown"
                  data-timer={product.countdown}
                  data-labels="D :,H :,M :,S"
                >
                  <CountdownTimer />
                </div>
              </div>
            </div>
          )} */}
                {product.mrp ? (
                    <div className="on-sale-wrap">
                        <span className="on-sale-item">-{(((product.mrp ?? 0) - product.selling_price) / (product.mrp ?? product.selling_price) * 100).toFixed(0)}%</span>
                    </div>
                ) : (
                    ""
                )}
                <div className="list-product-btn">
                    <a
                        onClick={() => updateWishlist(product)}
                        className="box-icon wishlist btn-icon-action"
                    >
                        <span className="icon icon-heart" />
                        <span className="tooltip">
                            {isAddedtoWishlist(product.id)
                                ? "Already Wishlished"
                                : "Wishlist"}
                        </span>
                    </a>
                    <a
                        href="#compare"
                        data-bs-toggle="offcanvas"
                        aria-controls="compare"
                        onClick={() => addToCompareItem(product)}
                        className="box-icon compare btn-icon-action"
                    >
                        <span className="icon icon-gitDiff" />
                        <span className="tooltip">
                            {" "}
                            {isAddedtoCompareItem(product.id)
                                ? "Already compared"
                                : "Compare"}
                        </span>
                    </a>
                    <a
                        href="#quickView"
                        onClick={() => setQuickViewItem(product)}
                        data-bs-toggle="modal"
                        className="box-icon quickview tf-btn-loading"
                    >
                        <span className="icon icon-eye" />
                        <span className="tooltip">Quick View</span>
                    </a>
                </div>
                <div className="list-btn-main">
                    <a
                        href="#shoppingCart"
                        data-bs-toggle="modal"
                        className="btn-main-product"
                        onClick={() => addToCart(product.id, 1)}
                    >
                        {isAddedToCartProducts(product.id)
                            ? "Already Added"
                            : "ADD TO CART"}
                    </a>
                </div>
            </div>
            <div className="card-product-info">
                <a href={`/product-details/${product.slug}`} className="title link">
                    {product.name}
                </a>
                <div className="box-rating">
                    <ul className="list-star">
                        {Array.from({ length: rating }).map((_, i) => (
                            <li key={i} className="icon icon-star" />
                        ))}
                        {Array.from({ length: 5 - rating }).map((_, i) => (
                            <li key={i} className="icon icon-star disabled" />
                        ))}
                    </ul>
                    <span className="text-caption-1 text-secondary"> ({Math.floor(Math.random() * 100) + 1}) </span>
                </div>
                <span className="price">
                    {product.mrp && (
                        <span className="old-price">${product.mrp.toFixed(2)}</span>
                    )}{" "}
                    ${product.selling_price.toFixed(2)}
                </span>
            </div>
        </div>
    );
}