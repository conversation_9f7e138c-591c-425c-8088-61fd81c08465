import React from 'react';
import { useDeleteAddress, useGetAddress } from '../../hooks/auth-quries';
import AddressModal from '../modals/address-modal';
import { useContextElement } from '@/layout/context';
import { Loader } from '../common/query-wrapper';

const AddressList: React.FC = () => {
    const { isLoading, data: addresses } = useGetAddress();
    const { editAddress, setIsLoading } = useContextElement();

    const { mutate: deleteAddress } = useDeleteAddress(
        (isLoading: boolean) => {
            setIsLoading(isLoading);
        },
        () => { }
    );

    const handleRemove = async (id: number | string) => {
        deleteAddress(id);
    };

    // const handleSetDefault = async (id: number | string) => {
    //     // Implement set default functionality
    //     console.log('Set default:', id);
    // };

    if (isLoading) {
        return (<Loader />);
    }

    return (
        <>
            <section className="address-list-section">
                <div className="address-list-container">
                    {!addresses || addresses.length === 0 ? (
                        <div className="empty-address-state">
                            <svg className="empty-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                            </svg>
                            <h4 className="empty-title">No saved addresses</h4>
                            <p>Save your addresses for a faster checkout</p>
                            <div className="empty-action">
                                <button
                                    className="add-address-btn"
                                    data-bs-toggle="modal"
                                    data-bs-target="#addressModal"
                                >
                                    Add New Address
                                </button>
                            </div>
                        </div>
                    ) : (
                        <div className="address-grid">
                            {addresses.length < 10 && <button
                                className="address-card add-address-card"
                                data-bs-toggle="modal"
                                data-bs-target="#addressModal"
                            >
                                <div>
                                    <div className="add-icon-circle">
                                        <svg className="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                                        </svg>
                                    </div>
                                    <span className="add-address-text text-base font-medium">Add New Address</span>
                                </div>
                            </button>}

                            {addresses.map((address) => (
                                <div key={address.id} className="address-card">
                                    <div className="address-content">
                                        <div className="address-header">
                                            <b className="font-semibold text-base text-gray-800">{address.name}</b>
                                            {address.is_default && (
                                                <span className="address-badge">Default</span>
                                            )}
                                        </div>

                                        <div className="address-details">
                                            <p>{address.address_line_1}</p>
                                            {address.address_line_2 && <p>{address.address_line_2}</p>}
                                            {address.landmark && (
                                                <p className="text-gray-500 mt-2">
                                                    <span className="font-medium">Landmark:</span> {address.landmark}
                                                </p>
                                            )}
                                            <p className="mt-2">{address.city}, {address.state} {address.pin_code}</p>
                                            <p className="mt-2 text-gray-600">
                                                {address.contact_number}
                                            </p>
                                        </div>

                                        <div className="address-footer">
                                            <div className="action-buttons">
                                                <button
                                                    onClick={() => editAddress(address)}
                                                    className="btn-action btn-edit"
                                                >
                                                    <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                                    </svg>
                                                    Edit
                                                </button>
                                                <button onClick={() => handleRemove(address.id)} className="btn-action btn-remove">
                                                    <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                                    </svg>
                                                    Remove
                                                </button>
                                            </div>
                                            {/* {!address.is_default && (
                                                <button
                                                    onClick={() => handleSetDefault(address.id)}
                                                    className="btn-action btn-default"
                                                >
                                                    Set as Default
                                                </button>
                                            )} */}
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    )}
                </div>
            </section>
            <AddressModal />
        </>
    );
};

export default AddressList;
