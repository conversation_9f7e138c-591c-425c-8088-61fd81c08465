import { useGetAddress } from '@/hooks/auth-quries';
import { useGetOrders } from '@/hooks/checkout-quires';
import { useContextElement } from '@/layout/context';
import { Link } from 'react-router-dom';
import { Loader } from '../common/query-wrapper';
import { UserAddress } from '@/types/user';

const MyAccountContent = () => {
    const { user, cartDetails, wishlist } = useContextElement();
    const { data: recentOrders, isLoading: ordersLoading } = useGetOrders(2)
    const { data: savedAddresses, isLoading: addressLoading } = useGetAddress();

    if (!user) {
        return <div className="alert alert-danger">User not found</div>;
    }

    if (ordersLoading || addressLoading) {
        return <Loader />
    }

    const getInitials = (name: string) => {
        return name.split(' ')
            .map(word => word[0])
            .join('')
            .toUpperCase();
    };

    const getRandomColor = (name: string) => {
        const colors = [
            'linear-gradient(135deg, #FF6B6B, #FF8787)',
            'linear-gradient(135deg, #4D96FF, #96B6FF)',
            'linear-gradient(135deg, #50C878, #70E898)',
            'linear-gradient(135deg, #FFB347, #FFD097)',
            'linear-gradient(135deg, #9B59B6, #BB89D6)'
        ];
        const index = name.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
        return colors[index % colors.length];
    };

    const menuItems = [
        {
            icon: `<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M6 2L3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4z"/>
                    <line x1="3" y1="6" x2="21" y2="6"/>
                    <path d="M16 10a4 4 0 0 1-8 0"/>
                </svg>`,
            title: 'Orders',
            description: 'Order History & Status',
            path: '/orders'
        },
        {
            icon: `<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"/>
                </svg>`,
            title: 'Wishlist',
            description: 'Saved Items',
            path: '/wish-list'
        },
        {
            icon: `<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                    <circle cx="12" cy="7" r="4"/>
                </svg>`,
            title: 'Profile',
            description: 'Personal Information',
            path: '/profile'
        },
        {
            icon: `<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"/>
                    <circle cx="12" cy="10" r="3"/>
                </svg>`,
            title: 'Address',
            description: 'Shipping Addresses',
            path: '/address'
        }
    ];

    const formatAddress = (address: UserAddress) => {
        const addressParts = [
            address.address_line_1,
            address.address_line_2,
            address.landmark
        ].filter(part => part && part.trim().length > 0);

        return (
            <div className="address-details">
                <div className="">
                    <strong>{address.name}</strong>
                </div>
                <div className="address-lines">
                    <div>{addressParts.join(', ')}</div>
                    <div>{`${address.city}, ${address.state} - ${address.pin_code}`}</div>
                </div>
            </div>
        );
    };

    return (
        <div className="my-account-section section-padding">
            <div className="container">
                <div className="row g-4">
                    <div className="col-lg-3">
                        <div className="account-sidebar">
                            <div className="user-profile-card">
                                <div className="profile-cover"></div>
                                <div className="user-info">
                                    <div className="user-avatar"
                                        style={{ background: getRandomColor(user.first_name) }}>
                                        <span className="initials">{getInitials(user.first_name)}</span>
                                    </div>
                                    <h4 className="user-name">{user.first_name}</h4>
                                    <p className="user-email">{user.email}</p>
                                    <div className="user-stats">
                                        <div className="stat-item">
                                            <div className="stat-icon">
                                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                                    <path d="M6 2L3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4z" />
                                                    <line x1="3" y1="6" x2="21" y2="6" />
                                                    <path d="M16 10a4 4 0 0 1-8 0" />
                                                </svg>
                                            </div>
                                            <span className="stat-value">{cartDetails?.order_summary.items.length ?? 0}</span>
                                            <span className="stat-label">Cart</span>
                                        </div>
                                        <div className="stat-item">
                                            <div className="stat-icon">
                                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                                    <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z" />
                                                </svg>
                                            </div>
                                            <span className="stat-value">{wishlist?.length ?? 0}</span>
                                            <span className="stat-label">Wishlists</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div className="col-lg-9">
                        <div className="row g-2">
                            {menuItems.map((item, index) => (
                                <div key={index} className="col-6 col-sm-4 col-lg-3">
                                    <Link to={item.path} className="menu-card">
                                        <div className="card-body">
                                            <div className="icon-wrapper">
                                                <span dangerouslySetInnerHTML={{ __html: item.icon }} />
                                            </div>
                                            <h3>{item.title}</h3>
                                            <p>{item.description}</p>
                                        </div>
                                    </Link>
                                </div>
                            ))}
                        </div>
                        <div className="account-summary mt-4">
                            <div className="row g-3">
                                <div className="col-md-6">
                                    <div className="summary-card">
                                        <h4>Recent Orders</h4>
                                        {recentOrders && recentOrders.length > 0 ? (
                                            recentOrders.slice(0, 2).map((order, index) => (
                                                <div key={index} className="summary-item">
                                                    <span className="text"><a href={`/order/${order.id}`} >#{order.order_number}</a></span>
                                                    <span className={`badge ${order.status}`}>{order.status}</span>
                                                </div>
                                            ))
                                        ) : (
                                            <div className="empty-state">
                                                <div className="empty-icon">
                                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                                        <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z" />
                                                        <circle cx="12" cy="12" r="4" />
                                                    </svg>
                                                </div>
                                                <p>No orders yet</p>
                                                <Link to="/" className="btn-outline">Start Shopping</Link>
                                            </div>
                                        )}
                                    </div>
                                </div>
                                <div className="col-md-6">
                                    <div className="summary-card">
                                        <h4>Saved Addresses</h4>
                                        {savedAddresses && savedAddresses.length > 0 ? (
                                            savedAddresses.slice(0, 2).map((address, index) => (
                                                <div key={index} className="summary-item">
                                                    <span className="text">{formatAddress(address)}</span>
                                                    <span className={`type ${address.isDefault ? 'default' : ''}`}>
                                                        {address.isDefault ? 'Default' : 'Other'}
                                                    </span>
                                                </div>
                                            ))
                                        ) : (
                                            <div className="empty-state">
                                                <div className="empty-icon">
                                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                                        <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z" />
                                                        <circle cx="12" cy="10" r="3" />
                                                    </svg>
                                                </div>
                                                <p>No addresses saved</p>
                                                <a className="btn-outline"
                                                    data-bs-toggle="modal"
                                                    data-bs-target="#addressModal"
                                                    style={{ fontSize: '0.85rem' }}>
                                                    + Add new address
                                                </a>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default MyAccountContent;
