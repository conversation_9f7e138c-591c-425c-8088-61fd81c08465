import { useContextElement } from "@/layout/context"

export default function ProfileDetails() {
    const { user } = useContextElement()

    const getVerificationStatus = () => {
        if (user?.is_email_verified) {
            return (
                <span className="verified-badge">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                        <path d="M20 6L9 17l-5-5" />
                    </svg>
                    Verified
                </span>
            )
        }
        return <span className="unverified-badge">Not Verified</span>
    }

    return (
        <div className="profile-details-section section-padding">
            <div className="container">
                <div className="row justify-content-center">
                    <div className="col-lg-8">
                        <div className="profile-card">
                            <div className="profile-header">
                                <h4>Personal Information</h4>
                            </div>

                            <div className="profile-info-grid">
                                <div className="info-group">
                                    <div className="info-label">First Name</div>
                                    <div className="info-value">{user?.first_name || 'Not provided'}</div>
                                </div>

                                <div className="info-group">
                                    <div className="info-label">Last Name</div>
                                    <div className="info-value">{user?.last_name || 'Not provided'}</div>
                                </div>

                                <div className="info-group">
                                    <div className="info-label">Email Address</div>
                                    <div className="info-value">
                                        <div className="email-container">
                                            <span>{user?.email}</span>
                                            {getVerificationStatus()}
                                        </div>
                                    </div>
                                </div>

                                <div className="info-group">
                                    <div className="info-label">Phone Number</div>
                                    <div className="info-value">{user?.primary_phone || 'Not provided'}</div>
                                </div>

                                <div className="info-group">
                                    <div className="info-label">Account Status</div>
                                    <div className="info-value">
                                        <span className={`status-badge ${user?.is_active ? 'active' : 'inactive'}`}>
                                            {user?.is_active ? 'Active' : 'Inactive'}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
}