import { useState } from "react";
import ProductCard1 from "../product-cards/product-card1";
import { ProductVariant } from "@/types/product/product";
import { useSearchProducts } from "@/hooks/product-quries";
import { Product1Loader } from "../products/product1";

export default function SearchModal() {
    const [query, setQuery] = useState("")
    const [page, setPage] = useState(1)
    const { isLoading, data, isFetching } = useSearchProducts(query, page)

    const search = (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault()

        const formData = new FormData(e.currentTarget);
        const queryValue = formData.get("text") as string;
        setQuery(queryValue)
    }

    return (
        <div className="modal fade modal-search" id="search">
            <div className="modal-dialog modal-dialog-centered">
                <div className="modal-content">
                    <div className="d-flex justify-content-between align-items-center">
                        <h5>Search</h5>
                        <span
                            className="icon-close icon-close-popup"
                            data-bs-dismiss="modal"
                        />
                    </div>
                    <form className="form-search" onSubmit={search}>
                        <fieldset className="text">
                            <input
                                type="text"
                                placeholder="Searching..."
                                className=""
                                name="text"
                                tabIndex={0}
                                defaultValue=""
                                aria-required="true"
                            // required
                            />
                        </fieldset>
                        <button className="" type="submit">
                            <svg
                                className="icon"
                                width={20}
                                height={20}
                                viewBox="0 0 24 24"
                                fill="none"
                                xmlns="http://www.w3.org/2000/svg"
                            >
                                <path
                                    d="M11 19C15.4183 19 19 15.4183 19 11C19 6.58172 15.4183 3 11 3C6.58172 3 3 6.58172 3 11C3 15.4183 6.58172 19 11 19Z"
                                    stroke="#181818"
                                    strokeWidth={2}
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                />
                                <path
                                    d="M21.35 21.0004L17 16.6504"
                                    stroke="#181818"
                                    strokeWidth={2}
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                />
                            </svg>
                        </button>
                    </form>
                    {/* <div>
                        <h5 className="mb_16">Feature keywords Today</h5>
                        <ul className="list-tags">
                            <li>
                                <a href="#" className="radius-60 link">
                                    Dresses
                                </a>
                            </li>
                            <li>
                                <a href="#" className="radius-60 link">
                                    Dresses women
                                </a>
                            </li>
                            <li>
                                <a href="#" className="radius-60 link">
                                    Dresses midi
                                </a>
                            </li>
                            <li>
                                <a href="#" className="radius-60 link">
                                    Dress summer
                                </a>
                            </li>
                        </ul>
                    </div> */}
                    <div>
                        <h6 className="mb_16">Top products</h6>
                        <div className="tf-grid-layout tf-col-2 lg-col-3 xl-col-4">
                            {(isLoading || isFetching) && <Product1Loader />}
                            {data && data.data.map((product: ProductVariant, i: number) => (
                                <ProductCard1 variant={product} key={i} />
                            ))}
                        </div>
                    </div>
                    {/* Load Item */}

                    {page >= (data?.pages ?? 0) ? (
                        ""
                    ) : (
                        <div
                            className="wd-load view-more-button text-center"
                            onClick={() => setPage(page + 1)}
                        >
                            <button
                                className={`tf-loading btn-loadmore tf-btn btn-reset ${isFetching || isLoading ? "loading" : ""
                                    } `}
                            >
                                <span className="text text-btn text-btn-uppercase">
                                    Load more
                                </span>
                            </button>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
}
