import { useContextElement } from '@/layout/context';

export interface AlertModalProps {
    alertChildren: React.ReactNode;
    alertSize?: 'sm' | 'lg';
    dismissible?: boolean;
}

export default function AlertModal() {
    const { alertModel } = useContextElement();
    const dialogClass = alertModel?.alertSize ? `modal-${alertModel.alertSize}` : '';

    const childresn = <div className={`modal-dialog modal-dialog-centered ${dialogClass}`}>
        <div className="modal-content">
            {alertModel?.alertChildren}
        </div>
    </div>

    if (alertModel?.dismissible) {
        return (
            <div className="modal fade" id="alertModel">
                {childresn}
            </div>
        );
    }

    return (
        <div className="modal fade" id="alertModel"
            data-bs-backdrop='static'
            data-bs-keyboard={alertModel?.dismissible}
        >
            {childresn}
        </div>
    );
}