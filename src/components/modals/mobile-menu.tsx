import { useContextElement } from "@/layout/context";
import { Category, Collection } from "@/types/product/collection";
import { Link } from "react-router-dom";

// import { usePathname } from "next/navigation";
export default function MobileMenu() {
    const { navData, isAuthenticated, logout } = useContextElement()

    return (
        <div className="offcanvas offcanvas-start canvas-mb" id="mobileMenu">
            <span
                className="icon-close icon-close-popup"
                data-bs-dismiss="offcanvas"
                aria-label="Close"
            />
            <div className="mb-canvas-content">
                <div className="mb-body">
                    <div className="mb-content-top">
                        <ul className="nav-ul-mb" id="wrapper-menu-navigation">
                            {navData && navData.slice(0, 6).map((menu, i) => (
                                <li key={i} className="nav-mb-item active">
                                    <a
                                        href={`#dropdown-menu-1${i}`}
                                        className={`collapsed mb-menu-link ${getMenuStyle(menu)} `}
                                        data-bs-toggle="collapse"
                                        aria-expanded="true"
                                        aria-controls={`dropdown-menu-1${i}`}
                                    >
                                        <span>{menu.name}</span>
                                        <span className="btn-open-sub" />
                                    </a>
                                    <div id={`dropdown-menu-1${i}`} className="collapse">
                                        <ul className="sub-nav-menu">
                                            {menu.sub_categories?.map((subMenu, i) => (
                                                <li key={i}>
                                                    <a
                                                        href={`/sub/${subMenu.slug}`}
                                                        className={`sub-nav-link ${getMenuStyle(menu, subMenu)} `}
                                                    >
                                                        {subMenu.name}
                                                    </a>
                                                </li>
                                            ))}
                                        </ul>
                                    </div>
                                </li>
                            ))}
                        </ul>

                    </div>
                    {!isAuthenticated ? (<a href={`/login`} className="site-nav-icon">
                        <svg
                            className="icon"
                            width={18}
                            height={18}
                            viewBox="0 0 24 24"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                        >
                            <path
                                d="M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21"
                                stroke="#181818"
                                strokeWidth={2}
                                strokeLinecap="round"
                                strokeLinejoin="round"
                            />
                            <path
                                d="M12 11C14.2091 11 16 9.20914 16 7C16 4.79086 14.2091 3 12 3C9.79086 3 8 4.79086 8 7C8 9.20914 9.79086 11 12 11Z"
                                stroke="#181818"
                                strokeWidth={2}
                                strokeLinecap="round"
                                strokeLinejoin="round"
                            />
                        </svg>
                        Login
                    </a>) : (
                        <div className="mobile-account-menu">
                            <Link to="/my-account" className="mobile-menu-item">
                                <i className="icon-user menu-icon"></i>
                                <span>My Profile</span>
                            </Link>
                            <Link to="/orders" className="mobile-menu-item">
                                <svg
                                    className="menu-icon"
                                    width={18}
                                    height={18}
                                    viewBox="0 0 24 24"
                                    fill="none"
                                    xmlns="http://www.w3.org/2000/svg"
                                >
                                    <path
                                        d="M16.5078 10.8734V6.36686C16.5078 5.17166 16.033 4.02541 15.1879 3.18028C14.3428 2.33514 13.1965 1.86035 12.0013 1.86035C10.8061 1.86035 9.65985 2.33514 8.81472 3.18028C7.96958 4.02541 7.49479 5.17166 7.49479 6.36686V10.8734M4.11491 8.62012H19.8877L21.0143 22.1396H2.98828L4.11491 8.62012Z"
                                        stroke="currentColor"
                                        strokeWidth={1}
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                    />
                                </svg>
                                <span>My Orders</span>
                            </Link>
                            <Link to="/address" className="mobile-menu-item">
                                <i className="icon-map-pin menu-icon"></i>
                                <span>My Addresses</span>
                            </Link>
                            <div className="mobile-menu-divider"></div>
                            <a onClick={logout} className="mobile-menu-item text-danger">
                                <i className="icon-log-out menu-icon"></i>
                                <span>Logout</span>
                            </a>
                        </div>

                    )}

                </div>
            </div>
        </div>
    );
}

function getMenuStyle(menu: Category, subMenu?: Collection | undefined): string {
    const path = window.location.pathname
    if (subMenu) {
        return path.includes(subMenu.slug) ? "active" : ""
    } else {
        const paths = path.split("/")
        if (!paths.some((p) => p == "sub") && path.includes(menu.slug)) {
            return "active"
        } else {

            return menu.sub_categories?.some((sub) => sub.slug === paths[paths.length - 1]) ? "active" : ""
        }
    }
}