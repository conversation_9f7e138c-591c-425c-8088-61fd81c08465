"use client";
import { useContextElement } from "@/layout/context";
import { useState } from "react";
import QuantitySelect from "../product-details/quantity-select";
import ColorSelect from "../product-details/color-select";
import SizeSelect from "../product-details/size-select";
import Grid5 from "../common/grid5";
// import Image from "next/image";
// import SizeSelect from "../productDetails/SizeSelect";
// import ColorSelect from "../productDetails/ColorSelect";
// import Grid5 from "../productDetails/grids/Grid5";
// import { useContextElement } from "@/context/Context";
// import QuantitySelect from "../productDetails/QuantitySelect";
export default function QuickView() {
    const [activeColor, setActiveColor] = useState("gray");
    const [quantity, setQuantity] = useState(1);
    const {
        quickViewItem,
        addToCart,
        isAddedToCartProducts,
        cartDetails,
        updateWishlist,
        isAddedtoWishlist,
        addToCompareItem,
        isAddedtoCompareItem,
        // cartProducts,
        // updateQuantity,
    } = useContextElement();

    // const openModalSizeChoice = () => {
    // const bootstrap = require("bootstrap"); // dynamically import bootstrap
    // var myModal = new bootstrap.Modal(document.getElementById("size-guide"), {
    //     keyboard: false,
    // });

    // myModal.show();
    // document
    //   .getElementById("size-guide")
    //   .addEventListener("hidden.bs.modal", () => {
    //     myModal.hide();
    //   });
    // const backdrops = document.querySelectorAll(".modal-backdrop");
    // if (backdrops.length > 1) {
    //     // Apply z-index to the last backdrop
    //     const lastBackdrop = backdrops[backdrops.length - 1] as HTMLElement;
    //     lastBackdrop.style.zIndex = "1057";
    // }
    // };
    // if (!quickViewItem) return <></>
    return (
        <div className="modal fullRight fade modal-quick-view" id="quickView">
            <div className="modal-dialog">
                <div className="modal-content">
                    <Grid5
                        activeColor={activeColor}
                        setActiveColor={setActiveColor}
                        images={quickViewItem?.images ?? []}
                    />
                    <div className="wrap mw-100p-hidden">
                        <div className="header">
                            <h5 className="title">Quick View</h5>
                            <span
                                className="icon-close icon-close-popup"
                                data-bs-dismiss="modal"
                            />
                        </div>
                        {quickViewItem && <div className="tf-product-info-list">
                            <div className="tf-product-info-heading">
                                <div className="tf-product-info-name">
                                    {/* <div className="text text-btn-uppercase">{quickViewItem.ca}</div> */}
                                    <h3 className="name">{quickViewItem.name}</h3>
                                    <div className="sub">
                                        {/* <div className="tf-product-info-rate">
                                            <div className="list-star">
                                                <i className="icon icon-star" />
                                                <i className="icon icon-star" />
                                                <i className="icon icon-star" />
                                                <i className="icon icon-star" />
                                                <i className="icon icon-star" />
                                            </div>
                                            <div className="text text-caption-1">(134 reviews)</div>
                                        </div> */}
                                        <div className="tf-product-info-sold">
                                            <i className="icon icon-lightning" />
                                            <div className="text text-caption-1">
                                                18&nbsp;sold in last&nbsp;32&nbsp;hours
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div className="tf-product-info-desc">
                                    <div className="tf-product-info-price">
                                        <h5 className="price-on-sale font-2">
                                            ₹{quickViewItem.selling_price.toFixed(2)}
                                        </h5>
                                        {quickViewItem.mrp ? (
                                            <>
                                                <div className="compare-at-price font-2">
                                                    {" "}
                                                    ₹{quickViewItem.mrp.toFixed(2)}
                                                </div>
                                                <div className="badges-on-sale text-btn-uppercase">
                                                    -{((quickViewItem.mrp - quickViewItem.selling_price) / quickViewItem.mrp * 100).toFixed(0)}%
                                                </div>
                                            </>
                                        ) : (
                                            ""
                                        )}
                                    </div>
                                    <p>
                                        {quickViewItem.description}
                                    </p>
                                    <div className="tf-product-info-liveview">
                                        <i className="icon icon-eye" />
                                        <p className="text-caption-1">
                                            <span className="liveview-count">28</span> people are
                                            viewing this right now
                                        </p>
                                    </div>
                                </div>
                            </div>
                            <div className="tf-product-info-choose-option">

                                <ColorSelect
                                    variant={quickViewItem}
                                    colorOptions={[{ id: "", value: quickViewItem.options.color ?? "", image: quickViewItem.images.length > 0 ? quickViewItem.images[0].image_url : "" }]}
                                />
                                {quickViewItem.options && Object.entries(quickViewItem.options).filter((f) => f[0] != "color").map((v, index) => {
                                    return <SizeSelect key={index} title={v[0]} variant={quickViewItem} options={[{ id: 0, key: v[0], value: v[1] ?? "", stock: quickViewItem.stock }]} />
                                })}

                                <div className="tf-product-info-quantity">
                                    <div className="title mb_12">Quantity:</div>
                                    <QuantitySelect
                                        quantity={
                                            isAddedToCartProducts(quickViewItem.id)
                                                ? cartDetails?.order_summary.items.filter(
                                                    (elm) => elm.id == quickViewItem.id
                                                )[0].quantity
                                                : quantity
                                        }
                                        setQuantity={(qty: number) => {
                                            if (isAddedToCartProducts(quickViewItem.id)) {
                                                addToCart(quickViewItem.id, qty);
                                            } else {
                                                setQuantity(qty);
                                            }
                                        }}
                                    />
                                </div>
                                <div>
                                    <div className="tf-product-info-by-btn mb_10">
                                        <a
                                            className="btn-style-2 flex-grow-1 text-btn-uppercase fw-6 show-shopping-cart"
                                            onClick={() =>
                                                addToCart(quickViewItem.id, quantity)
                                            }
                                        >
                                            <span>
                                                {isAddedToCartProducts(quickViewItem.id)
                                                    ? "Already Added"
                                                    : "Add to cart"}
                                            </span>
                                        </a>
                                        <a
                                            href="#compare"
                                            onClick={() => addToCompareItem(quickViewItem)}
                                            data-bs-toggle="offcanvas"
                                            aria-controls="compare"
                                            className="box-icon hover-tooltip compare btn-icon-action show-compare"
                                        >
                                            <span className="icon icon-gitDiff" />
                                            <span className="tooltip text-caption-2">
                                                {" "}
                                                {isAddedtoCompareItem(quickViewItem.id)
                                                    ? "Already compared"
                                                    : "Compare"}
                                            </span>
                                        </a>
                                        <a
                                            onClick={() => updateWishlist(quickViewItem)}
                                            className="box-icon hover-tooltip text-caption-2 wishlist btn-icon-action"
                                        >
                                            <span className="icon icon-heart" />
                                            <span className="tooltip text-caption-2">
                                                {isAddedtoWishlist(quickViewItem.id)
                                                    ? "Already Wishlished"
                                                    : "Wishlist"}
                                            </span>
                                        </a>
                                    </div>
                                    <a href={`/checkout?id=${quickViewItem.slug}`} className="btn-style-3 text-btn-uppercase">
                                        Buy it now
                                    </a>
                                </div>
                            </div>
                        </div>}
                    </div>
                </div>
            </div>
        </div>
    );
}
