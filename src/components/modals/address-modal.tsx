import { useAddAddress } from '@/hooks/auth-quries';
import { useContextElement } from '@/layout/context';
import { Modal } from 'bootstrap';
import React, { useState, useEffect } from 'react';

interface AddressFormData {
    id?: number | string;
    name: string;
    contact_number?: string;
    address_line_1: string;
    address_line_2: string;
    landmark?: string;
    city: string;
    state: string;
    country: string;
    pin_code: string;
    is_active?: boolean;
    address_type?: 'house' | 'apartment' | 'business' | 'other';
    comments?: string;
    latitude?: number | null;
    longitude?: number | null;
}

const initialFormData: AddressFormData = {
    name: '',
    contact_number: '',
    address_line_1: '',
    address_line_2: '',
    landmark: '',
    city: '',
    state: '',
    country: 'India',
    pin_code: '',
    is_active: true,
    address_type: 'house',
    comments: '',
    latitude: null,
    longitude: null,
};

export default function AddressModal() {
    const { setIsLoading, address } = useContextElement()
    const [formData, setFormData] = useState<AddressFormData>(initialFormData);
    const [errors, setErrors] = useState<Partial<AddressFormData>>({});
    const [isExpanded, setIsExpanded] = useState(false);

    const { mutate: addAddress } = useAddAddress(setIsLoading, () => {
        const modal = document.getElementById('addressModal');
        if (modal) {
            const bsModal = Modal.getInstance(modal);
            if (bsModal) {
                bsModal.hide();
            }
        }
    });

    // Handle modal show event
    useEffect(() => {
        const modal = document.getElementById('addressModal');
        if (modal) {
            if (address) {
                setFormData({
                    id: address.id,
                    name: address.name || '',
                    contact_number: address.contact_number || '',
                    address_line_1: address.address_line_1 || '',
                    address_line_2: address.address_line_2 || '',
                    landmark: address.landmark || '',
                    city: address.city || '',
                    state: address.state || '',
                    country: 'India',
                    pin_code: address.pin_code || '',
                    is_active: true,
                    address_type: address.address_type || 'house',
                    comments: address.comments || '',
                    latitude: address.latitude || null,
                    longitude: address.longitude || null,
                });
            }
        }
    }, [address, initialFormData]);

    // Reset form on modal close
    const handleClose = () => {
        setFormData(initialFormData);
        setErrors({});
        setIsExpanded(false);
    };

    useEffect(() => {
        const modal = document.getElementById('addressModal');
        if (modal) {
            modal.addEventListener('hidden.bs.modal', handleClose);
            return () => {
                modal.removeEventListener('hidden.bs.modal', handleClose);
            };
        }
    }, []);

    const validateForm = () => {
        const newErrors: Partial<AddressFormData> = {};

        if (!formData.name.trim()) newErrors.name = 'Name is required';
        if (formData.contact_number && !/^[\d\s+()-]*$/.test(formData.contact_number)) {
            newErrors.contact_number = 'Invalid contact number format';
        }
        if (!formData.address_line_1.trim()) newErrors.address_line_1 = 'Address line 1 is required';
        if (!formData.city.trim()) newErrors.city = 'City is required';
        if (!formData.state.trim()) newErrors.state = 'State is required';
        if (!formData.pin_code.trim()) newErrors.pin_code = 'Pincode is required';
        if (!/^\d{6}$/.test(formData.pin_code)) newErrors.pin_code = 'Invalid pincode';

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handlePhoneInput = (e: React.KeyboardEvent<HTMLInputElement>) => {
        const allowedChars = /^[\d\s+()-]$/;
        if (!allowedChars.test(e.key) && e.key !== 'Backspace' && e.key !== 'Delete' && e.key !== 'ArrowLeft' && e.key !== 'ArrowRight') {
            e.preventDefault();
        }
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        if (validateForm()) {
            addAddress(formData);
            setFormData(initialFormData);
        }
    };

    return (
        <div className="modal fade tf-modal-address" id="addressModal">
            <div className="modal-dialog modal-dialog-centered">
                <div className="modal-content">
                    <div className="tf-modal-header p-4 mb-2">
                        <div className="d-flex justify-content-between align-items-center w-100">
                            <h5>{address ? 'Edit Address' : 'Add New Address'}</h5>
                            <span
                                className="icon-close icon-close-popup"
                                data-bs-dismiss="modal"
                            />
                        </div>
                    </div>

                    <div className="modal-body px-4 pb-4">
                        <form onSubmit={handleSubmit} className="address-form">
                            <div className="input-groups mb-4">
                                <div className="form-group">
                                    <input
                                        type="text"
                                        placeholder="Name*"
                                        value={formData.name}
                                        onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                                        className={errors.name ? 'error' : ''}
                                    />
                                    {errors.name && <span className="error-text">{errors.name}</span>}
                                </div>

                                <div className="form-group">
                                    <input
                                        type="tel"
                                        placeholder="Mobile Number (Optional)"
                                        value={formData.contact_number || ''}
                                        onChange={(e) => setFormData({ ...formData, contact_number: e.target.value })}
                                        onKeyDown={handlePhoneInput}
                                        className={errors.contact_number ? 'error' : ''}
                                        maxLength={15}
                                    />
                                    {errors.contact_number && <span className="error-text">{errors.contact_number}</span>}
                                </div>
                            </div>

                            <div className="address-details mb-4">
                                <div className="form-group">
                                    <input
                                        type="text"
                                        placeholder="House No., Building Name*"
                                        value={formData.address_line_1}
                                        onChange={(e) => setFormData({ ...formData, address_line_1: e.target.value })}
                                        className={errors.address_line_1 ? 'error' : ''}
                                    />
                                    {errors.address_line_1 && <span className="error-text">{errors.address_line_1}</span>}
                                </div>

                                <div className="form-group">
                                    <input
                                        type="text"
                                        placeholder="Road Name, Area, Colony"
                                        value={formData.address_line_2}
                                        onChange={(e) => setFormData({ ...formData, address_line_2: e.target.value })}
                                    />
                                </div>

                                <div className="form-group">
                                    <input
                                        type="text"
                                        placeholder="Landmark (Optional)"
                                        value={formData.landmark}
                                        onChange={(e) => setFormData({ ...formData, landmark: e.target.value })}
                                    />
                                </div>

                                <div className="location-group">
                                    <div className="form-group">
                                        <input
                                            type="text"
                                            placeholder="City*"
                                            value={formData.city}
                                            onChange={(e) => setFormData({ ...formData, city: e.target.value })}
                                            className={errors.city ? 'error' : ''}
                                        />
                                        {errors.city && <span className="error-text">{errors.city}</span>}
                                    </div>

                                    <div className="form-group">
                                        <input
                                            type="text"
                                            placeholder="State*"
                                            value={formData.state}
                                            onChange={(e) => setFormData({ ...formData, state: e.target.value })}
                                            className={errors.state ? 'error' : ''}
                                        />
                                        {errors.state && <span className="error-text">{errors.state}</span>}
                                    </div>

                                    <div className="form-group">
                                        <input
                                            type="text"
                                            placeholder="Pincode*"
                                            value={formData.pin_code}
                                            onChange={(e) => setFormData({ ...formData, pin_code: e.target.value })}
                                            className={errors.pin_code ? 'error' : ''}
                                            maxLength={6}
                                        />
                                        {errors.pin_code && <span className="error-text">{errors.pin_code}</span>}
                                    </div>
                                </div>
                            </div>

                            <div className="expandable-section mb-4">

                                <a
                                    className="expand-header"
                                    onClick={() => setIsExpanded(!isExpanded)}
                                >
                                    <div className="expand-content">
                                        <div className="d-flex justify-content-between align-items-center">
                                            <span >
                                                <b className='expand-title '>Delivery Instructions (Optional)</b>
                                                <p className="expand-subtitle">
                                                    Your instructions help us deliver your packages to your expectations and will be used when possible.
                                                </p>
                                            </span>
                                            <span className="icon icon-arrow-down" style={{
                                                transform: isExpanded ? 'rotate(180deg)' : 'rotate(0deg)',
                                            }} />
                                        </div>

                                    </div>
                                </a>
                                {isExpanded && (
                                    <div className="expanded-content animate-expand">
                                        <div className="address-type-selector mb-4">
                                            <h6 className="section-label">Address Type</h6>
                                            <div className="type-buttons">
                                                <button
                                                    type="button"
                                                    className={`type-btn ${formData.address_type === 'house' ? 'active' : ''}`}
                                                    onClick={() => setFormData({ ...formData, address_type: 'house' })}
                                                >

                                                    <span>Home</span>
                                                </button>
                                                <button
                                                    type="button"
                                                    className={`type-btn ${formData.address_type === 'apartment' ? 'active' : ''}`}
                                                    onClick={() => setFormData({ ...formData, address_type: 'apartment' })}
                                                >

                                                    <span>Work</span>
                                                </button>
                                                <button
                                                    type="button"
                                                    className={`type-btn ${formData.address_type === 'business' ? 'active' : ''}`}
                                                    onClick={() => setFormData({ ...formData, address_type: 'business' })}
                                                >

                                                    <span>Office</span>
                                                </button>
                                                <button
                                                    type="button"
                                                    className={`type-btn ${formData.address_type === 'other' ? 'active' : ''}`}
                                                    onClick={() => setFormData({ ...formData, address_type: 'other' })}
                                                >

                                                    <span>Other</span>
                                                </button>
                                            </div>
                                        </div>

                                        <div className="delivery-instructions">
                                            <h6 className="section-label">Additional Instructions</h6>
                                            <textarea
                                                placeholder="Provide details such as building description, a nearby landmark, or other navigation instructions."
                                                value={formData.comments}
                                                onChange={(e) => setFormData({ ...formData, comments: e.target.value })}
                                                rows={2}
                                            />
                                        </div>
                                    </div>
                                )}
                            </div>

                            <div className="tf-modal-footer pt-3">
                                <button type="submit" className='tf-btn-sm' style={{ fontSize: '0.85rem', padding: '6px 24px', }} >Save Address</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div >
        </div >
    );
}
