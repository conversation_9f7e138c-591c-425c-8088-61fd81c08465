"use client";
import { useEffect, useRef, useState } from "react";
// import Image from "next/image";
import { Link } from 'react-router-dom';
// import emailjs from "@emailjs/browser";
// import CurrencySelect from "../common/CurrencySelect";
// import LanguageSelect from "../common/LanguageSelect";
// import ToolbarBottom from "../headers/ToolbarBottom";
import { footerLinks, links, socialLinks } from "../../utils/footer-links";
import ScrollTop from "../common/scroll-top";

export default function Footer1({
    border = true,
    dark = false,
    hasPaddingBottom = false,
}) {
    const formRef = useRef<HTMLFormElement>(null);
    const [success,] = useState(true);
    const [showMessage] = useState(false);

    // const handleShowMessage = () => {
    //     setShowMessage(true);
    //     setTimeout(() => {
    //         setShowMessage(false);
    //     }, 2000);
    // };

    const sendMail = (e: any) => {
        e.preventDefault();
        // emailjs
        //     .sendForm("service_noj8796", "template_fs3xchn", formRef!.current!, {
        //         publicKey: "iG4SCmR-YtJagQ4gV",
        //     })
        //     .then((res) => {
        //         if (res.status === 200) {
        //             setSuccess(true);
        //             handleShowMessage();

        //             formRef?.current?.reset();
        //         } else {
        //             setSuccess(false);
        //             handleShowMessage();
        //         }
        //     })
        //     .catch((err) => {
        //         console.log(err);
        //     });
    };
    useEffect(() => {
        const headings = document.querySelectorAll(".footer-heading-mobile");

        const toggleOpen = (event: any) => {
            const parent = event.target.closest(".footer-col-block");
            const content = parent.querySelector(".tf-collapse-content");

            if (parent.classList.contains("open")) {
                parent.classList.remove("open");
                content.style.height = "0px";
            } else {
                parent.classList.add("open");
                content.style.height = content.scrollHeight + 10 + "px";
            }
        };

        headings.forEach((heading) => {
            heading.addEventListener("click", toggleOpen);
        });

        // Clean up event listeners when the component unmounts
        return () => {
            headings.forEach((heading) => {
                heading.removeEventListener("click", toggleOpen);
            });
        };
    }, []); // Empty dependency array means this will run only once on mount
    return (
        <>
            <footer
                id="footer"
                className={`footer ${dark ? "bg-main" : ""} ${hasPaddingBottom ? "has-pb" : ""
                    } `}
            >
                <div className={`footer-wrap ${!border ? "border-0" : ""}`}>
                    <div className="footer-body">
                        <div className="container">
                            <div className="row">
                                <div className="col-lg-4">
                                    <div className="footer-infor">
                                        <div className="footer-logo">
                                            <Link to={`/`}>
                                                <img
                                                    alt=""
                                                    src={import.meta.env.VITE_FOOTER_LOGO}
                                                    width={144}
                                                    height={25}
                                                />
                                            </Link>
                                        </div>
                                        <div className="footer-address">
                                            <p>{import.meta.env.VITE_BASIC_ADDRESS}</p>
                                            <Link
                                                to={links.contactUs}
                                                className={`tf-btn-default fw-6 ${dark ? "style-white" : ""
                                                    } `}
                                            >
                                                GET DIRECTION
                                                <i className="icon-arrowUpRight" />
                                            </Link>
                                        </div>
                                        <ul className="footer-info">
                                            <li>
                                                <i className="icon-mail" />
                                                <a href={`mailto:${import.meta.env.VITE_BASIC_EMAIL}`}><p>{import.meta.env.VITE_BASIC_EMAIL}</p></a>
                                            </li>
                                            <li>
                                                <i className="icon-phone" />
                                                <a href={`tel:${import.meta.env.VITE_BASIC_MOBILE}`}><p>{import.meta.env.VITE_BASIC_MOBILE}</p></a>
                                            </li>
                                        </ul>
                                        <ul
                                            className={`tf-social-icon  ${dark ? "style-white" : ""
                                                } `}
                                        >
                                            {socialLinks.filter((link) => link.href).map((link, index) => (
                                                <li key={index}>
                                                    <a href={link.href} className={link.className} target="_blank">
                                                        <i className={`icon ${link.iconClass}`} />
                                                    </a>
                                                </li>
                                            ))}
                                        </ul>
                                    </div>
                                </div>
                                <div className="col-lg-4">
                                    <div className="footer-menu">
                                        {footerLinks.map((section, sectionIndex) => (
                                            <div className="footer-col-block" key={sectionIndex}>
                                                <div className="footer-heading text-button footer-heading-mobile">
                                                    {section.heading}
                                                </div>
                                                <div className="tf-collapse-content">
                                                    <ul className="footer-menu-list">
                                                        {section.items.map((item, itemIndex) => (
                                                            <li className="text-caption-1" key={itemIndex}>
                                                                {item.isLink ? (
                                                                    <a
                                                                        href={item.href}
                                                                        className="footer-menu_item"
                                                                    >
                                                                        {item.label}
                                                                    </a>
                                                                ) : (
                                                                    <a
                                                                        href={item.href}
                                                                        className="footer-menu_item"
                                                                    >
                                                                        {item.label}
                                                                    </a>
                                                                )}
                                                            </li>
                                                        ))}
                                                    </ul>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                                <div className="col-lg-4">
                                    {import.meta.env.VITE_FOOTER_ENABLE_NEWSLETTER === "true" && <div className="footer-col-block">
                                        <div className="footer-heading text-button footer-heading-mobile">
                                            Newletter
                                        </div>
                                        <div className="tf-collapse-content">
                                            <div className="footer-newsletter">
                                                <p className="text-caption-1">
                                                    Sign up for our newsletter and get 10% off your first
                                                    purchase
                                                </p>
                                                <div
                                                    className={`tfSubscribeMsg  footer-sub-element ${showMessage ? "active" : ""
                                                        }`}
                                                >
                                                    {success ? (
                                                        <p style={{ color: "rgb(52, 168, 83)" }}>
                                                            You have successfully subscribed.
                                                        </p>
                                                    ) : (
                                                        <p style={{ color: "red" }}>Something went wrong</p>
                                                    )}
                                                </div>
                                                <form
                                                    onSubmit={sendMail}
                                                    ref={formRef}
                                                    className={`form-newsletter subscribe-form ${dark ? "style-black" : ""
                                                        }`}
                                                >
                                                    <div className="subscribe-content">
                                                        <fieldset className="email">
                                                            <input
                                                                type="email"
                                                                name="email-form"
                                                                className="subscribe-email"
                                                                placeholder="Enter your e-mail"
                                                                tabIndex={0}
                                                                aria-required="true"
                                                            />
                                                        </fieldset>
                                                        <div className="button-submit">
                                                            <button
                                                                className="subscribe-button"
                                                                type="submit"
                                                            >
                                                                <i className="icon icon-arrowUpRight" />
                                                            </button>
                                                        </div>
                                                    </div>
                                                    <div className="subscribe-msg" />
                                                </form>
                                                <div className="tf-cart-checkbox">
                                                    <div className="tf-checkbox-wrapp">
                                                        <input
                                                            className=""
                                                            type="checkbox"
                                                            id="footer-Form_agree"
                                                            name="agree_checkbox"
                                                        />
                                                        <div>
                                                            <i className="icon-check" />
                                                        </div>
                                                    </div>
                                                    <label
                                                        className="text-caption-1"
                                                        htmlFor="footer-Form_agree"
                                                    >
                                                        By clicking subcribe, you agree to the{" "}
                                                        <Link className="fw-6 link" to={links.termsConditions}>
                                                            Terms of Service
                                                        </Link>{" "}
                                                        and
                                                        <a className="fw-6 link" href={links.privacyPolicy}>
                                                            Privacy Policy
                                                        </a>
                                                        .
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div className="footer-bottom">
                        <div className="container">
                            <div className="row">
                                <div className="col-12">
                                    <div className="footer-bottom-wrap">
                                        <div className="left">
                                            <p className="text-caption-1">
                                                {import.meta.env.VITE_BASIC_COPYRIGHT}. All Rights Reserved.
                                            </p>
                                            <div className="tf-cur justify-content-end">
                                                <div className="tf-currencies">
                                                    {/* <CurrencySelect light={dark ? true : false} /> */}
                                                </div>
                                                <div className="tf-languages">
                                                    {/* <LanguageSelect
                            parentClassName={`image-select center style-default type-languages ${
                              dark ? "color-white" : ""
                            }`}
                          /> */}
                                                </div>
                                            </div>
                                        </div>
                                        {import.meta.env.VITE_PAYMENT_ENABLE_ONLINE_PAYMENT && <div className="tf-payment">
                                            <p className="text-caption-1">Payment:</p>
                                            <ul>
                                                <li>
                                                    <img
                                                        alt=""
                                                        src="/images/payment/img-1.png"
                                                        width={100}
                                                        height={64}
                                                    />
                                                </li>
                                                <li>
                                                    <img
                                                        alt=""
                                                        src="/images/payment/img-2.png"
                                                        width={100}
                                                        height={64}
                                                    />
                                                </li>
                                                <li>
                                                    <img
                                                        alt=""
                                                        src="/images/payment/img-3.png"
                                                        width={100}
                                                        height={64}
                                                    />
                                                </li>
                                                <li>
                                                    <img
                                                        alt=""
                                                        src="/images/payment/img-4.png"
                                                        width={98}
                                                        height={64}
                                                    />
                                                </li>
                                                <li>
                                                    <img
                                                        alt=""
                                                        src="/images/payment/img-5.png"
                                                        width={102}
                                                        height={64}
                                                    />
                                                </li>
                                                <li>
                                                    <img
                                                        alt=""
                                                        src="/images/payment/img-6.png"
                                                        width={98}
                                                        height={64}
                                                    />
                                                </li>
                                            </ul>
                                        </div>}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </footer>
            <ScrollTop hasPaddingBottom={hasPaddingBottom} />
            {/* <ToolbarBottom /> */}
        </>
    );
}
