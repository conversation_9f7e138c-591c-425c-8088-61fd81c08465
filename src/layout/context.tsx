import { Loader } from "@/components/common/query-wrapper";
import { useAddToCart, useCartDetails, useUpdateWishList, useWishListDetails } from "@/hooks/checkout-quires";
import { useNavData } from "@/hooks/home-quries";
import { DataContextType } from "@/types/home";
import { ProductVariant } from "@/types/product/product";
import { User, UserAddress } from "@/types/user";
import { useQueryClient } from "@tanstack/react-query";
import { Modal } from "bootstrap";
import React, { useEffect } from "react";
import { useContext, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { getAuth, onAuthStateChanged } from "firebase/auth";
import AppConfig from "@/utils/app-config";
import { AlertModalProps } from "@/components/modals/alert-modal";

const dataContext = React.createContext<DataContextType | undefined>(undefined);
export const useContextElement = () => {
    const context = useContext(dataContext);
    if (!context) {
        throw new Error("useContextElement must be used within a Context Provider");
    }
    return context;
};

export default function Context({ children }: { children: (isLoading: boolean) => React.ReactNode }) {
    const { isLoading: initialLoading, data: navData } = useNavData()
    const [isLoading, setIsLoading] = useState(false);
    const [alertModel, setAlertModel] = useState<AlertModalProps | null>(null);

    const queryClient = useQueryClient();
    const navigate = useNavigate();
    const location = useLocation();

    const [authLoading, setAuthLoading] = useState(true);
    const [isAuthenticated, setIsAuthenticated] = useState(false);
    const [user, setUser] = useState<null | User>(null);

    const { isLoading: cartLoading, data: cartDetails } = useCartDetails(isAuthenticated, setIsLoading)

    const { mutate: addToCartEvent } = useAddToCart(setIsLoading)

    const { data: wishlist } = useWishListDetails(setIsLoading)

    const { mutate: updateWishListEvent } = useUpdateWishList(setIsLoading)

    const [cartProducts, setCartProducts] = useState<any>([]);
    const [compareItem, setCompareItem] = useState<ProductVariant[]>([]);
    const [quickViewItem, setQuickViewItem] = useState<ProductVariant>();
    const [quickAddItem, setQuickAddItem] = useState(1);
    const [totalPrice] = useState(0);

    const [address, setAddress] = useState<UserAddress>()


    // Auth effects
    useEffect(() => {
        setAuthLoading(true);
        if (import.meta.env.VITE_BASIC_HOST === "FB") {
            const app = AppConfig()
            const auth = getAuth(app);
            onAuthStateChanged(auth, (user) => {
                if (user) {
                    setIsAuthenticated(true);
                    setUser({
                        email: user.email || "",
                        is_email_verified: user.emailVerified || false,
                        first_name: user.displayName || "",
                        id: user.uid,
                        image_url: user.photoURL || "",
                        is_active: true,
                    });
                } else {
                    setIsAuthenticated(false);
                    setUser(null);
                }
                setAuthLoading(false);
            });
        } else {
            const token = localStorage.getItem("token");
            const userData = localStorage.getItem("user");
            if (token && userData) {
                setIsAuthenticated(true);
                setUser(JSON.parse(userData));
            }
            setAuthLoading(false);
        }
    }, []);


    // Auth methods
    const login = (token: string, userData: any) => {
        if (import.meta.env.VITE_BASIC_HOST !== "FB") {
            localStorage.setItem("token", token);
            localStorage.setItem("user", JSON.stringify(userData));
            setIsAuthenticated(true);
            setUser(userData);
        }
        queryClient.invalidateQueries();
        navigate(location.state?.from?.pathname || '/');
    };

    const logout = () => {
        if (import.meta.env.VITE_BASIC_HOST === "FB") {
            setIsLoading(true)
            const app = AppConfig()
            const auth = getAuth(app);
            auth.signOut().then(() => {
                setIsLoading(false)
            }).catch((_) => {
                setIsLoading(false)
            });
        } else {
            localStorage.removeItem("token");
            localStorage.removeItem("user");
            setIsAuthenticated(false);
            setUser(null);
        }
        queryClient.clear();
        navigate('/');
    };

    const isAddedToCartProducts = (id: string) => {
        if (cartDetails?.order_summary.items.filter((elm) => elm.id == id)[0]) {
            return true;
        }
        return false;
    };


    const addToCart = (product: string, qty: number) => {
        if (!isAuthenticated) {
            navigate('/login', { state: { from: location } });
            return;
        }
        addToCartEvent({ product: product, qty: qty })
    }

    const editAddress = (address: UserAddress | undefined) => {
        setAddress(address ? { ...address } : undefined);
        const modal = document.getElementById('addressModal');
        if (modal) {
            const bsModal = new Modal(modal);
            bsModal.show();
        }
    }

    const updateWishlist = (product: ProductVariant) => {
        const isAdd = !wishlist?.some((elm) => elm.id == product.id);
        updateWishListEvent({ product, isAdd })
    };

    const isAddedtoWishlist = (id: string) => {
        if (wishlist?.some((elm) => elm.id == id)) {
            return true;
        }
        return false;
    };

    const addToCompareItem = (product: ProductVariant) => {
        if (!compareItem.includes(product)) {
            setCompareItem((pre) => [...pre, product]);
        }
    };
    const removeFromCompareItem = (id: string) => {
        if (compareItem.find((elm) => elm.id == id)) {
            setCompareItem((pre) => [...pre.filter((elm) => elm.id != id)]);
        }
    };

    const isAddedtoCompareItem = (id: string) => {
        if (compareItem.find((elm) => elm.id == id)) {
            return true;
        }
        return false;
    };

    // Load compare items from localStorage on mount
    useEffect(() => {
        const items = JSON.parse(localStorage.getItem("compareItems") || "[]");
        if (items?.length) {
            setCompareItem(items);
        }
    }, []);

    // Update localStorage when compareItem changes
    useEffect(() => {
        localStorage.setItem("compareItems", JSON.stringify(compareItem));
    }, [compareItem]);

    const showAlert = (children: React.ReactNode, size?: "sm" | "lg" | undefined, dismissible: boolean = true) => {
        setAlertModel({
            alertChildren: children,
            alertSize: size,
            dismissible: dismissible
        });
        const modal = document.getElementById('alertModel');
        if (modal) {
            const bsModal = new Modal(modal);
            bsModal.show();
        }
    };
    const closeAlert = () => {
        const modal = document.getElementById('alertModel');
        if (modal) {
            const bsModal = new Modal(modal);
            bsModal.hide();
            setAlertModel(null);
        }
    };

    const contextElement = {
        navData,
        isLoading,
        setIsLoading,
        authLoading,
        isAuthenticated,
        user,
        login,
        logout,

        cartLoading,
        cartDetails,
        isAddedToCartProducts,
        addToCart,
        cartProducts,
        setCartProducts,
        totalPrice,
        wishlist,
        updateWishlist,
        isAddedtoWishlist,
        quickViewItem,
        setQuickViewItem,
        quickAddItem,
        setQuickAddItem,
        addToCompareItem,
        isAddedtoCompareItem,
        removeFromCompareItem,
        compareItem,
        setCompareItem,
        address,
        editAddress,

        showAlert,
        closeAlert,
        alertModel,
    };
    return (
        <dataContext.Provider value={contextElement}>
            {initialLoading ? <Loader /> : children(isLoading)}
        </dataContext.Provider>
    );
}
