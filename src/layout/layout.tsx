// import { usePathname } from "next/navigation";
import "../assets/scss/main.scss";
import "photoswipe/style.css";
import "react-range-slider-input/dist/style.css";
<link rel="stylesheet" href="/css/image-compare-viewer.min.css" />
import Context from "./context";
import { useEffect, useState } from "react";
import * as bootstrap from "bootstrap";
import SearchModal from "@/components/modals/search-model";
import MobileMenu from "@/components/modals/mobile-menu";
import CartModal from "@/components/modals/cart-model";
import { Loader } from "@/components/common/query-wrapper";
import QuickView from "@/components/modals/quick-view";
import AddressModal from "@/components/modals/address-modal";
import CompareModal from "@/components/modals/compare-modal";
import AlertModal from "@/components/modals/alert-modal";

// import { useEffect, useState } from "react";
// import Context from "@/context/context";

export default function Layout({
    children,
}: Readonly<{
    children: React.ReactNode;
}>) {
    //   const pathname = usePathname();
    useEffect(() => {
        if (typeof window !== "undefined") {
            // Import the script only on the client side
            // import("bootstrap/dist/js/bootstrap.esm").then(() => {
            //     // Module is imported, you can access any exported functionality if
            // });
        }
    }, []);
    useEffect(() => {
        const handleScroll = () => {
            const header = document.querySelector("header");
            if (window.scrollY > 100) {
                header!.classList.add("header-bg");
            } else {
                header!.classList.remove("header-bg");
            }
        };

        window.addEventListener("scroll", handleScroll);

        // Cleanup function to remove event listener on component unmount
        return () => {
            window.removeEventListener("scroll", handleScroll);
        };
    }, []); // Empty dependency array means this effect runs once on mount and cleans up on unmount

    const [scrollDirection, setScrollDirection] = useState("down");

    useEffect(() => {
        setScrollDirection("up");
        const handleScroll = () => {
            const currentScrollY = window.scrollY;

            if (currentScrollY > 250) {
                if (currentScrollY > lastScrollY.current) {
                    // Scrolling down
                    setScrollDirection("down");
                } else {
                    // Scrolling up
                    setScrollDirection("up");
                }
            } else {
                // Below 250px
                setScrollDirection("down");
            }

            lastScrollY.current = currentScrollY;
        };

        const lastScrollY = { current: window.scrollY };

        // Add scroll event listener
        window.addEventListener("scroll", handleScroll);

        // Cleanup: remove event listener when component unmounts
        return () => {
            window.removeEventListener("scroll", handleScroll);
        };
    }, []);
    useEffect(() => {
        // Close any open modal
        // const bootstrap = require("bootstrap"); // dynamically import bootstrap
        const modalElements = document.querySelectorAll(".modal.show");
        modalElements.forEach((modal) => {
            const modalInstance = bootstrap.Modal.getInstance(modal);
            if (modalInstance) {
                modalInstance.hide();
            }
        });

        // Close any open offcanvas
        const offcanvasElements = document.querySelectorAll(".offcanvas.show");
        offcanvasElements.forEach((offcanvas) => {
            const offcanvasInstance = bootstrap.Offcanvas.getInstance(offcanvas);
            if (offcanvasInstance) {
                offcanvasInstance.hide();
            }
        });
    }, []); // Runs every time the route changes

    useEffect(() => {
        const header = document.querySelector("header");
        if (header) {
            if (scrollDirection == "up") {
                header.style.top = "0px";
            } else {
                header.style.top = "-185px";
            }
        }
    }, [scrollDirection]);
    useEffect(() => {
        // const WOW = require("../utils/wow");
        // const wow = new WOW.default({
        //     mobile: false,
        //     live: false,
        // });
        // wow.init();
    }, []);
    return (
        <Context>{(isLoading) => (
            <>
                <div id="wrapper">{children}</div>
                <CartModal />
                <QuickView />
                {/* <QuickAdd /> */}
                <CompareModal />
                <MobileMenu />

                {/* <NewsLetterModal /> */}
                <SearchModal />
                <AddressModal />
                <AlertModal />
                {/* <SizeGuide /> */}
                {/* <Wishlist /> */}
                {/* <DemoModal /> */}
                {/* <Categories />  */}
                {isLoading && <Loader />}
            </>
        )}</Context>
    );
}
