import React, { StrictMode, Suspense } from 'react'
import { createRoot } from 'react-dom/client'
import { <PERSON>rowserRouter } from "react-router";
import { QueryClient } from '@tanstack/react-query';
import { PersistQueryClientProvider } from '@tanstack/react-query-persist-client'
import { createSyncStoragePersister } from '@tanstack/query-sync-storage-persister'

const App = React.lazy(() => import('./App.tsx'))

const loading = (
  <div className="pt-3 text-center">
    <div className="sk-spinner sk-spinner-pulse"></div>
  </div>
)

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 24 * 60 * 60 * 1000,
      // staleTime: 0,
      gcTime: 24 * 60 * 60 * 1000,
      refetchOnReconnect: false,
      refetchOnWindowFocus: false,
    }
  }
})

const persister = createSyncStoragePersister({
  storage: window.localStorage,
})

createRoot(document.getElementById('root')!).render(
  <PersistQueryClientProvider
    client={queryClient}
    persistOptions={{
      persister, dehydrateOptions: {
        shouldDehydrateQuery: (query) => {
          const queryIsReadyForPersistance = query.state.status === 'success';
          if (queryIsReadyForPersistance) {
            const { queryKey } = query;
            const excludeFromPersisting = queryKey.includes('orders') || queryKey.includes('checkout');
            return !excludeFromPersisting;
          }
          return queryIsReadyForPersistance;
        }
      }
    }}
  >
    <BrowserRouter>
      <StrictMode>
        <Suspense fallback={loading}>
          <App />
        </Suspense>
      </StrictMode>
    </BrowserRouter>
  </PersistQueryClientProvider>
)
