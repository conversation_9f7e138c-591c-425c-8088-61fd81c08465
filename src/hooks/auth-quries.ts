import { authRepository } from "@/repositories/auth/auth-repository";
import { UserResponse } from "@/types/user";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

export const register = async (body: Record<string, any>) => {
    const repo = await authRepository()
    return await repo.register(body)
}
export const useRegister = (onSuccess: (data: UserResponse) => void, onError?: (error: any) => void) => {
    return useMutation({
        mutationFn: register,
        onSuccess,
        onError,
    })
}

const login = async ({ method, body }: { method: string; body: Record<string, any> }) => {
    const repo = await authRepository()
    return await repo.login(method, body)
}

export const useLogin = (onSuccess: (data: UserResponse) => void, onError?: (error: any) => void) => {
    return useMutation({
        mutationFn: login,
        onSuccess,
        onError,
    })
}

export function useGetAddress() {
    return useQuery({
        queryKey: ["address"],
        queryFn: async () => {
            const repo = await authRepository()
            const data = await repo.getAddress()
            return data
        }
    })
}

export const useAddAddress = (onLoading: (isLoading: boolean) => void, onSuccess: () => void) => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: async (data: any) => {

            try {
                onLoading(true);
                const repo = await authRepository();
                if (data.id) {
                    await repo.updateAddress(data);
                } else {
                    await repo.addAddress(data);
                }
            } finally {
                onLoading(false);
            }
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["address"] });
            onSuccess();
        },
    })
}

export const useDeleteAddress = (onLoading: (isLoading: boolean) => void, onSuccess: () => void) => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: async (id: number | string) => {
            try {
                onLoading(true);
                const repo = await authRepository();
                await repo.deleteAddress(id);
            } finally {
                onLoading(false);
            }
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["address"] });
            onSuccess();
        },
    })
}