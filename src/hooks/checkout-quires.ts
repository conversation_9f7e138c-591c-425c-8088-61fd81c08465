import { checkoutRepository } from "@/repositories/checkout/checkout-repository"
import { ProductVariant } from "@/types/product/product"
import { UserAddress } from "@/types/user"
import { keepPreviousData, useInfiniteQuery, useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import { Timestamp } from "firebase/firestore"

export const useCartDetails = (isAuthenticated: boolean, onLoading: (isLoading: boolean) => void) => {
    return useQuery({
        queryKey: ["cart"],
        queryFn: async () => {
            onLoading(true)
            const repo = await checkoutRepository()
            const data = await repo.getCartItems()
            onLoading(false)
            return data
        },
        enabled: isAuthenticated
    })
}

export const useAddToCart = (onLoading: (isLoading: boolean) => void) => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: async ({ product, qty }: { product: string, qty: number }) => {
            try {
                onLoading(true);
                const repo = await checkoutRepository();
                await repo.addToCart(product, qty);
            } finally {
                onLoading(false);
            }
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["cart"] });
        },
    });
};

export const useWishListDetails = (onLoading: (isLoading: boolean) => void) => {
    return useQuery({
        queryKey: ["wishlist"],
        queryFn: async () => {
            onLoading(true)
            const repo = await checkoutRepository()
            const data = await repo.getWishlistItems()
            onLoading(false)
            return data
        },
    })
}

export const useUpdateWishList = (onLoading: (isLoading: boolean) => void) => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: async ({ product, isAdd }: { product: ProductVariant, isAdd: boolean }) => {
            try {
                onLoading(true);
                const repo = await checkoutRepository();
                await repo.updateWishlist(product, isAdd);
            } finally {
                onLoading(false);
            }
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["wishlist"] });
        },
    });
};

export const useCheckoutDetails = (id?: string,) => {
    return useQuery({
        queryKey: ["checkout", id],
        queryFn: async () => {
            const repo = await checkoutRepository()
            const data = await repo.getCartItems(undefined, id)
            return data
        },
    })
}

export const usePlaceOrder = (onLoading: (isLoading: boolean) => void, onSuccess: (data: [string, string]) => void, onError: (error: any) => void) => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: async ({ address, coupon, productId }: { address: UserAddress, coupon?: string, productId?: number | string }) => {
            try {
                onLoading(true);
                const repo = await checkoutRepository();
                const data = await repo.placeOrder(address, coupon, productId);
                return data
            } finally {
                onLoading(false);
            }
        },
        onSuccess: (data) => {
            queryClient.invalidateQueries({ queryKey: ["cart"] });
            onSuccess(data);
        },
        onError: (error) => {
            onError(error)
        }
    });
};

export function useGetOrders(size: number) {
    return useQuery({
        queryKey: ["orders"],
        queryFn: async () => {
            const repo = await checkoutRepository()
            const data = await repo.getOrders(0, size)
            return data
        },
        placeholderData: keepPreviousData,
    })
}

export function useGetInfiniteOrders() {
    const pageSize = 10
    return useInfiniteQuery({
        queryKey: ["orders"],
        queryFn: async (pageParams) => {
            const repo = await checkoutRepository()
            const data = await repo.getOrders(pageParams.pageParam, pageSize)
            return data
        },
        initialPageParam: 0,
        getNextPageParam: (lastPage) => {
            if (lastPage && lastPage.length === pageSize) {
                return (lastPage[lastPage.length - 1].created_at as Timestamp).toMillis()
            }
            return undefined
        },
        gcTime: 0,
        staleTime: 0,
        refetchOnMount: true,
    })
}

export function useGetOrderDetails(id: string) {
    return useQuery({
        queryKey: ["orders", id],
        queryFn: async () => {
            const repo = await checkoutRepository()
            const data = await repo.getOrderDetails(id)
            return data
        }
    })
}