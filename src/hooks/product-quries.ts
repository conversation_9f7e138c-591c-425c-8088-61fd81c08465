import { productRepository } from "@/repositories/product/product-repository"
import { ProductVariant } from "@/types/product/product"
import { keepPreviousData, useQueries, useQuery } from "@tanstack/react-query"

export function useHomePageProduct() {
    return useQuery({
        queryKey: ["home_products"],
        queryFn: async () => {
            const repo = await productRepository()
            return repo.getUniqueProducts(1, 24)
        },
        select: (data) => {
            const list: [ProductVariant[], ProductVariant[], ProductVariant[]] = [[], [], []]
            data.data.forEach((item, index) => {
                list[index % 3].push(item)
            })
            return list
        },
    })
}

export function useShopInstagram() {
    return useQuery({
        queryKey: ["shopInsta"],
        queryFn: async () => {
            const repo = await productRepository()
            return (await repo.getUniqueProducts(2, 10)).data
        }
    })
}

export function useProducts(page: number, categoryId?: string, subCategoryId?: string, filters?: any, sortData?: any) {
    return useQuery({
        queryKey: ["products", categoryId, subCategoryId, page, filters],
        queryFn: async () => {
            const params = new URLSearchParams();

            if (categoryId) {
                params.set("category_id", categoryId);
            }

            if (subCategoryId) {
                params.set("sub_category_id", subCategoryId)
            }

            // Pagination
            params.set("page", page.toString());
            params.set("per_page", "12");

            // Sorting
            if (sortData && sortData !== "newest") {
                const sort = sortData.split("_")
                const sortOrder = sort[sort.length - 1]
                params.set("sort_by", "selling_price")
                params.set("sort_order", sortOrder)
            }

            if (filters.brands.length > 0) {
                params.set("brand_id", filters.brands.map((b: string) => {
                    const brand = b.split("-")
                    return brand[brand.length - 1]
                }))
            }

            if (Object.keys(filters.options).length > 0) {
                const optionsString = Object.entries(filters.options)
                    .map(([key, value]) => `${key}:${(value as any).toString()}`)
                    .join(",");
                params.set("options", optionsString);
            }

            const repo = await productRepository()
            return (await repo.getProducts(params.toString()))
        },
        placeholderData: keepPreviousData
    })
}

export function useFilter(categoryId: string) {
    return useQuery({
        queryKey: ["filters", categoryId],
        queryFn: async () => {
            const repo = await productRepository()
            return repo.getFilters(categoryId)
        }
    })
}

export function useSearchProducts(query: string | undefined, page: number) {
    return useQuery({
        queryKey: ["products", query, page],
        queryFn: async () => {
            const repo = await productRepository()
            if (query && query.length > 0) {
                return await repo.searchProducts(query, page, 12)
            } else {
                const params = new URLSearchParams();
                params.set("page", page.toString());
                params.set("per_page", "12");
                return (await repo.getProducts(params.toString()))
            }
        }
    })
}

export function useProductDetails(slug: string) {
    return useQuery({
        queryKey: ["product-details", slug],
        queryFn: async () => {
            const repo = await productRepository()
            return await repo.getProductDetails(slug)
        }
    })
}

export function useProductDetailsList(ids: string[]) {
    return useQueries({
        queries: ids.map(id => ({
            queryKey: ['product-details', id],
            queryFn: async () => {
                const repo = await productRepository()
                return await repo.getProductDetails(id)
            }
        })),
    })
}