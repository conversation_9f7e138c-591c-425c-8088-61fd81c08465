import { useQueries, useQuery, useQueryClient } from "@tanstack/react-query";
import { ProductVariant } from "@/types/product/product";
import { Collection } from "@/types/product/collection";
import { homeRepository } from "@/repositories/home/<USER>";
import { productRepository } from "@/repositories/product/product-repository";


export function useHomePageData({ homeProductSize }: { homeProductSize: number } = { homeProductSize: 24 }) {
    const result = useQueries({
        queries: [
            {
                queryKey: ["slides"],
                queryFn: async () => {
                    const repo = await homeRepository()
                    return repo.getSlides()
                },
            },
            {
                queryKey: ["subCategories"],
                queryFn: () => [],
                enabled: false,
                select: (data) => (data as Collection[] | undefined) ?? [],
            },
            {
                queryKey: ["home_products"],
                queryFn: async () => {
                    const repo = await productRepository()
                    return await repo.getUniqueProducts(1, homeProductSize)
                },
                select: (data) => {
                    const list: [ProductVariant[], ProductVariant[], ProductVariant[]] = [[], [], []];
                    (data as Pagination<ProductVariant>).data.forEach((item, index) => {
                        list[index % 3].push(item)
                    })
                    return list
                },
            },
            {
                queryKey: ["countdownBanners"],
                queryFn: async () => {
                    const repo = await homeRepository()
                    return await repo.fetchCountdownBanner()
                },
            },
            {
                queryKey: ["testimonials"],
                queryFn: async () => {
                    const repo = await homeRepository()
                    return await repo.getTestimonials()
                },
            },
            {
                queryKey: ["shopInsta"],
                queryFn: async () => {
                    const repo = await productRepository()
                    return (await repo.getUniqueProducts(2, 10)).data
                },
            }
        ]
    })
    return {
        slides: result[0],
        collection: result[1],
        products: result[2],
        countdownBanners: result[3],
        testimonials: result[4],
        shopInsta: result[5],
    }
}

export function useNavData() {
    const queryClient = useQueryClient();

    return useQuery({
        queryKey: ["categories"],
        queryFn: async () => {
            const repo = await homeRepository()
            // await new Promise(resolve => setTimeout(resolve, 20000))
            const data = await repo.getNavData()
            queryClient.setQueryData(["subCategories"], () =>
                data.flatMap(category => category.sub_categories).filter(Boolean)
            );
            return data
        },
    })
}