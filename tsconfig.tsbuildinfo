{"root": ["./src/app.tsx", "./src/main.tsx", "./src/vite-env.d.ts", "./src/components/auth/google-auth.tsx", "./src/components/auth/login.tsx", "./src/components/auth/other-auth.tsx", "./src/components/common/scroll-top.tsx", "./src/components/footers/footer1.tsx", "./src/components/headers/header1.tsx", "./src/components/product-cards/product-card1.tsx", "./src/components/product-details/breadcumb.tsx", "./src/components/product-details/color-select.tsx", "./src/components/product-details/quantity-select.tsx", "./src/components/product-details/releated-product.tsx", "./src/components/product-details/size-select.tsx", "./src/components/product-details/descriptions/descriptions.tsx", "./src/components/product-details/descriptions/review-sorting.tsx", "./src/components/product-details/descriptions/review.tsx", "./src/components/product-details/descriptions/specification.tsx", "./src/components/product-details/details/details1.tsx", "./src/components/product-details/sliders/slider1.tsx", "./src/layout/context.tsx", "./src/layout/layout.tsx", "./src/pages/home.tsx", "./src/pages/auth/login.tsx", "./src/pages/others/page404.tsx", "./src/pages/product/product-details.tsx", "./src/redux/store.ts", "./src/repositories/product/product-fake-repository-impl.ts", "./src/repositories/product/product-repository.ts", "./src/repositories/product/product-respository-impl.ts", "./src/test/products/product-details.ts", "./src/types/product/product.d.ts", "./src/utils/app-config.ts", "./src/utils/footer-links.ts", "./src/utils/product-utils.ts"], "errors": true, "version": "5.6.3"}