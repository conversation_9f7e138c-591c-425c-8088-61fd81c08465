// import { defineConfig, loadEnv } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'
import { defineConfig } from 'vite';
// import obfuscatorPlugin from "vite-plugin-javascript-obfuscator";
// import process from 'process';


export default defineConfig(({ mode }) => {

  console.log(mode);

  // Load environment variables
  // const env = loadEnv(mode, process.cwd(), "");

  return {
    optimizeDeps: {
      include: ['@dotlottie/react-player']
    },
    server: {
      host: true,
      port: 5173,
      hmr: {
        host: '*************',
        protocol: 'ws'
      }
    },
    plugins: [react(),
      // obfuscatorPlugin({
      // options: {
      // compact: true,
      // controlFlowFlattening: true, // Makes control flow harder to understand
      // stringArray: true, // Encodes strings
      // stringArrayEncoding: ["rc4"], // Encrypts string literals
      // selfDefending: true, // Protects against formatting
      // ignoreImports: false,
      // }
      // }),
    ],
    define: {
      "import.meta.env.REPO_PATH":
        process.env.VITE_BASIC_ENV === "dev"
          ? JSON.stringify("./product-fake-repository-impl")
          : JSON.stringify("./product-repository-impl"),
    },
    resolve: {
      alias: {
        "@": path.resolve(__dirname, "./src/"),
        // "@components": `${path.resolve(__dirname, "./src/components/")}`,
        // pages: path.resolve(__dirname, "./src/pages/"),
        // types: `${path.resolve(__dirname, "./src/types/")}`,
        // repositories: `${path.resolve(__dirname, "./src/repositories/")}`,
        // "repositories/product/product-respository": env.VITE_ENV === "dev"
        //   ? path.resolve(__dirname, "src/repositories/product/product-fake-repository-impl.ts")
        //   : path.resolve(__dirname, "src/repositories/product/product-respository-impl.ts"),
      },
      // '@': path.resolve(__dirname, 'src'),
      // // "@/components": path.resolve(__dirname, "src/components"),
      // "@/repositories/product/product-repository": env.VITE_ENV === "dev"
      //   ? path.resolve(__dirname, "src/repositories/product/product-fake-repository-impl.ts")
      //   : path.resolve(__dirname, "src/repositories/product/product-respository-impl.ts"),

    },
    build: {
      emptyOutDir: true,
      minify: "terser",
      // terserOptions: {
      //   compress: {
      //     drop_console: true, // Remove console logs
      //     drop_debugger: true,
      //     passes: 2, // Optimize multiple times

      //   },
      // mangle: {
      //   toplevel: true, // Mangle top-level variable names
      //   properties: true, // Rename object properties
      // },
      // format: {
      //   ascii_only: true, // Avoid Unicode characters
      // },

      // }
    }
  };
})


// https://vite.dev/config/
// export default defineConfig({
//   resolve:{
//     alias:{
//       "@/repositories/ProductRepository": import.meta.env.VITE_ENV === "dev"
//     }
//   },
//   plugins: [react(),

//   ],
// })
