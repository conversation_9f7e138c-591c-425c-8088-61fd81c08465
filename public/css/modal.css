.tf-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    backdrop-filter: blur(2px);
}

.tf-modal {
    position: relative;
    background: white;
    border-radius: 8px;
    padding: 20px;
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    animation: modalFadeIn 0.2s ease-out;
}

.modal-dialog {
    max-width: 700px;
    /* Increased width */
    margin: 1.75rem auto;
}

.modal-content {
    border: none;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.tf-modal-header {
    display: flex;
    align-items: baseline;
    /* Align close button with title */
    justify-content: space-between;
    padding: 1.5rem 1.5rem 1rem;
    border-bottom: 1px solid #eee;
}

.tf-modal-header .btn-close {
    padding: 0;
    margin: 0;
    font-size: 1.25rem;
    opacity: 0.7;
    transition: opacity 0.2s;
    line-height: 1;
}

.tf-modal-header .btn-close:hover {
    opacity: 1;
}

.tf-modal-header .title {
    font-size: 1.5rem;
    margin: 0;
    color: #333;
}

.modal-body {
    padding: 1.5rem;
}

.tf-modal .form-group {
    margin-bottom: 1.25rem;
    /* Default margin for all cases */
    position: relative;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.625rem 0.875rem;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    background-color: #fff;
    font-size: 0.95rem;
    transition: all 0.2s ease;
    box-shadow: none;
    margin-bottom: 0.25rem;
    /* Space for error message */
}

.form-group input:hover,
.form-group select:hover,
.form-group textarea:hover {
    border-color: #b0b0b0;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    border-color: #2196F3;
    box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.1);
    outline: none;
}

.form-group input.error,
.form-group select.error,
.form-group textarea.error {
    border-color: #dc3545;
    border-width: 1px;
    background-color: #fff;
    animation: shake 0.5s cubic-bezier(0.36, 0.07, 0.19, 0.97) both;
}

.form-group .error-text {
    color: #dc3545;
    font-size: 0.75rem;
    margin-top: 0.25rem;
    font-weight: 500;
    display: block;
    animation: fadeIn 0.3s ease-in;
}

.grid-2 {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.tf-modal-footer {
    display: flex;
    justify-content: center;
    /* Center the button */
    padding-top: 1.25rem;
    margin-top: 1.5rem;
    border-top: 1px solid #eee;
}

.tf-modal-footer .tf-btn {
    min-width: 150px;
    padding: 0.625rem 1.5rem;
    font-size: 0.95rem;
    height: 42px;
    border-radius: 6px;
    background-color: var(--main);
    color: white;
    border: none;
    transition: background-color 0.2s ease;
}

.tf-modal-footer .tf-btn:hover {
    background-color: transparent;
    border: 1px solid var(--main);
    color: var(--main);
}

textarea {
    min-height: 100px;
    resize: vertical;
}

@keyframes shake {

    10%,
    90% {
        transform: translateX(-1px);
    }

    20%,
    80% {
        transform: translateX(2px);
    }

    30%,
    50%,
    70% {
        transform: translateX(-4px);
    }

    40%,
    60% {
        transform: translateX(4px);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@media (max-width: 768px) {
    .tf-modal {
        width: 95%;
        padding: 15px;
    }

    .modal-dialog {
        margin: 1rem;
    }

    .tf-modal-header {
        padding: 1rem;
    }

    .modal-body {
        padding: 1rem;
    }

    .grid-2 {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    .form-group {
        margin-bottom: 1rem;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
        padding: 0.625rem 0.875rem;
        font-size: 0.9rem;
    }

    .tf-modal-footer {
        margin-top: 1.5rem;
        padding-top: 1rem;
    }

    .tf-modal-footer .tf-btn {
        width: 100%;
    }

    /* Address Modal Specific Styles */
    #addressModal .modal-dialog {
        margin: 0;
        max-width: 100%;
        min-height: 100vh;
    }

    #addressModal .modal-content {
        border-radius: 0;
        min-height: 100vh;
    }

    #addressModal .tf-modal-header {
        position: sticky;
        top: 0;
        background: white;
        z-index: 1;
        padding: 1rem;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    #addressModal .modal-body {
        padding: 1rem;
        padding-bottom: 80px;
        /* Space for fixed footer */
    }

    #addressModal .tf-modal-footer {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background: white;
        padding: 1rem;
        box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
        margin: 0;
        border-top: 1px solid #eee;
    }

    #addressModal .tf-btn {
        width: 100%;
        height: 48px;
        font-size: 1rem;
    }
}

@media (min-width: 769px) {
    #addressModal .modal-dialog {
        max-width: 600px;
    }

    #addressModal .modal-content {
        border-radius: 12px;
        box-shadow: 0 5px 25px rgba(0, 0, 0, 0.15);
    }

    #addressModal .tf-btn {
        min-width: 120px;
        height: 40px;
    }

    #addressModal .form-group {
        margin-bottom: 1.25rem;
    }

    #addressModal .tf-modal-header {
        padding: 1.5rem 1.5rem 1rem;
    }

    #addressModal .modal-body {
        padding: 0 1.5rem 1.5rem;
    }
}

/* Address Modal Specific Styles */
.tf-modal-address .modal-dialog {
    max-width: 600px;
    padding: 15px;
    gap: 20px;

    .tf-grid-layout {
        padding-right: 10px;
        margin-right: -15px;
    }
}

.tf-modal-address .form-group {
    margin-bottom: 1.25rem;
}

.tf-modal-address .form-group:last-child {
    margin-bottom: 0;
}

.tf-modal-address .input-groups {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.25rem;
}

.tf-modal-address .address-details {
    margin-bottom: 1.5rem;
}

.tf-modal-address .location-group {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.25rem;
    margin-top: 1.25rem;
}

.tf-modal-address .additional-info {
    margin-top: 1.5rem;
}

.tf-modal-address .form-group {
    margin-bottom: 1rem;
}

.tf-modal-address input,
.tf-modal-address select,
.tf-modal-address textarea {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    font-size: 0.95rem;
    transition: all 0.2s ease;
}

.tf-modal-address input:focus,
.tf-modal-address select:focus,
.tf-modal-address textarea:focus {
    border-color: #2196F3;
    box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.1);
    outline: none;
}

.tf-modal-address input.error {
    border-color: #dc3545;
}

.tf-modal-address .error-text {
    color: #dc3545;
    font-size: 0.75rem;
    margin-top: 0.25rem;
}

.tf-modal-address .tf-btn {
    min-width: 180px;
    height: 44px;
    border-radius: 8px;
    background-color: var(--main);
    color: white;
    border: none;
    font-size: 1rem;
    transition: background-color 0.2s ease;
}

.tf-modal-address .tf-btn:hover {
    background-color: transparent;
    border: 1px solid var(--main);
    color: var(--main);
}

.tf-modal-address .tf-modal-footer {
    margin-top: 2rem;
    padding-top: 1.5rem;
}

@media (max-width: 768px) {

    .tf-modal-address .input-groups,
    .tf-modal-address .location-group {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .tf-modal-address .modal-dialog {
        margin: 0;
        max-width: 100%;
        min-height: 100vh;
    }

    .tf-modal-address .modal-content {
        border-radius: 0;
        min-height: 100vh;
    }

    .tf-modal-address .modal-body {
        padding: 1.25rem;
    }

    .tf-modal-address .tf-btn {
        width: 100%;
    }
}

/* Address Type Selector Styles */
.address-type-selector .type-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 8px;
}

.address-type-selector .type-btn {
    display: inline-flex;
    align-items: center;
    padding: 6px 12px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    background: transparent;
    font-size: 13px;
    white-space: nowrap;
    transition: all 0.2s ease;
    min-width: auto;
}

.address-type-selector .type-btn.active {
    background: var(--secondary);
    color: white;
}

.address-type-selector .type-btn i {
    margin-right: 6px;
    font-size: 12px;
}

.address-type-selector .type-btn span {
    line-height: 1;
}

/* Delivery Instructions Styles */
.delivery-instructions-wrapper {
    margin-top: 1rem;
}

.delivery-instructions-wrapper .expand-btn {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background: white;
    cursor: pointer;
    transition: all 0.2s ease;
}

.delivery-instructions-wrapper .expand-btn:hover {
    background: #f5f5f5;
}

.delivery-instructions-wrapper .expand-btn span {
    color: #333;
    font-size: 0.95rem;
}

.delivery-instructions-wrapper .expand-btn i {
    color: #666;
    font-size: 0.875rem;
}

.delivery-instructions {
    margin-top: 1rem;
}

.animate-expand {
    animation: expandView 0.3s ease-out;
}

@keyframes expandView {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@media (max-width: 576px) {
    .address-type-selector .type-buttons {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Expandable Section Styles */
.expandable-section {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
}

.expand-header {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: white;
    border: none;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.expand-header:hover {
    background-color: #f8f9fa;
}

.expand-content {
    text-align: left;
}

.expand-title {
    font-size: small;
    color: #333;
    margin-bottom: 0.25rem;
}

.expand-subtitle {
    font-size: x-small;
    line-height: normal;
    color: #666;
    margin: 0;
}

.expand-header i {
    color: #666;
    font-size: 1rem;
    transition: transform 0.2s ease;
}

.expanded-content {
    padding: 1rem;
    border-top: 1px solid #e0e0e0;
    background: #f8f9fa;
}

.section-label {
    font-size: 0.875rem;
    color: #666;
    margin-bottom: 0.75rem;
}

.address-type-selector .type-buttons {
    display: flex;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
}

.type-btn {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 0.75rem;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background: white;
    color: var(--main);
    transition: all 0.2s ease;
    cursor: pointer;

    &:hover {
        background-color: var(--main);
        color: #ffffff;
        /* Ensure text is white on hover */
        border-color: var(--main);
        /* Match border with background */
    }

    &.active {
        background-color: var(--main);
        color: #ffffff;
        border-color: var(--main);
    }
}

/* Mobile Responsive Adjustments */
@media (max-width: 576px) {
    .address-type-selector .type-buttons {
        flex-wrap: wrap;
    }

    .type-btn {
        flex: 1 1 calc(50% - 0.375rem);
    }
}

.tf-alert-success {
    background: #ffffff;
    border-radius: 8px;
    padding: 1rem;
    max-width: 400px;
    margin: 0 auto;
    position: relative;
    animation: modalFadeIn 0.3s ease-out;
}

.tf-alert-success .success-icon {
    color: #28a745;
    animation: scaleIn 0.3s ease-in-out;
}

.tf-alert-success .alert-title {
    color: #333;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.tf-alert-success .alert-message {
    color: #666;
    font-size: 0.95rem;
    line-height: 1.5;
}

.tf-alert-success .alert-actions {
    display: flex;
    justify-content: center;
    gap: 10px;
    flex-wrap: wrap;
}



@keyframes scaleIn {
    0% {
        transform: scale(0);
        opacity: 0;
    }

    100% {
        transform: scale(1);
        opacity: 1;
    }
}

#lottie-success {
    width: 120px;
    height: 120px;
    margin: 0 auto;
    transform-origin: center center;
    transform: scale(1.2);
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: scale(0.95);
    }

    to {
        opacity: 1;
        transform: scale(1);
    }
}