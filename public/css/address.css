.address-details {
    font-size: 0.9rem;
    line-height: 1.5;
}

.address-details .name-contact {
    margin-bottom: 0.5rem;
}

.address-details .address-lines {
    margin-bottom: 0.5rem;
}

.address-details .badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    background-color: var(--primary);
    border-radius: 4px;
    text-transform: capitalize;
}

.address-details .delivery-instructions {
    font-size: 0.8rem;
    margin-top: 0.5rem;
    color: #666;
}

.address-list-container {
    background-color: #fff;
}

.address-item {
    background-color: #fff;
    transition: all 0.2s ease-in-out;
}

.address-item:hover {
    background-color: #f8f9fa;
}

.address-item label {
    cursor: pointer;
    width: 100%;
    margin: 0;
}

.text-btn {
    color: #007bff;
    cursor: pointer;
    text-decoration: none;
    transition: color 0.2s ease-in-out;
}

.text-btn:hover {
    color: #0056b3;
    text-decoration: underline;
}

.address-actions {
    border-top: 1px solid #eee;
    padding-top: 0.75rem;
}

.address-actions .tf-btn {
    min-width: auto;
    padding: 8px 16px;
    font-size: 0.9rem;
    line-height: 1;
}

.tf-btn-sm {
    min-height: unset;
    font-weight: normal;
}

@media (max-width: 768px) {
    .address-details .name-contact {
        flex-direction: column;
    }

    .address-container {
        padding: 1rem;
    }

    .address-actions {
        padding-top: 0.5rem;
    }

    .address-actions .tf-btn {
        padding: 4px 10px;
        height: 28px;
        font-size: 0.8rem;
    }
}