.address-list-section {
    padding: 40px 0;
    min-height: calc(100vh - 200px);
}

.address-list-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.address-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 24px;
}

.address-card {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
    height: 100%;
    min-height: 280px;
    display: flex;
    flex-direction: column;
}

.address-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.add-address-card {
    border: 2px dashed #ddd;
    background: #fff;
    /* Changed from transparent to white */
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 24px;
    transition: all 0.3s ease;
}

.add-address-card:hover {
    border-color: var(--primary);
    background: #f8f9fa;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.add-address-text {
    color: #2c3e50;
    /* Darker default color */
    transition: color 0.3s ease;
}

.add-address-card:hover .add-address-text {
    color: var(--primary);
}

.add-icon-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--primary-light);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 12px;
}

.address-content {
    padding: 24px;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.address-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;
    padding-bottom: 16px;
    border-bottom: 1px solid #eee;
}

.address-badge {
    background: var(--primary-light);
    color: var(--primary);
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
}

.address-details {
    flex: 1;
    font-size: 14px;
    color: #555;
    line-height: 1.6;
}

.address-footer {
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.action-buttons {
    display: flex;
    gap: 16px;
}

.btn-action {
    background: none;
    border: none;
    padding: 0;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 4px;
}

.btn-edit {
    color: var(--primary);
}

.btn-remove {
    color: #dc3545;
}

.btn-default {
    color: #666;
}

.empty-address-state {
    text-align: center;
    padding: 2rem 1rem;
}

.empty-icon {
    width: 32px;
    height: 32px;
    color: #9ca3af;
    margin-bottom: 1rem;
}

.empty-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #111827;
    margin-bottom: 0.5rem;
}

.empty-address-state p {
    font-size: 0.875rem;
    color: #6b7280;
    margin-bottom: 1.5rem;
}

.empty-action {
    display: flex;
    justify-content: center;
}

.add-address-btn {
    font-size: 0.875rem;
    padding: 0.625rem 1.25rem;
    color: var(--white);
    background: var(--main);
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s;
}

.add-address-btn:hover {
    background: var(--primary);
    color: white;
}

@media (max-width: 768px) {
    .address-list-section {
        padding: 20px 0;
    }

    .address-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .address-content {
        padding: 16px;
    }

    .add-address-card {
        min-height: unset;
        padding: 12px;
        flex-direction: row;
        gap: 12px;
        background: #fff;
        border: 1px solid #e5e7eb;
        border-radius: 8px;
    }

    .add-icon-circle {
        width: 32px;
        height: 32px;
        margin: 0;
    }

    .add-address-card>div {
        display: flex;
        align-items: center;
        gap: 12px;
    }

    .add-address-text {
        margin: 0;
        font-size: 14px;
    }

    .empty-address-state {
        margin: 1rem;
        padding: 1.5rem;
    }
}