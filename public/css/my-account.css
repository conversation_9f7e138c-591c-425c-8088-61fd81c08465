.my-account-section {
    padding: 40px 0;
}

.user-profile-card {
    background: var(--white);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: var(--shadow1);
}

.profile-cover {
    height: 80px;
    background: var(--gradient);
}

.user-info {
    padding: 20px;
    text-align: center;
    position: relative;
}

.user-avatar {
    width: 90px;
    height: 90px;
    margin: -65px auto 15px;
    position: relative;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 4px solid var(--white);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

.user-avatar .initials {
    font-size: 2rem;
    font-weight: 600;
    color: var(--white);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.user-name {
    font-size: 1.4rem;
    font-weight: 600;
    color: var(--main);
    margin: 0 0 5px;
}

.user-email {
    font-size: 0.9rem;
    color: var(--secondary);
    margin-bottom: 25px;
}

.user-stats {
    display: flex;
    justify-content: center;
    gap: 30px;
    padding: 20px 0;
    margin-top: 20px;
    border-top: 1px solid var(--line);
    background: var(--surface);
    border-radius: 12px;
}

.stat-item {
    text-align: center;
}

.stat-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--white);
    border-radius: 50%;
    margin: 0 auto 10px;
    box-shadow: 0 2px 8px rgba(228, 49, 49, 0.15);
}

.stat-icon svg {
    color: var(--primary);
    width: 20px;
    height: 20px;
}

.stat-value {
    display: block;
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--main);
    margin-bottom: 2px;
}

.stat-label {
    font-size: 0.8rem;
    color: var(--secondary-2);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.menu-card {
    display: block;
    text-decoration: none;
    background: var(--white);
    border: 1px solid var(--line);
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.25s ease;
    position: relative;
    height: 100%;
}

.menu-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: var(--gradient);
    opacity: 0;
    transition: opacity 0.25s ease;
}

.menu-card .card-body {
    padding: 1rem;
    text-align: center;
}

.menu-card .icon-wrapper {
    position: relative;
    display: inline-block;
    margin-bottom: 0.75rem;
}

.menu-card .icon-wrapper i,
.menu-card .icon-wrapper svg {
    font-size: 1.1rem;
    width: 42px;
    height: 42px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--surface);
    color: var(--primary);
    border-radius: 8px;
    margin: 0 auto;
    transition: all 0.25s ease;
}

.menu-card .icon-wrapper svg {
    padding: 10px;
}

.menu-card h3 {
    font-size: 0.8rem;
    font-weight: 600;
    color: var(--main);
    margin-bottom: 0.25rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.menu-card p {
    font-size: 0.7rem;
    color: var(--secondary-2);
    margin: 0;
    line-height: 1.2;
}

.menu-card:hover {
    transform: translateY(-3px);
    border-color: var(--primary);
    box-shadow: var(--shadow1);
}

.menu-card:hover::before {
    opacity: 1;
}

.menu-card:hover .icon-wrapper i,
.menu-card:hover .icon-wrapper svg {
    background: var(--primary);
    color: var(--white);
    stroke: var(--white);
}

.menu-card:hover h3 {
    color: var(--primary);
}

.account-summary .summary-card {
    background: var(--white);
    border: 1px solid var(--line);
    border-radius: 12px;
    padding: 20px;
}

.account-summary h4 {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--main);
    margin-bottom: 15px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.summary-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 0;
    border-bottom: 1px solid var(--line);
}

.summary-item:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.summary-item .text {
    font-size: 0.85rem;
    color: var(--secondary);
}

.summary-item .badge {
    font-size: 0.7rem;
    padding: 4px 8px;
    border-radius: 4px;
    font-weight: 500;
}

.summary-item .badge.processing {
    background: var(--warning);
    color: var(--white);
}

.summary-item .badge.delivered {
    background: var(--success);
    color: var(--white);
}

.summary-item .type {
    font-size: 0.7rem;
    color: var(--secondary-2);
    padding: 4px 8px;
    border-radius: 4px;
    background: var(--surface);
}

.summary-item .type.default {
    color: var(--primary);
    background: var(--rgba-primary);
}

.empty-state {
    text-align: center;
    padding: 30px 20px;
}

.empty-icon {
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--surface);
    border-radius: 50%;
    margin: 0 auto 15px;
    color: var(--secondary-2);
}

.empty-state p {
    font-size: 0.9rem;
    color: var(--secondary);
    margin-bottom: 15px;
}

.empty-state .btn-outline {
    display: inline-block;
    padding: 8px 16px;
    font-size: 0.8rem;
    color: var(--primary);
    border: 1px solid var(--primary);
    border-radius: 6px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.empty-state .btn-outline:hover {
    background: var(--primary);
    color: var(--white);
}

/* Profile Details Styles */
.profile-details-section {
    padding: 40px 0;
}

.profile-card {
    background: var(--white);
    border-radius: 15px;
    padding: 30px;
    /* box-shadow: var(--shadow1); */
}

.profile-header {
    margin-bottom: 30px;
    border-bottom: 1px solid var(--line);
    padding-bottom: 15px;
}

.profile-header h4 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--main);
    margin: 0;
}

.profile-info {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.profile-info-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 24px;
}

.info-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.info-label {
    font-size: 0.85rem;
    color: var(--secondary-2);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.info-value {
    font-size: 1rem;
    color: var(--main);
    font-weight: 500;
}

.email-container {
    display: flex;
    align-items: center;
    gap: 12px;
}

.verified-badge,
.unverified-badge,
.status-badge {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
}

.verified-badge {
    background-color: var(--rgba-primary);
    color: var(--primary);
}

.verified-badge svg {
    stroke: var(--primary);
}

.unverified-badge {
    background-color: #fff3e0;
    color: #ef6c00;
}

.status-badge {
    width: fit-content;
}

.status-badge.active {
    background-color: #e8f5e9;
    color: #2e7d32;
}

.status-badge.inactive {
    background-color: #ffebee;
    color: #c62828;
}

@media (max-width: 768px) {
    .profile-details-section {
        padding: 20px 0;
    }

    .profile-card {
        padding: 20px;
        border-radius: 12px;
    }

    .profile-header {
        margin-bottom: 24px;
        padding-bottom: 12px;
    }

    .profile-header h4 {
        font-size: 1.1rem;
    }

    .profile-info {
        gap: 20px;
    }

    .profile-info-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .info-group {
        gap: 4px;
    }

    .info-label {
        font-size: 0.75rem;
    }

    .info-value {
        font-size: 0.9rem;
    }

    .email-container {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
}