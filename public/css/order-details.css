.order-details {
    padding: 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

/* Replace .order-header with .order-meta-info */
.order-meta-info {
    margin-bottom: 2rem;
}

.order-details-meta {
    display: flex;
    align-items: center;
    gap: 1rem;
    color: #6c757d;
    border: none;

}

.order-details-meta span {
    color: #666;
    font-size: 12px;
    /* margin-bottom: 4px; */
}

.meta-divider {
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background: #6c757d;
}

.delivery-status {
    color: #2c3e50;
    margin-top: 1rem;
}

.delivery-info {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #dee2e6;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    padding: 1.5rem;
    background: #fff;
    border: 1px solid #dee2e6;
    border-radius: 0.5rem;
    margin-bottom: 2rem;
}

.info-section h6 {
    color: #6c757d;
    margin-bottom: 1rem;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.refund-status {
    color: #2e7d32;
    font-weight: 500;
    margin-top: 0.5rem;
}

/* Update order status styles to be more subtle */
.order-status {
    padding: 0.4rem 0.8rem;
    border-radius: 0.25rem;
    font-weight: 500;
    font-size: 0.9rem;
}

.order-status.completed {
    background-color: #e8f5e9;
    color: #2e7d32;
}

.order-status.processing {
    background-color: #fff3e0;
    color: #ef6c00;
}

.order-status.shipped {
    background-color: #e3f2fd;
    color: #1565c0;
}

.order-status.cancelled {
    background-color: #ffebee;
    color: #c62828;
}

.order-timeline {
    margin-top: 1rem;
    background: transparent;
    padding: 0;
}

.timeline-item {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
    position: relative;
    margin-left: 1rem;
}

.timeline-item:not(:last-child)::before {
    content: '';
    position: absolute;
    left: 0.35rem;
    top: 1.5rem;
    bottom: -1rem;
    width: 2px;
    background: #dee2e6;
}

.timeline-dot {
    width: 0.75rem;
    height: 0.75rem;
    border-radius: 50%;
    background: var(--bs-primary);
    margin-top: 0.5rem;
}

.timeline-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease-out;
}

.timeline-content.show {
    max-height: 1000px;
    /* Adjust based on your needs */
}

.order-items {
    margin: 2rem 0;
    border: 1px solid #dee2e6;
    border-radius: 0.5rem;
}

.order-item {
    display: flex;
    gap: 1rem;
    padding: 1rem;
    border: 1px solid #dee2e6;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
}

.item-image {
    width: 100px;
    height: 100px;
    object-fit: cover;
    border-radius: 0.25rem;
}

.order-summary-card {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 0.5rem;
}

@media (max-width: 768px) {
    .order-details {
        padding: 1rem;
        display: flex;
        flex-direction: column;
    }

    .order-meta-info {
        order: 1;
        margin-bottom: 0rem;
    }

    .order-items {
        order: 2;
        margin-bottom: 2rem;
    }

    .info-grid {
        order: 3;
        display: block;
        padding: 0;
        background: transparent;
        border: none;
        margin-bottom: 0;
    }

    .info-section {
        background: #fff;
        border: 1px solid #dee2e6;
        border-radius: 0.5rem;
        padding: 0.5rem;
        margin-bottom: 1rem;
        font-size: 0.75rem;
        line-height: normal;
    }

    .info-section h6 {
        font-size: 0.75rem;
        margin-bottom: 0rem;
    }

    .info-section .refund-status {
        font-size: 0.875rem;
    }

    .order-item {
        flex-direction: column;
    }

    .item-image {
        width: 100%;
        height: 200px;
    }

    .order-details-meta {
        flex-direction: column;
        width: 100%;
        gap: 0rem;
        margin-bottom: 0rem;
        padding: unset;
    }

    .order-details-meta span:not(.meta-divider) {
        width: 100%;
        display: flex;
        justify-content: space-between;
    }

    .meta-divider {
        display: none;
    }

    .order-details-meta {
        flex-wrap: wrap;
        margin-bottom: 1rem;
    }

    .delivery-status {
        font-size: 0.75rem;
    }

    .delivery-status .d-flex {
        flex-direction: row !important;
        /* Force row direction */
        align-items: center;
        gap: 0.75rem;
        flex-wrap: wrap;
        /* Allow wrapping if needed */
    }

    .order-status {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
        white-space: nowrap;
        /* Prevent status from wrapping */
    }

    .timeline-item {
        font-size: 0.75rem;
    }
}