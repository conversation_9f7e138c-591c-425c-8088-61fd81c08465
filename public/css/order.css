.orders-container {
    max-width: 900px;
    margin: 0 auto;
}

.order-card {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.order-meta {
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    text-decoration: none;
    /* Add this */
}

.order-info {
    display: flex;
    gap: 40px;
}

.order-date,
.order-total,
.ship-to,
.order-number {
    display: flex;
    flex-direction: column;
}

.order-meta span {
    color: #666;
    font-size: 12px;
}

.order-meta strong {
    color: #333;
    font-size: 13px;
}

.order-number {
    text-align: right;
}

.order-number a {
    color: var(--primary-color);
    font-size: 13px;
    text-decoration: none;
}

.order-product {
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    border-bottom: 1px solid #eee;
}

.product-info {
    display: flex;
    gap: 20px;
    flex: 1;
}

.product-info img {
    border-radius: 4px;
    object-fit: cover;
}

.product-details {
    display: flex;
    flex-direction: column;
    gap: 8px;
    max-width: 400px;
}

.product-name {
    color: #333;
    font-weight: 500;
    text-decoration: none;
    font-size: 18px;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.product-name:hover {
    color: var(--primary-color);
}

.price {
    font-size: 16px;
    font-weight: 600;
    color: #2c3e50;
}

/* Button styles */
.tf-button-alt {
    background: transparent;
    border: 1px solid transparent;
    color: var(--main);
    padding: 4px 20px;
    border-radius: 24px;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    margin-top: 10px;
    font-size: 14px;
    transform: all 0.3s ease;

    &:hover {
        border: 1px solid var(--main);
    }
}

.write-review {
    padding: 4px 16px;
    border: 1px solid #666;
    background: transparent;
    color: var(--main);
    border-radius: 24px;
    font-size: 12px;
    transition: all 0.3s ease;
}

.write-review:hover {
    background: var(--main);
    color: var(--white);
}

.order-delivery {
    padding: 15px 20px;
    display: flex;
    align-items: center;
    gap: 15px;
}

.status-badge {
    display: inline-block;
    padding: 5px 12px;
    border-radius: 15px;
    font-size: 13px;
    font-weight: 500;
}

.status-badge.completed {
    background: #d4edda;
    color: #155724;
}

.status-badge.pending {
    background: #fff3cd;
    color: #856404;
}

.status-badge.cancelled {
    background: #f8d7da;
    color: #721c24;
}

.delivery-date {
    color: #666;
    font-size: 14px;
}

.mobile-arrow {
    display: none;
}

@media (max-width: 768px) {

    .order-meta {
        position: relative;
        padding: 12px 15px;
        flex-direction: column;
        gap: 15px;
        padding-right: 25px;
        /* Make room for arrow */
    }

    .mobile-arrow {
        display: block;
        position: absolute;
        right: 15px;
        top: 50%;
        transform: translateY(-50%);
        color: #666;
    }

    .mobile-arrow i {
        font-size: 14px;
    }

    /* Hide most meta info on mobile */
    .order-total,
    .ship-to,
    .order-number {
        display: none;
    }

    .order-meta:hover {
        background-color: #f8f9fa;
    }

    .order-info {
        width: 100%;
        flex-direction: column;
        gap: 15px;
    }

    .order-date {
        width: 100%;
    }

    .order-number {
        text-align: left;
        margin-top: 10px;
        display: none !important;
        /* Force hide */
    }

    .order-product {
        padding: 15px;
        flex-direction: column;
        align-items: flex-start;
    }

    .product-info {
        display: flex;
        gap: 12px;
        width: 100%;
    }

    .product-info img {
        width: 70px;
        height: 90px;
    }

    .product-details {
        flex: 1;
        gap: 4px;
    }

    .product-name {
        font-size: 14px;
        -webkit-line-clamp: 3;
        line-clamp: 3;
        margin-bottom: 2px;
        line-height: 1.3;
    }

    .price {
        font-size: 14px;
        margin: 0;
        color: var(--primary-color);
    }

    .product-actions {
        display: flex;
        padding-left: 82px;
        gap: 12px;
        margin-top: 4px;
        align-items: center;
    }

    .tf-button-alt,
    .write-review {
        padding: 0;
        border: none;
        background: none;
        font-size: 13px;
        color: var(--primary-color);
        margin: 0;
        font-weight: 500;
        line-height: 1;

    }

    .write-review {
        color: #666;
    }

    .tf-button-alt:hover,
    .write-review:hover {
        border: none;
        background: none;
        color: var(--primary-color);
        text-decoration: underline;
    }

    /* Minimize delivery status */
    .order-delivery {
        padding: 8px 15px;
    }

    .delivery-date {
        display: none;
    }

    .status-badge {
        font-size: 12px;
        padding: 4px 10px;
    }
}