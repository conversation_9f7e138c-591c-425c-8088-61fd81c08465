.account-dropdown {
    min-width: 220px;
    padding: 8px 0;
}

.account-menu-items {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.account-menu-item {
    display: flex;
    align-items: center;
    padding: 10px 12px;
    color: var(--main);
    font-size: 14px;
    border-radius: 6px;
    transition: background-color 0.2s ease;
}

.account-menu-item:hover {
    background-color: var(--rgba-primary);
    color: var(--primary);
}

.account-menu-item i,
.account-menu-item svg {
    width: 16px;
    height: 16px;
    margin-right: 12px;
}

.account-menu-item.text-danger {
    color: var(--critical);
}

.account-menu-item.text-danger:hover {
    background-color: rgba(240, 62, 62, 0.1);
}

.account-menu-divider {
    height: 1px;
    background-color: var(--line);
    margin: 8px 0;
}

.account-menu-icon {
    width: 16px;
    height: 16px;
    margin-right: 10px;
    color: inherit;
}

.dropdown-account.show .account-dropdown {
    animation: fadeInDown 0.3s ease;
}