# E-Commerce App

A flexible and modular e-commerce web application built using **Vite**, **TypeScript**, and **React.js**. The app is designed to support multiple types of e-commerce businesses from a single codebase.

## 🔧 Tech Stack
- Vite
- TypeScript
- React.js
- Modular and scalable component architecture

## 💡 Purpose
To provide a reusable e-commerce application that can be customized to suit different business types with minimal changes.

## 🛍️ Supported Store Types
1. **Fashion** – Includes features like filters by size, color, gender, etc.
2. **Electronics** – Tailored product specs, warranty info, brand filtering, etc.
3. **Groceries** – (Coming soon)

## ⚙️ Environment Setup
Before running or building the project, you must generate environment, SCSS, and other configurations from a JSON file.

### 📄 Sample Config Files
Sample config JSON files are available at:
```
config/electronics.json  
config/fashion.json
```

### 📦 Generate Configs
Run the following command to generate the necessary environment and configuration files:
```bash
node generate-env.cjs ./config/config.json
```

## 🚀 Getting Started
```bash

# Install dependencies
npm install

# Run the development server
npm run dev

# Or build the project
npm run build

# Run the development server for different stores:
# Fashion
npm run dev_fashion
# Electronics
npm run dev_electronics
# Grocery (when ready)
npm run dev_grocery

# Build for different stores:
# Fashion
npm run build_fashion
# Electronics
npm run build_electronics
# Grocery (when ready)
npm run build_grocery
```

## 📁 Folder Structure
```
public/
├── css/               # Global styles and generated CSS
├── images/            # Static images and logos

src/
├── assets/            # Static assets like fonts and icons
├── components/        # Reusable UI components
├── hooks/             # Custom React hooks
├── layout/            # Layout components (header, footer, etc.)
├── pages/             # Route-level components and views
├── repositories/      # API abstraction and service layers
├── test/              # Unit and integration tests
├── types/             # TypeScript types and interfaces
├── utils/             # Utility/helper functions
```

## 🔧 Configuration
Easily switch between store types (Fashion, Electronics, etc.) using configuration files or environment variables.

