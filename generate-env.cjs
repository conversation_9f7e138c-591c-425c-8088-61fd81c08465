const fs = require("fs");
const path = require("path");

const jsonPath = process.argv[2];

if (!jsonPath) {
    console.error('❌ Please provide the path to the JSON file.');
    console.error('Usage: node generate-env.js path/to/config.json');
    process.exit(1);
}

// Resolve and read the JSON file
const absolutePath = path.resolve(jsonPath);
if (!fs.existsSync(absolutePath)) {
    console.error(`❌ JSON file not found at: ${absolutePath}`);
    process.exit(1);
}

const outputFile = path.join(__dirname, ".env.local");

const prefix = "VITE_";

// Helper to flatten nested objects
function flattenObject(obj, parentKey = "") {
    return Object.entries(obj).filter(([key]) => key != "theme").reduce((acc, [key, value]) => {
        const fullKey = parentKey ? `${parentKey}_${key}` : key;
        if (typeof value === "object" && value !== null) {
            Object.assign(acc, flattenObject(value, fullKey));
        } else {
            acc[fullKey.toUpperCase()] = value;
        }
        return acc;
    }, {});
}

// utils to format var names
const toCssVar = (key) => `--${key.replace(/_/g, '-').toLowerCase()}`;

function buildRootCssVars(obj, parent = '') {
    let css = '';
    for (const key in obj) {
        const val = obj[key];
        const newKey = parent ? `${parent}-${key}` : key;
        if (typeof val === 'object') {
            css += buildRootCssVars(val, newKey);
        } else {
            css += `  ${toCssVar(newKey)}: ${val};\n`;
        }
    }
    return css;
}

// Load, flatten and write
const raw = fs.readFileSync(absolutePath, "utf-8");
const json = JSON.parse(raw);
const flat = flattenObject(json);

const envLines = Object.entries(flat)
    .map(([key, value]) => value === "#" ? `${prefix}${key}="${value}"` : `${prefix}${key}=${value}`)
    .join("\n");

fs.writeFileSync(outputFile, envLines);

console.log(`✅ Generated .env.local with ${Object.keys(flat).length} keys`);

const cssVars = `:root {\n${buildRootCssVars(json.theme)}\n}`;
fs.writeFileSync(path.join(__dirname, 'src/assets/scss/abstracts/_color-variable.scss'), cssVars);
console.log(`✅ variables.scss generated with ${Object.keys(json.theme).length} keys`);
